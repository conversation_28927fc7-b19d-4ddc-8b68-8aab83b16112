# Fintary API

## Summary
Fintary API uses
- [Next.js](https://nextjs.org/) API routes
  - Currently not using Next.js for React or SSR, though we could in the future.
- [Prisma](https://www.prisma.io/) for ORM
- [Google Cloud Run](https://cloud.google.com/run) for serving API requests
- [Google Cloud SQL](https://cloud.google.com/sql) Postgres database

### Pre-requisites

#### Install Visual Studio Code (Recommended).
https://code.visualstudio.com/download

Mac: `brew install --cask visual-studio-code`

Ubuntu: `sudo snap install --classic code`

#### Install NodeJS 20 LTS.
Use [nvm](https://github.com/nvm-sh/nvm) if you need multiple versions

Mac: `brew install node`

Ubuntu:
- Check if node is installed with snap: % `sudo snap list | grep node` (if it is not installed, should return nothing). If it is installed, remove it: % `sudo snap remove node` (the snap version doesn't install the dependencies correctly)
- Check if it is installed and the versions available with apt package manager: % `sudo apt-cache policy nodejs`
- If the version installed is the 18 LTS you are ready, else remove nodejs and npm: `sudo apt purge nodejs npm`.
- Enter the following command which will download a sh script to add the PPA sources for NodeJS 18 % `curl -s https://deb.nodesource.com/setup_18.x -o nodesource_setup.sh`
- Make the script file executable % `chmod +x nodesource_setup.sh`
- Run the script % `sudo ./nodesource_setup.sh`
- Update the repositories and install nodejs and suggested packages (this will install npm too) % `sudo apt update && sudo apt install --install-suggests nodejs`

To check your node version (should be 20) run `node -v`

#### Install Google Cloud CLI (gcloud).
https://cloud.google.com/sdk/docs/install

Mac: `brew install --cask google-cloud-sdk`

Ubuntu:
- Download install: `curl -O https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-cli-457.0.0-linux-x86_64.tar.gz`
- Extract the contents: `tar -xf google-cloud-cli-457.0.0-linux-x86_64.tar.gz`
- Execute install script: `./google-cloud-sdk/install.sh`
- Close/reopen terminal for environment variables to take effect.


#### Log into gcloud (for you using command to interact with gcloud)
`gcloud auth login`

### Grant credentails to application (for application to interact with glcoud)
`gcloud auth application-default login`

### Optional - configure autocomplete

To enable autocomplete for the `gcloud` CLI, run the following commands in your terminal to append the necessary lines to your `.zshrc`:

```sh
echo 'source "$(gcloud info --format=\'value(installation.sdk_root)\')/path.bash.inc"' >> ~/.zshrc
echo 'source "$(gcloud info --format=\'value(installation.sdk_root)\')/completion.bash.inc"' >> ~/.zshrc
```

Then, reload your shell configuration:

```sh
source ~/.zshrc
```

## Database
We use a PostgreSQL instance on Google Cloud SQL. To connect from your local machine, use Cloud SQL Auth Proxy.  
- Run the proxy: 
`misc/db/cloud-sql-proxy --port 5433 fintary-dev:us-central1:fintary-dev`  
You can use pm2 to keep them running in the background.  
  - `npm i pm2@latest -g`
  - `pm2 --name db-prod start misc/db/cloud-sql-proxy -- --port 5432 fintary-prod:us-central1:fintary-db-prod` 
  - `pm2 --name db-dev start misc/db/cloud-sql-proxy -- --port 5433 fintary-dev:us-central1:fintary-dev`
  - After reboots, use `pm2 resurrect` to resurrect them.
- Cloud SQL Proxy is available in our repo for scripted running, but if you prefer to install separately: 
  - [Install Cloud SQL Proxy](https://cloud.google.com/sql/docs/postgres/connect-instance-auth-proxy#install-proxy)

## Development
Download the .env files (shared in Google Drive) and check that the file is correctly named (.env.prod or .env.dev). Copy the .env files to their respective /web and /api directories.
By default, .env files are configured to run the API on port 3000 and the FE on port 3001.

In the /api directory:
- `npx prisma generate` # Generates new client to connect to db  
- `npm i` # Install deps  

Mac
- `npm run dev` # Starts local server at http://localhost:3000 using dev env.  
- `npm run dev:debug` # Starts local server at http://localhost:3000 using dev debug mode. 
<details><summary>Running debug: </summary>

https://github.com/user-attachments/assets/32c624ef-bbd6-40c4-8e93-22df690bb10f

</details>
<br>

- `npm run prod` # Starts local server at http://localhost:3000 using production env.  

Windows
- `npm run dev-win ` # Starts local server at http://localhost:3000 using dev env.
- `npm run prod-win ` # Starts local server at http://localhost:3000 using production env.

### Notes
Important files are .env, .firebase-fintary-prod.json, next.config.js, prisma/schema.prisma, Dockerfile.  

### Docker container
`docker build -t nextjs-docker .` # Builds Docker container  
`docker run -p 3000:3000 nextjs-docker` # Serves Docker container locally at http://localhost:3000

## Deployment
### Environments
[main](https://github.com/Fintary/fintary/tree/main) branch deploys to dev.fintary.com  
[preview](https://github.com/Fintary/fintary/tree/preview) branch deploys to preview.fintary.com  
[prod](https://github.com/Fintary/fintary/tree/prod) branch deploys to app.fintary.com  

### Manual deployment
Web: `npm run deploy-dev` # From /web

API: `npm run deploy-dev` # From /

We package our API as a Docker container and deploy it to Google Cloud Run.  
`% gcloud builds submit --tag gcr.io/fintary-dev/api --project fintary-dev`

## Firebase Emulator Development

To run the API against the local Firebase Emulator Suite, you can use the `local:emulated` script. This is the recommended approach for features involving Firebase Authentication or Cloud Functions.

First, ensure the emulators are running by using the `npm run local:emulate` command from the project root.

Then, from the `/api` directory, you can run the API independently with:

```sh
npm run local:emulated
```

This script uses the `.env.local` file, which sets `USE_FIREBASE_EMULATOR=true` and configures the following environment variables to connect to the local emulators:

-   `FIREBASE_AUTH_EMULATOR_HOST="127.0.0.1:4002"`
-   `FIRESTORE_EMULATOR_HOST="127.0.0.1:4003"`
-   `STORAGE_EMULATOR_HOST="127.0.0.1:4004"`

The Admin SDK in `lib/firebase-admin.ts` automatically detects these variables and connects to the emulators instead of the live Firebase services.
