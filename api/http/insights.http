###
# @name Widget preview (custom python code)
# @import ./auth.http
# @ref auth
POST {{baseUrl}}/api/insights/preview
Content-Type: application/json
Accept: */*
Accept-Language: en-US,en;q=0.9
Origin: http://localhost:3001
Referer: http://localhost:3001/
accountid: {{accountId}}
authentication: Bearer {{idToken}}
feversion: 2025-08-15T23:23:33.805Z
trace-id: Shv7OwF4uA22CiiRDLXLD
userrole: 1
x-timezone: America/Los_Angeles

{
  "agent": [],
  "end_date": "2025-08-15",
  "start_date": "2025-06-16",
  "widgetDefinition": {
    "name": "test",
    "access": "global",
    "dataSource": "commissions",
    "filterByDate": "effective_date",
    "type": "box",
    "groupBy": "",
    "similarityGroupBy": false,
    "filters": [],
    "timePeriod": "month",
    "column": "",
    "sortingField": null,
    "columnLimit": null,
    "aggregationSelectors": [
      {
        "field": "any",
        "aggregation_method": "Count",
        "formatter": "number"
      }
    ],
    "dataFieldsExpression": null,
    "customRender": {
      "enabled": true,
      "language": "python",
      "code": "import pandas as pd\n\ndef main(dataSource):\n    return 42"
    }
  },
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.test("Given valid python widget, when previewing, should return data", function() {
    client.assert(response.status === 200, "Response status is not 200");
  })
%}

###
# @name Widget preview (custom javascript code)
# @import ./auth.http
# @ref auth
POST {{baseUrl}}/api/insights/preview
Content-Type: application/json
Accept: */*
Accept-Language: en-US,en;q=0.9
Origin: http://localhost:3001
Referer: http://localhost:3001/
accountid: {{accountId}}
authentication: Bearer {{idToken}}
feversion: 2025-08-15T23:23:33.805Z
trace-id: Shv7OwF4uA22CiiRDLXLD
userrole: 1
x-timezone: America/Los_Angeles

{
  "agent": [],
  "end_date": "2025-08-15",
  "start_date": "2025-06-16",
  "widgetDefinition": {
    "name": "test",
    "access": "global",
    "dataSource": "commissions",
    "filterByDate": "effective_date",
    "type": "box",
    "groupBy": "",
    "similarityGroupBy": false,
    "filters": [],
    "timePeriod": "month",
    "column": "",
    "sortingField": null,
    "columnLimit": null,
    "aggregationSelectors": [
      {
        "field": "any",
        "aggregation_method": "Count",
        "formatter": "number"
      }
    ],
    "dataFieldsExpression": null,
    "customRender": {
      "enabled": true,
      "language": "javascript",
      "code": "(dataSource) => {\n  return 42;\n}"
    }
  },
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.test("Given valid javascript widget, when previewing, should return data", function() {
    client.assert(response.status === 200, "Response status is not 200");
  })
%}