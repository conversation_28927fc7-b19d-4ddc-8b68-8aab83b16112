import { CompProfilesBulkOperationsService } from './services/comp-profiles/CompProfilesBulkOperationsService';
import { PrismaClient } from '@prisma/client';
import { Container, type interfaces } from 'inversify';
import type { LRUCache } from 'lru-cache';
import 'reflect-metadata';

import { REPOSITORY_TYPES, SERVICE_TYPES } from '@/constants';
import prisma from '@/lib/prisma';
import { AccountSettingsValidator } from '@/pages/api/accounts/settings/validator';
import { DataUpdateValidator } from '@/pages/api/admin/data-update/validator';
import { CompProfileMatchingService } from '@/pages/api/admin/tools/comp-profile-matcher/CompProfileMatchingService';
import { CompGridValidator } from '@/pages/api/comp-grids/validator';
import { ContactsValidator } from '@/pages/api/contacts/validator';
import { CommissionCalculator } from '@/pages/api/data_processing/commissions/commissionCalculator';
import { CommissionProcessor } from '@/pages/api/data_processing/commissions/commissionProcessor';
import { CommissionUtils } from '@/pages/api/data_processing/commissions/commissionUtils';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import { UpdatePolicyPayoutRatesValidator } from '@/pages/api/report_data/validator';
import { SavedReportsGroupsValidator } from '@/pages/api/saved_reports/groups/validator';
import { UsersValidator } from '@/pages/api/users/validator';
import { AccountService } from '@/services/account';
import { AccountProcessorConfigService } from '@/services/account-processor-config';
import { AccountConfigService } from '@/services/account/config';
import { AccountMetricsService } from '@/services/metrics';
import { AccountingTransactionsService } from '@/services/accounting-transactions';
import { AdobePDFExtractService } from '@/services/adobe-pdf-extract';
import { AgencyIntegratorService } from '@/services/agencyIntegrator';
import { APIKeyService } from '@/services/api_key/service';
import { AwsStorageService } from '@/services/aws-storage';
import { BenefitPointService } from '@/services/benefitPoint';
import { LRUCacheService } from '@/services/cache/lru';
import { CalculationService } from '@/services/calculation';
import { CloudTaskService } from '@/services/cloud-task';
import { CommissionService } from '@/services/commission';
import { AdvancedCommissionService } from '@/services/advance-commission-schedule';
import { CompGridLevelsService } from '@/services/comp-grids/levels';
import { CompGridRatesService } from '@/services/comp-grids/rates';
import { CompReportsService } from '@/services/comp-reports';
import { CompaniesService } from '@/services/companies';
import { CompanyService } from '@/services/company';
import { ConfigService } from '@/services/config';
import { ContactService } from '@/services/contact';
import { ContactLevelService } from '@/services/contact/levels';
import { ContactsService } from './services/contact/ContactsService';
import { DataUpdateActionsService } from '@/services/data-update/actions';
import { DataUpdateConfigService } from '@/services/data-update/config';
import { DataUpdateCriteriaService } from '@/services/data-update/criteria';
import { DataUpdateDynamicSelects } from '@/services/data-update/dynamic_selects';
import { DataProcessService } from '@/services/data_processing';
import { GroupDedupeService } from '@/services/data_processing/group-dedupe';
import { GroupingCalculator } from '@/services/data_processing/grouping-calculator';
import { GroupingRulesService } from '@/services/data_processing/grouping-rules';
import { ClassificationService } from '@/services/document-classification/service';
import { DocumentProfileService } from '@/services/document-profiles/service';
import { DocumentProcessingTaskService } from '@/services/documents/document-processing-task';
import { ExtractTableService } from '@/services/extract-table/service';
import { ExtractTableValidator } from '@/services/extract-table/validator';
import { FieldsService } from '@/services/fields';
import { GoogleDocumentAIService } from '@/services/google-document-ai';
import { HtmlExtractService } from '@/services/htmlExtract/service';
import { AppLoggerService } from '@/services/logger/appLogger';
import { MyAdvisorGridsService } from '@/services/myadvisorgrids';
import { NanonetsService } from '@/services/nanonets/service';
import {
  GChatService,
  type IGChatService,
} from '@/services/notification/gchat';
import { NowCertsService } from '@/services/nowCerts';
import { OneHQService } from '@/services/oneHQ';
import { ZohoService } from '@/services/zoho';
import { PaymentAllocateService } from '@/services/payment-allocate';
import { PermissionService } from '@/services/permission';
import { ProcessorsService } from '@/services/processors/service';
import { ProductService } from '@/services/products';
import { QueueService } from '@/services/queue';
import { SyncOverrideFieldsService } from '@/services/queue/syncOverrideFields';
import { AgencyIntegratorWorker } from '@/services/queue/worker/agencyIntegrator';
import { AwsS3Worker } from '@/services/queue/worker/awss3';
import { BaseWorker } from '@/services/queue/worker/base';
import { BenefitPointWorker } from '@/services/queue/worker/benefitPoint';
import { DocumentProcessingWorker } from '@/services/queue/worker/documentProcessing';
import { MyAdvisorGridsWorker } from '@/services/queue/worker/myadvisorgrids';
import { NowCertsWorker } from '@/services/queue/worker/nowCerts';
import { OneHQWorker } from '@/services/queue/worker/oneHQ';
import { SmartOfficeWorker } from '@/services/queue/worker/smartOffice';
import { TransGlobalWorker } from '@/services/queue/worker/transGlobal';
import { ReceivableScheduleService } from '@/services/receivable-schedule';
import { ReconcilerOutputService } from '@/services/reconciler-output';
import { ReconciliationService } from '@/services/reconciliation';
import { ReleaseService } from '@/services/release';
import { ReportService } from '@/services/report';
import { ZohoWorker } from '@/services/queue/worker/zoho';
import ReportDataFilterService from '@/services/report/filter';
import { SessionService } from '@/services/session';
import { CompReportSettingsService } from '@/services/settings/comp_reports';
import { ShareFilter } from '@/services/share/filter';
import { SmartOfficeService } from '@/services/smart-office';
import { StatementService } from '@/services/statement';
import { StatementFilterService } from '@/services/statement/filter';
import { TokenService } from '@/services/token';
import { TransGlobalService } from '@/services/transglobal';
import { UserService } from '@/services/user';
import { WidgetService } from '@/services/widget';
import type ICommissionScheduleRepo from './persistence/commission-schedules/ICommissionScheduleRepo';
import PostgresCommissionScheduleRepo from './persistence/commission-schedules/PostgresCommissionScheduleRepo';
import type ICompGridCriteriaRepo from './persistence/comp-grid-criteria/ICompGridCriteriaRepo';
import PostgresCompGridCriteriaRepo from './persistence/comp-grid-criteria/PostgresCompGridCriteriaRepo';
import type ICompGridProductsRepo from './persistence/comp-grid-products/ICompGridProductsRepo';
import PostgresCompGridProductsRepo from './persistence/comp-grid-products/PostgresCompGridProductsRepo';
import type ICompProfilesRepo from './persistence/comp-profiles/ICompProfilesRepo';
import PostgresCompProfilesRepo from './persistence/comp-profiles/PostgresCompProfilesRepo';
import type ICompanyRepository from './persistence/companies/ICompanyRepository';
import PostgresCompanyRepository from './persistence/companies/PostgresCompanyRepository';
import type ICompanyProductOptionsRepo from './persistence/company-product-options/ICompanyProductOptionsRepo';
import PostgresCompanyProductOptionsRepo from './persistence/company-product-options/PostgresCompanyProductOptionsRepo';
import type ICompanyProductsRepo from './persistence/company-products/ICompanyProductsRepo';
import PostgresCompanyProductsRepo from './persistence/company-products/PostgresCompanyProductsRepo';
import type IContactsRepo from './persistence/contacts/IContactsRepo';
import PostgresContactsRepo from './persistence/contacts/PostgresContactsRepo';
import type IReconciliationDataRepo from './persistence/reconciliation-data/IReconciliationDataRepo';
import PostgresReconciliationDataRepo from './persistence/reconciliation-data/PostgresReconciliationDataRepo';
import type IReportDataRepo from './persistence/report-data/IReportDataRepo';
import PostgresReportDataRepo from './persistence/report-data/PostgresReportDataRepo';
import { ContactsQueries } from './queries/contactsQueries';
import { AccountingService } from './services/accounting';
import { APIKeyValidator } from './services/api_key/validator';
import CommissionSchedulesService from './services/commission-schedules/CommissionSchedulesService';
import CompGridsService from './services/comp-grids/CompGridsService';
import { CompGridCriteriaService } from './services/comp-grids/criteria';
import { CompGridProductsService } from './services/comp-grids/products';
import CompProfilesService from './services/comp-profiles/CompProfilesService';
import { ContactGroupService } from './services/contact/group';
import { ContactHierarchyService } from './services/contact/hierarchy';
import { ContactOptionsService } from './services/contact/options';
import { BulkContactsOperationService } from './services/bulk-operations/contacts/BulkContactsOperationService';
import { BulkPartnerGridLevelService } from './services/bulk-operations/contacts/BulkPartnerGridLevelService';
import { CustomerService } from './services/customers/service';
import { DashboardService } from './services/dashboard';
import { DataUpdateService } from './services/data-update';
import { DateRangesService } from './services/date-ranges';
import { DocumentsService } from './services/documents';
import { DocumentFileService } from './services/documents/fileService';
import { EmailerService } from './services/emailer';
import { FirebaseStorageSignedUrlService } from './services/firebase-storage/signedUrl';
import { HtmlExtractValidator } from './services/htmlExtract/validator';
import { NanonetsValidator } from './services/nanonets/validator';
import { PdfService } from './services/pdfService';
import ReceivableCalcService from './services/receivable-calc/ReceivableCalcService';
import { SavedReportsService } from './services/saved-reports';
import { SavedReportsGroupsService } from './services/saved-reports/group';
import { SettingsService } from './services/settings';
import { StatementDataOptionsService } from './services/statement/options';
import { UserAuthService } from './services/user/auth';
import { CommissionRetrieval } from '@/pages/api/data_processing/commissions/commissionRetrieval';
import { StatementDataService } from '@/services/statement/statement-data';
import { StatementHelper } from '@/services/statement/helpers';
import { HistoryService } from '@/services/history';
import { TimerStats } from '@/lib/timerStats';
import { CompGridsOptionsService } from '@/services/comp-grids/options';
import { CompGridsViewerExportService } from '@/services/comp-grids/viewer/export';
import { ReportDataService } from '@/services/report_data';
import type { ICompGridRepo } from './persistence/comp-grid/ICompGridRepo';
import CompGridRepo from './persistence/comp-grid/CompGridRepo';
import { DocumentsCrudService } from '@/services/documents/crud';
import { FeatureFlagsService } from '@/services/featureFlag';
import { CommissionCalculationProfileService } from '@/services/commission-calculation/comp-profile';

// Default scope is transient, the container will return a new instance when request to get a service
const container = new Container();
container.bind<TimerStats>(TimerStats).toSelf().inSingletonScope();
container.bind<AccountConfigService>(AccountConfigService).toSelf();
container.bind<AccountingService>(AccountingService).toSelf();
container
  .bind<AccountingTransactionsService>(AccountingTransactionsService)
  .toSelf();
container
  .bind<AccountProcessorConfigService>(AccountProcessorConfigService)
  .toSelf();
container.bind<AccountService>(AccountService).toSelf();
container.bind<AccountMetricsService>(AccountMetricsService).toSelf();
container.bind<AccountSettingsValidator>(AccountSettingsValidator).toSelf();
container.bind<AdobePDFExtractService>(AdobePDFExtractService).toSelf();
container.bind<AgencyIntegratorService>(AgencyIntegratorService).toSelf();
container.bind<AgencyIntegratorWorker>(AgencyIntegratorWorker).toSelf();
container.bind<APIKeyService>(APIKeyService).toSelf();
container.bind<APIKeyValidator>(APIKeyValidator).toSelf();
container.bind<AwsS3Worker>(AwsS3Worker).toSelf();
container.bind<AwsStorageService>(AwsStorageService).toSelf();
container.bind<BaseWorker>(BaseWorker).toSelf();
container.bind<BenefitPointService>(BenefitPointService).toSelf();
container.bind<BenefitPointWorker>(BenefitPointWorker).toSelf();
container.bind<CalculationService>(CalculationService).toSelf();
container.bind<ClassificationService>(ClassificationService).toSelf();
container.bind<CloudTaskService>(CloudTaskService).toSelf();
container.bind<CommissionCalculator>(CommissionCalculator).toSelf();
container.bind<CommissionRetrieval>(CommissionRetrieval).toSelf();
container.bind<CommissionProcessor>(CommissionProcessor).toSelf();
container.bind<AdvancedCommissionService>(AdvancedCommissionService).toSelf();
container.bind<CommissionSchedulesService>(CommissionSchedulesService).toSelf();
container.bind<CommissionService>(CommissionService).toSelf();
container.bind<CommissionUtils>(CommissionUtils).toSelf();
container.bind<CompaniesService>(CompaniesService).toSelf();
container.bind<CompanyService>(CompanyService).toSelf();
container.bind<CompGridCriteriaService>(CompGridCriteriaService).toSelf();
container.bind<CompGridLevelsService>(CompGridLevelsService).toSelf();
container.bind<CompGridProductsService>(CompGridProductsService).toSelf();
container.bind<CompGridRatesService>(CompGridRatesService).toSelf();
container.bind<CompGridsService>(CompGridsService).toSelf();
container.bind<CompGridValidator>(CompGridValidator).toSelf();
container.bind<CompProfileMatchingService>(CompProfileMatchingService).toSelf();
container.bind<CompProfilesService>(CompProfilesService).toSelf();
container
  .bind<CompProfilesBulkOperationsService>(CompProfilesBulkOperationsService)
  .toSelf();
container.bind<CompReportSettingsService>(CompReportSettingsService).toSelf();
container.bind<CompReportsService>(CompReportsService).toSelf();
container.bind<ConfigService>(ConfigService).toSelf();
container.bind<ContactGroupService>(ContactGroupService).toSelf();
container.bind<ContactHierarchyService>(ContactHierarchyService).toSelf();
container.bind<ContactLevelService>(ContactLevelService).toSelf();
container.bind<ContactOptionsService>(ContactOptionsService).toSelf();
container.bind<ContactService>(ContactService).toSelf();
container
  .bind<BulkContactsOperationService>(BulkContactsOperationService)
  .toSelf();
container
  .bind<BulkPartnerGridLevelService>(BulkPartnerGridLevelService)
  .toSelf();
container.bind<ContactsQueries>(ContactsQueries).toSelf();
container.bind<ContactsService>(ContactsService).toSelf();
container.bind<ContactsValidator>(ContactsValidator).toSelf();
container.bind<CustomerService>(CustomerService).toSelf();
container.bind<DashboardService>(DashboardService).toSelf();
container.bind<DataProcessService>(DataProcessService).toSelf();
container.bind<DataUpdateActionsService>(DataUpdateActionsService).toSelf();
container.bind<DataUpdateConfigService>(DataUpdateConfigService).toSelf();
container.bind<DataUpdateCriteriaService>(DataUpdateCriteriaService).toSelf();
container.bind<DataUpdateDynamicSelects>(DataUpdateDynamicSelects).toSelf();
container.bind<DataUpdateService>(DataUpdateService).toSelf();
container.bind<DataUpdateValidator>(DataUpdateValidator).toSelf();
container.bind<DateRangesService>(DateRangesService).toSelf();
container.bind<DocumentFileService>(DocumentFileService).toSelf();
container
  .bind<DocumentProcessingTaskService>(DocumentProcessingTaskService)
  .toSelf();
container.bind<DocumentProcessingWorker>(DocumentProcessingWorker).toSelf();
container.bind<DocumentProfileService>(DocumentProfileService).toSelf();
container.bind<DocumentsService>(DocumentsService).toSelf();
container.bind<EmailerService>(EmailerService).toSelf();
container.bind<ExtractTableService>(ExtractTableService).toSelf();
container.bind<ExtractTableValidator>(ExtractTableValidator).toSelf();
container.bind<FieldsService>(FieldsService).toSelf();
container
  .bind<FirebaseStorageSignedUrlService>(FirebaseStorageSignedUrlService)
  .toSelf();
container.bind<GoogleDocumentAIService>(GoogleDocumentAIService).toSelf();
container.bind<GroupDedupeService>(GroupDedupeService).toSelf();
container.bind<GroupingCalculator>(GroupingCalculator).toSelf();
container.bind<GroupingRulesService>(GroupingRulesService).toSelf();
container.bind<HtmlExtractService>(HtmlExtractService).toSelf();
container.bind<HtmlExtractValidator>(HtmlExtractValidator).toSelf();
container
  .bind<ICommissionScheduleRepo>(REPOSITORY_TYPES.CommissionScheduleRepository)
  .to(PostgresCommissionScheduleRepo);
container
  .bind<ICompanyProductOptionsRepo>(
    REPOSITORY_TYPES.CompanyProductOptionsRepository
  )
  .to(PostgresCompanyProductOptionsRepo);
container
  .bind<ICompanyProductsRepo>(REPOSITORY_TYPES.CompanyProductsRepository)
  .to(PostgresCompanyProductsRepo);
container
  .bind<ICompanyRepository>(REPOSITORY_TYPES.CompanyRepository)
  .to(PostgresCompanyRepository);
container
  .bind<ICompGridCriteriaRepo>(REPOSITORY_TYPES.CompGridCriteriaRepository)
  .to(PostgresCompGridCriteriaRepo);
container
  .bind<ICompGridProductsRepo>(REPOSITORY_TYPES.CompGridProductsRepository)
  .to(PostgresCompGridProductsRepo);
container
  .bind<ICompProfilesRepo>(REPOSITORY_TYPES.CompProfilesRepository)
  .to(PostgresCompProfilesRepo);
container
  .bind<IContactsRepo>(REPOSITORY_TYPES.ContactsRepository)
  .to(PostgresContactsRepo);
container
  .bind<ICompGridRepo>(REPOSITORY_TYPES.CompGridRepository)
  .to(CompGridRepo);
container
  .bind<IGChatService>(SERVICE_TYPES.GChatServiceFactory)
  .toFactory((_context) => {
    return (endpoint: string) => new GChatService(endpoint);
  });
container
  .bind<
    interfaces.SimpleFactory<
      AppLoggerService,
      ConstructorParameters<typeof AppLoggerService>
    >
  >(SERVICE_TYPES.LoggerServiceFactory)
  .toFactory((_context) => {
    return (options: ConstructorParameters<typeof AppLoggerService>[0]) =>
      new AppLoggerService(options);
  });
container
  .bind<
    interfaces.SimpleFactory<
      LRUCacheService,
      ConstructorParameters<typeof LRUCache>
    >
  >(SERVICE_TYPES.LRUCacheServiceFactory)
  .toFactory((_context) => {
    return (options: ConstructorParameters<typeof LRUCache>[0]) =>
      new LRUCacheService(options);
  });
container
  .bind<IReconciliationDataRepo>(REPOSITORY_TYPES.ReconciliationDataRepository)
  .to(PostgresReconciliationDataRepo);
container
  .bind<IReportDataRepo>(REPOSITORY_TYPES.ReportDataRepository)
  .to(PostgresReportDataRepo);
container.bind<MyAdvisorGridsService>(MyAdvisorGridsService).toSelf();
container.bind<MyAdvisorGridsWorker>(MyAdvisorGridsWorker).toSelf();
container.bind<NanonetsService>(NanonetsService).toSelf();
container.bind<NanonetsValidator>(NanonetsValidator).toSelf();
container.bind<NowCertsService>(NowCertsService).toSelf();
container.bind<NowCertsWorker>(NowCertsWorker).toSelf();
container.bind<OneHQService>(OneHQService).toSelf();
container.bind<OneHQWorker>(OneHQWorker).toSelf();
container.bind<ZohoService>(ZohoService).toSelf();
container.bind<ZohoWorker>(ZohoWorker).toSelf();
container.bind<PaymentAllocateService>(PaymentAllocateService).toSelf();
container.bind<PdfService>(PdfService).toSelf();
container.bind<PermissionService>(PermissionService).toSelf();
container.bind<PrismaClient>(PrismaClient).toFactory(() => prisma);
container.bind<ProcessorsService>(ProcessorsService).toSelf();
container.bind<ProductService>(ProductService).toSelf();
container.bind<QueueService>(QueueService).toSelf();
container
  .bind<QueueService>(SERVICE_TYPES.QueueServiceFactory)
  .toFactory((_context) => {
    return () => container.get(QueueService);
  });
container.bind<ReceivableCalcService>(ReceivableCalcService).toSelf();
container.bind<ReceivableScheduleService>(ReceivableScheduleService).toSelf();
container.bind<ReconcilerOutputService>(ReconcilerOutputService).toSelf();
container.bind<ReconciliationService>(ReconciliationService).toSelf();
container.bind<ReleaseService>(ReleaseService).toSelf();
container.bind<ReportDataFilterService>(ReportDataFilterService).toSelf();
container.bind<ReportService>(ReportService).toSelf();
container.bind<SavedReportsGroupsService>(SavedReportsGroupsService).toSelf();
container
  .bind<SavedReportsGroupsValidator>(SavedReportsGroupsValidator)
  .toSelf();
container.bind<SavedReportsService>(SavedReportsService).toSelf();
container.bind<SessionService>(SessionService).toSelf();
container.bind<SettingsService>(SettingsService).toSelf();
container.bind<ShareFilter>(ShareFilter).toSelf();
container.bind<SmartOfficeService>(SmartOfficeService).toSelf();
container.bind<SmartOfficeWorker>(SmartOfficeWorker).toSelf();
container
  .bind<StatementDataOptionsService>(StatementDataOptionsService)
  .toSelf();
container.bind<StatementFilterService>(StatementFilterService).toSelf();
container.bind<StatementService>(StatementService).toSelf();
container.bind<SyncFieldService>(SyncFieldService).toSelf();
container.bind<SyncOverrideFieldsService>(SyncOverrideFieldsService).toSelf();
container.bind<TokenService>(TokenService).toSelf();
container.bind<TransGlobalService>(TransGlobalService).toSelf();
container.bind<TransGlobalWorker>(TransGlobalWorker).toSelf();
container
  .bind<UpdatePolicyPayoutRatesValidator>(UpdatePolicyPayoutRatesValidator)
  .toSelf();
container.bind<UserAuthService>(UserAuthService).toSelf();
container.bind<UserService>(UserService).toSelf();
container.bind<UsersValidator>(UsersValidator).toSelf();
container.bind<WidgetService>(WidgetService).toSelf();
container.bind<StatementDataService>(StatementDataService).toSelf();
container.bind<StatementHelper>(StatementHelper).toSelf();
container.bind<HistoryService>(HistoryService).toSelf();
container.bind<CompGridsOptionsService>(CompGridsOptionsService).toSelf();
container
  .bind<CompGridsViewerExportService>(CompGridsViewerExportService)
  .toSelf();
container.bind<ReportDataService>(ReportDataService).toSelf();
container.bind<DocumentsCrudService>(DocumentsCrudService).toSelf();
container.bind<FeatureFlagsService>(FeatureFlagsService).toSelf();
container
  .bind<CommissionCalculationProfileService>(
    CommissionCalculationProfileService
  )
  .toSelf();

export { container };
