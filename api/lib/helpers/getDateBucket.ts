import type { Dayjs } from 'dayjs';

import dayjs from '@/lib/dayjs';
import type { WidgetTimePeriods } from 'common/dto/widgets';

export const MappingDateFormat = {
  day: (date: Dayjs) => date.format('YYYY/MM/DD'),
  month: (date: Dayjs) => date.format('YYYY/MM'),
  week: (date: Dayjs) => `Week of ${date.startOf('week').format('MM/DD')}`,
  year: (date: Dayjs) => date.format('YYYY'),
} as const;

export const getDateBucket = (
  date: Date | string,
  period: WidgetTimePeriods = 'month'
) => {
  if (date instanceof Date || typeof date === 'string') {
    const formattedDate = dayjs(date);
    if (!formattedDate.isValid()) return null;

    return MappingDateFormat[period]?.(formattedDate);
  }
  return null;
};
