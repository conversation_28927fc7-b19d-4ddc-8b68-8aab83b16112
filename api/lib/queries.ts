import prisma from '@/lib/prisma';
import type { TimerStats } from '@/lib/timerStats';

const getEffectiveDateCondition = (effectiveDate?: Date | undefined) => ({
  AND: [
    {
      OR: [
        ...(effectiveDate ? [{ start_date: { lte: effectiveDate } }] : []),
        { start_date: null },
      ],
    },
    {
      OR: [
        ...(effectiveDate ? [{ end_date: { gte: effectiveDate } }] : []),
        { end_date: null },
      ],
    },
  ],
});

// @ts-expect-error
const recursiveSelect = (depth: number, vars?: { effectiveDate?: Date }) => {
  if (depth === 0) {
    return {
      select: {
        parent: {
          include: {
            parent_relationships: {
              where: {
                state: 'active',
                parent_id: { not: null },
                ...getEffectiveDateCondition(vars?.effectiveDate),
              },
            },
          },
        },
        split_percentage: true,
      },
    };
  }

  return {
    select: {
      parent: {
        include: {
          parent_relationships: {
            where: {
              state: 'active',
              ...getEffectiveDateCondition(vars?.effectiveDate),
            },
            ...recursiveSelect(depth - 1, vars),
          },
        },
      },
      split_percentage: true,
    },
  };
};

export const findContactWithAncestorsCTE = async (
  strId: string,
  effectiveDate: Date | undefined,
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();

  const result = await prisma.$queryRaw`
    WITH RECURSIVE contact_ancestors AS (
      -- Base case: start with the requested contact
      SELECT 
        c.id,
        c.str_id,
        c.name,
        c.state,
        ch.parent_id,
        ch.split_percentage,
        0 as level,
        ARRAY[c.id] as path
      FROM contacts c
      LEFT JOIN contact_hierarchy ch ON c.id = ch.contact_id
          AND ch.state = 'active'
          AND ch.contact_id != ch.parent_id  -- Prevent self reference
          AND ch.parent_id IS NOT NULL
          AND (ch.start_date IS NULL OR ch.start_date <= ${effectiveDate || new Date()})
          AND (ch.end_date IS NULL OR ch.end_date >= ${effectiveDate || new Date()})
      WHERE c.str_id = ${strId} AND c.state = 'active'
      
      UNION ALL
      
      -- Recursive case: find parents
      SELECT 
        parent.id,
        parent.str_id,
        parent.name,
        parent.state,
        parent_ch.parent_id,
        parent_ch.split_percentage,
        ca.level + 1,
        ca.path || parent.id
      FROM contact_ancestors ca
      JOIN contacts parent ON parent.id = ca.parent_id
      LEFT JOIN contact_hierarchy parent_ch ON parent.id = parent_ch.contact_id
        AND parent_ch.state = 'active'
          AND parent_ch.contact_id != parent_ch.parent_id  -- Prevent self reference
          AND parent_ch.parent_id IS NOT NULL
          AND (parent_ch.start_date IS NULL OR parent_ch.start_date <= ${effectiveDate || new Date()})
          AND (parent_ch.end_date IS NULL OR parent_ch.end_date >= ${effectiveDate || new Date()})
      WHERE ca.parent_id IS NOT NULL
        AND ca.parent_id != ca.id  -- Prevent self reference
        AND parent.id != ALL(ca.path) -- Prevent infinite loops
        AND ca.level < 50 -- Safety limit
    )
    SELECT * FROM contact_ancestors 
    ORDER BY level, id;
  `;

  // @ts-expect-error
  const duration = timerStats?.end('findContactWithAncestorsCTE', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `findContactWithAncestorsCTE took ${duration?.toFixed(2)}s (total: ${timerStats
      ?.get('findContactWithAncestorsCTE')
      ?.toFixed(2)}s)\n${timerStats?.logString('findContactWithAncestorsCTE')}`
  );

  return result;
};

export const findContactWithAncestorsIterative = async (
  strId: string,
  effectiveDate: Date | undefined,
  maxDepth: number = 50,
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();
  const ancestors = [];
  let currentContactId: string | null = null;

  const initialContact = await prisma.contacts.findFirst({
    where: { str_id: strId, state: 'active' },
    include: {
      parent_relationships: {
        where: {
          state: 'active',
          ...getEffectiveDateCondition(effectiveDate),
        },
        select: {
          parent_id: true,
          split_percentage: true,
        },
      },
    },
  });

  if (!initialContact) {
    return null;
  }

  ancestors.push(initialContact);
  // Prevent self reference: if parent ID equals own ID, set to null
  const firstParentId = initialContact.parent_relationships[0]?.parent_id;
  currentContactId =
    firstParentId && firstParentId !== initialContact.id ? firstParentId : null;

  let depth = 0;
  const visitedIds = new Set([initialContact.id]);

  while (currentContactId && depth < maxDepth) {
    if (visitedIds.has(currentContactId)) {
      break;
    }

    const parentContact = await prisma.contacts.findFirst({
      where: { id: currentContactId, state: 'active' },
      include: {
        parent_relationships: {
          where: {
            state: 'active',
            ...getEffectiveDateCondition(effectiveDate),
          },
          select: {
            parent_id: true,
            split_percentage: true,
          },
        },
      },
    });

    if (!parentContact) {
      break;
    }

    ancestors.push(parentContact);
    visitedIds.add(parentContact.id);
    // Prevent self reference: ensure next parent ID is not equal to current contact ID
    const nextParentId = parentContact.parent_relationships[0]?.parent_id;
    currentContactId =
      nextParentId && nextParentId !== parentContact.id ? nextParentId : null;
    depth++;
  }

  // @ts-expect-error
  const duration = timerStats?.end('findContactWithAncestorsIterative', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `findContactWithAncestorsIterative took ${duration?.toFixed(2)}s (total: ${timerStats
      ?.get('findContactWithAncestorsIterative')
      ?.toFixed(
        2
      )}s)\n${timerStats?.logString('findContactWithAncestorsIterative')}`
  );

  return {
    contact: ancestors[0],
    ancestors: ancestors.slice(1),
    totalLevels: ancestors.length - 1,
  };
};

export const findContactWithAncestorsBatch = async (
  strId: string,
  effectiveDate: Date | undefined,
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();

  const contactIds = await prisma.$queryRaw<
    Array<{ id: string; level: number }>
  >`
    WITH RECURSIVE contact_ancestors AS (
      SELECT c.id, 0 as level
      FROM contacts c
      WHERE c.str_id = ${strId} AND c.state = 'active'
      
      UNION ALL
      
      SELECT ch.parent_id as id, ca.level + 1
      FROM contact_ancestors ca
      JOIN contact_hierarchy ch ON ca.id = ch.contact_id
      WHERE ch.state = 'active'
        AND ch.contact_id != ch.parent_id  -- Prevent self reference
        AND ch.parent_id IS NOT NULL
        AND ca.level < 50
        AND (ch.start_date IS NULL OR ch.start_date <= ${effectiveDate || new Date()})
        AND (ch.end_date IS NULL OR ch.end_date >= ${effectiveDate || new Date()})
)
    
    SELECT DISTINCT id, level FROM contact_ancestors ORDER BY level;
  `;

  if (contactIds.length === 0) {
    return null;
  }

  const [contacts, relationships] = await Promise.all([
    prisma.contacts.findMany({
      where: {
        id: { in: contactIds.map((c: { id: string; level: number }) => c.id) },
        state: 'active',
      },
    }),
    prisma.contact_hierarchy.findMany({
      where: {
        contact_id: {
          in: contactIds.map((c: { id: string; level: number }) => c.id),
        },
        state: 'active',
        ...getEffectiveDateCondition(effectiveDate),
      },
    }),
  ]);

  const contactMap = new Map(
    contacts.map((c: { id: string }) => [c.id, c] as const)
  );
  const relationshipMap = new Map(
    relationships.map((r: { contact_id: string }) => [r.contact_id, r] as const)
  );

  const result = contactIds.map(
    ({ id, level }: { id: string; level: number }) => {
      const contact = contactMap.get(id);
      return {
        ...(contact || {}),
        level,
        hierarchy_relationship: relationshipMap.get(id),
      };
    }
  );

  // @ts-expect-error
  const duration = timerStats?.end('findContactWithAncestorsBatch', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `findContactWithAncestorsBatch took ${duration?.toFixed(2)}s (total: ${timerStats
      ?.get('findContactWithAncestorsBatch')
      ?.toFixed(
        2
      )}s)\n${timerStats?.logString('findContactWithAncestorsBatch')}`
  );

  return result;
};

interface CacheEntry {
  data: unknown;
  timestamp: number;
}

const ancestorCache = new Map<string, CacheEntry>();
const CACHE_TTL = 5 * 60 * 1000;

export const findContactWithAncestorsCached = async (
  strId: string,
  effectiveDate: Date | undefined,
  timerStats?: TimerStats
) => {
  const cacheKey = `${strId}-${effectiveDate?.toISOString() || 'null'}`;
  const cached = ancestorCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }

  const result = await findContactWithAncestorsBatch(
    strId,
    effectiveDate,
    timerStats
  );

  if (result) {
    ancestorCache.set(cacheKey, {
      data: result,
      timestamp: Date.now(),
    });
  }

  return result;
};

export const clearExpiredAncestorCache = () => {
  const now = Date.now();
  for (const [key, value] of ancestorCache.entries()) {
    if (now - value.timestamp >= CACHE_TTL) {
      ancestorCache.delete(key);
    }
  }
};

export const clearAncestorCache = (strId?: string) => {
  if (strId) {
    for (const key of ancestorCache.keys()) {
      if (key.startsWith(`${strId}-`)) {
        ancestorCache.delete(key);
      }
    }
  } else {
    ancestorCache.clear();
  }
};

export const findContactInclAncestors = async (
  strId: string,
  effectiveDate: Date | undefined,
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();
  const query = {
    where: {
      str_id: strId,
      state: 'active',
    },
    accountInject: false,
    include: {
      parent_relationships: {
        where: {
          state: 'active',
          ...getEffectiveDateCondition(effectiveDate),
        },
        ...recursiveSelect(9, { effectiveDate }),
      },
    },
  };

  const result = await prisma.contacts.findFirst(query);
  // @ts-expect-error
  const duration = timerStats?.end('findContactInclAncestors', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `findContactInclAncestors took ${duration?.toFixed(2)}s (total: ${timerStats
      ?.get('findContactInclAncestors')
      ?.toFixed(2)}s)\n${timerStats?.logString('findContactInclAncestors')}`
  );
  return result;
};
