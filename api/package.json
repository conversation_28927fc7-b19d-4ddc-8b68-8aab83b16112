{"name": "@fintary/api", "version": "6.15.0", "required_fe_version": "2025-06-25 00:00", "preferred_fe_version": "2025-06-25 00:00", "private": true, "scripts": {"postinstall": "prisma generate", "tsc": "tsc", "dev-win": "set PORT=3000 && dotenv -e .env.dev next", "prod-win": "set PORT=3000 && dotenv -e .env.prod next", "dev-local": "npm run local", "integration-local": "prisma generate && set PORT=3000 && dotenv -e .env.test next", "gcloud:login": "gcloud auth list --filter='status:ACTIVE' --format='value(account)' || gcloud auth login", "gcloud:project:dev": "gcloud config set project fintary-dev", "gcloud:project:prod": "gcloud config set project fintary-prod", "gcloud:login:dev": "npm run gcloud:login && npm run gcloud:project:dev", "gcloud:login:prod": "npm run gcloud:login && npm run gcloud:project:prod", "update-local-secrets:dev": "npm run gcloud:login:dev && PROJECT_ID=fintary-dev ts-node scripts/load-secrets-to-env.ts .env.dev", "update-local-secrets:prod": "npm run gcloud:login:prod && PROJECT_ID=fintary-prod ts-node scripts/load-secrets-to-env.ts .env.prod", "dev": "prisma generate && cross-env PORT=3000 dotenv -e .env.dev next", "prod": "prisma generate && cross-env PORT=3000 dotenv -e .env.prod next", "build": "next build", "clean": "rm -rf .next", "deep-clean": "npm run clean && rm -rf node_modules", "start": "cross-env NODE_ENV=production node server.js", "local": "PORT=3000 dotenv -e .env.local next", "local:emulated": "FIREBASE_AUTH_EMULATOR_HOST='127.0.0.1:4002' FIREBASE_EMULATOR_HOST='127.0.0.1:4003' STORAGE_EMULATOR_HOST='127.0.0.1:4004' USE_FIREBASE_EMULATOR=true npm run local", "local:db-dump": "./scripts/pg_dump.sh", "local:db-generate-migration": "prisma generate && dotenv -e .env.local -- prisma migrate dev", "local:db-generate-migration-create-only": "prisma generate && dotenv -e .env.local -- prisma migrate dev --create-only", "local:db-seed": "ts-node scripts/seed-local-database/seed-local-database.ts", "local:migrate": "prisma generate && dotenv -e .env.local -- prisma migrate deploy", "integration": "prisma generate && PORT=3003 dotenv -e .env.test next", "integration:migrate": "prisma generate && dotenv -e .env.test -- prisma migrate deploy", "lint": "eslint . --max-warnings=0", "lint-fix": "eslint . --fix", "release": "npm run release --prefix ../", "changelog": "npm run changelog --prefix ../", "introspect": "dotenv -e .env.local prisma introspect", "test:unit": "cross-env TZ=UTC vitest -c ./vitest.config.unit.mts --run", "test:unit:ui": "cross-env TZ=UTC vitest -c ./vitest.config.unit.mts --ui", "test:db": "cross-env TZ=UTC vitest -c ./vitest.config.db.ts --run", "test:integration": "docker compose -f ../docker-compose.integration.yaml up --abort-on-container-exit --exit-code-from integration-test integration-test", "type:check": "tsc --noEmit --pretty", "deploy-dev": "npm run deploy-dev --prefix ../", "deploy-prod": "npm run deploy-prod --prefix ../", "load-payout-report-dev": "npm run script:dev ./payout-report-loader/csv-loader.ts", "load-payout-report-prod": "npm run script:prod ./payout-report-loader/csv-loader.js", "check-deps": "madge -c --extensions ts,tsx --ts-config ./tsconfig.json ./", "script:prod": "dotenv -e .env.prod -- ts-node -T -r tsconfig-paths/register", "script:dev": "dotenv -e .env.dev -- ts-node -T -r tsconfig-paths/register", "script:local": "dotenv -e .env.local -- ts-node -T -r tsconfig-paths/register"}, "dependencies": {"@adobe/pdfservices-node-sdk": "^4.1.0", "@aws-sdk/client-s3": "^3.806.0", "@casl/ability": "^6.7.3", "@google-cloud/documentai": "^9.1.0", "@google-cloud/logging-winston": "^6.0.0", "@google-cloud/secret-manager": "^6.0.1", "@google-cloud/storage": "^7.16.0", "@google-cloud/tasks": "^6.0.1", "@google-cloud/vertexai": "^1.10.0", "@prisma/client": "^6.14.0", "@react-pdf/renderer": "^4.3.0", "@sendgrid/mail": "^8.1.5", "@sentry/nextjs": "^8.55.0", "@sentry/node": "^9.40.0", "@types/react-dom": "^19.1.6", "archiver": "^7.0.1", "axios": "^1.9.0", "bignumber.js": "^9.3.0", "bluebird": "^3.7.2", "cheerio": "^1.0.0", "chrono-node": "2.8.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "common": "0.25.0", "crypto-hash": "^3.1.0", "csv-parser": "^3.2.0", "currency.js": "^2.0.4", "damerau-levenshtein": "^1.0.8", "dayjs": "^1.11.13", "didyoumean2": "^7.0.4", "fast-csv": "^5.0.2", "fast-xml-parser": "^4.5.3", "fastest-levenshtein": "^1.0.16", "firebase-admin": "^13.3.0", "formidable": "^3.5.4", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "inversify": "^6.2.2", "jsonwebtoken": "^9.0.2", "lodash-es": "^4.17.21", "lru-cache": "^11.1.0", "mathjs": "^14.4.0", "module-alias": "^2.2.3", "nanoid": "^5.1.5", "next": "^15.4.2", "next-api-decorators": "^2.0.2", "object-sizeof": "^2.6.5", "p-retry": "^6.2.1", "path-to-regexp": "^6.3.0", "pg-format": "^1.0.4", "pgvector": "^0.2.0", "plaid": "^36.0.0", "prisma-extension-pagination": "^0.7.5", "pyodide": "0.28.2", "react": "^19.1.0", "react-dom": "^19.1.0", "reflect-metadata": "^0.2.2", "soap": "^1.1.11", "statsig-node": "^5.33.0", "stripe": "^17.7.0", "uuid": "^11.1.0", "winston": "^3.17.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "yup": "^1.6.1", "zod": "^3.25.51", "zod-openapi": "^4.2.4"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@swc/core": "^1.13.4", "@types/adobe__pdfservices-node-sdk": "^2.2.3", "@types/bluebird": "^3.5.42", "@types/formidable": "^3.4.5", "@types/lodash-es": "^4.17.12", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/swagger-ui-react": "^5.18.0", "@vitest/ui": "^3.2.4", "cross-env": "^7.0.3", "dotenv": "^17.2.1", "dotenv-cli": "^8.0.0", "globals": "^15.15.0", "jest-worker": "^29.7.0", "madge": "^8.0.0", "next-swagger-doc": "^0.4.1", "prisma": "^6.14.0", "swagger-ui-react": "^5.27.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.9.2", "vitest": "^3.2.4", "vitest-mock-extended": "^3.1.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.44.1"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}}