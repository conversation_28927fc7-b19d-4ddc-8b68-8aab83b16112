import type { NextApiRequest } from 'next';
import { create<PERSON><PERSON><PERSON>, Post, Req } from 'next-api-decorators';
import { z } from 'zod';

import { BaseHandler } from '@/lib/baseHandler';
import { ZodBody } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest } from '@/types';
import {
  ProcessorsService,
  type ProcessorDescriptionUpload,
} from '@/services/processors/service';
import { container } from '@/ioc';

const ProcessorDescriptionUploadSchema = z.object({
  processor_id: z.string().min(1),
  file_content: z.string().min(1),
  file_name: z.string().min(1),
});

class Handler extends BaseHandler {
  private processorsService: ProcessorsService;

  constructor() {
    super();
    this.processorsService =
      container.get<ProcessorsService>(ProcessorsService);
  }

  @Post()
  async post(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    // biome-ignore format: compound decorator
    @(ZodBody(ProcessorDescriptionUploadSchema)())
    body: ProcessorDescriptionUpload
  ) {
    if (!req.account_id || !req.uid) {
      throw new Error('Account ID and UID are required');
    }

    return await this.processorsService.uploadProcessorDescriptionImage({
      body,
      accountId: req.account_id,
      uid: req.uid,
      req,
    });
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '15mb',
    },
  },
};

export default withAuth(createHandler(Handler));
