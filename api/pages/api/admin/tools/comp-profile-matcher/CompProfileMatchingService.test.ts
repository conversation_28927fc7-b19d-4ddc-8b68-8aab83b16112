import { describe, it, expect, vi, beforeEach, type Mock } from 'vitest';

import { CompProfileMatchingService } from '@/pages/api/admin/tools/comp-profile-matcher/CompProfileMatchingService';
import * as commissionUtils from '@/lib/commissions';
import prismaClient from '@/lib/prisma';
import { CommissionRetrieval } from '@/pages/api/data_processing/commissions/commissionRetrieval';

vi.mock('@/lib/prisma', () => {
  const prismaMock = {
    report_data: {
      findUnique: vi.fn(),
    },
    statement_data: {
      findUnique: vi.fn(),
    },
    contacts: {
      findMany: vi.fn(),
      findFirst: vi.fn(),
    },
    company_mappings: {
      findMany: vi.fn(),
    },
  };

  return {
    default: prismaMock,
    prismaClient: prismaMock,
  };
});

vi.mock('@/lib/commissions', async () => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const actual = await vi.importActual<any>('@/lib/commissions');
  return {
    ...actual,
    getApplicableCompProfiles: vi.fn(),
    getContactAncestorsWithCommissionProfiles: vi.fn(),
    flatCompProfileSets: vi.fn(),
    fetchProfileCompanies: vi.fn(),
    DataStates: { ACTIVE: 'active' },
  };
});

const mockGetAllData = vi.fn();
vi.mock('@/pages/api/data_processing/commissions/commissionRetrieval', () => ({
  CommissionRetrieval: vi.fn().mockImplementation(() => ({
    getAllData: mockGetAllData,
  })),
}));

describe('CompProfileMatchingService.match()', () => {
  let service: CompProfileMatchingService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new CompProfileMatchingService();
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (service as any).commissionRetrieval = new CommissionRetrieval();
  });

  it('calls fetchProfileCompanies when single_carrier_mode is false', async () => {
    (prismaClient.statement_data.findUnique as Mock).mockResolvedValue({
      id: 1,
      report_data_id: 2,
    });
    (prismaClient.report_data.findUnique as Mock).mockResolvedValue({
      effective_date: new Date('2025-01-01'),
    });
    (prismaClient.contacts.findMany as Mock).mockResolvedValue([
      { str_id: 'agent-1', id: 111 },
    ]);

    mockGetAllData.mockResolvedValue({
      companies: [],
      companyProducts: [],
      companyProductOptions: [],
      statementData: [],
      useCompGrids: false,
      compGridCriteria: [],
      compGridProducts: [],
    });

    const profile = {
      agent_commission_schedule_profile: {
        str_id: 'profile-abc',
        single_carrier_mode: false,
      },
    };

    const allAncestryProfiles = [
      {
        commissionProfiles: [profile],
      },
    ];

    (
      commissionUtils.getContactAncestorsWithCommissionProfiles as Mock
    ).mockResolvedValue(allAncestryProfiles);

    (commissionUtils.flatCompProfileSets as Mock).mockReturnValue([]);

    await service.match('acct-1', 'stmt-1', [
      { agentStrId: 'agent-1', compProfileStrId: 'profile-abc' },
    ]);

    expect(commissionUtils.fetchProfileCompanies).toHaveBeenCalledWith([
      profile,
    ]);
  });
});
