import * as Sentry from '@sentry/nextjs';
import * as dto from 'common/dto/comp_grid_products/dto';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';
import BigNumber from 'bignumber.js';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import {
  type CompGridProduct,
  CompGridProductsService,
  type ICompGridProductsService,
} from '@/services/comp-grids/products';
import { container } from '@/ioc';
import { calculateSkipAndTake } from '@/lib/prisma/utils';
import { ZodBody } from '@/lib/decorators';

class Handler extends BaseHandler {
  private CompGridProductsService: CompGridProductsService;

  constructor() {
    super();
    this.CompGridProductsService = container.get<CompGridProductsService>(
      CompGridProductsService
    );
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await _getData(req);
      res.json(data);
    } catch (error) {
      // @ts-expect-error
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`An error occurred when getting data: ${error.message}`);
      Sentry.captureException(error);
      // @ts-expect-error
      res.status(500).json({ error: error.message });
    }
  }

  @Post()
  async post(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridProductCreateDTOSchema)())
    body: dto.CompGridProductCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await createOne(req, body, this.CompGridProductsService);
      res.status(201).json(data);
    } catch (error) {
      // @ts-expect-error
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error creating new record: ${error.message}`);
      Sentry.captureException(error);
      // @ts-expect-error
      res.status(500).json({ error: error.message });
    }
  }

  @Patch()
  async patch(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridProductUpdateDTOSchema)())
    body: dto.CompGridProductUpdateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const data = await updateOne(req, body, this.CompGridProductsService);
      res.status(200).json(data);
    } catch (error) {
      // @ts-expect-error
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error updating record: ${error.message}`);
      Sentry.captureException(error);
      // @ts-expect-error
      res.status(500).json({ error: error.message });
    }
  }

  @Delete()
  async delete(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridProductDeleteDTOSchema)())
    body: dto.CompGridProductDeleteDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      await deleteMany(req, body);
      res.status(200).json({ status: 'OK' });
    } catch (error) {
      // @ts-expect-error
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error deleting record: ${error.message}`);
      Sentry.captureException(error);
      // @ts-expect-error
      res.status(500).json({ error: error.message });
    }
  }
}

export default withAuth(createHandler(Handler));

export const _getData = async (req: ExtNextApiRequest & NextApiRequest) => {
  const qc = req.query?.qc;
  const isDynamicSelect = req.query?.is_dynamic_select;
  const onlyTypes = req.query?.only_types;
  const { page, limit, q } = req.query;

  const { skip, take } = calculateSkipAndTake({ page, limit });

  const where = {
    account_id: req.account_id,
    state: 'active',
    str_id: req.query?.id,
    comp_grid: { state: 'active', id: undefined, str_id: undefined },
    company_products:
      qc === 'contains_products'
        ? { some: { state: 'active' } }
        : qc === 'missing_products'
          ? { none: { state: 'active' } }
          : undefined,
    ...(q && {
      OR: generateSearchQueryForFields(q, ['name', 'type', 'notes']),
    }),
  };

  if (req.query?.comp_grid_id) {
    const ids = Array.isArray(req.query?.comp_grid_id)
      ? req.query?.comp_grid_id
      : [req.query?.comp_grid_id];
    const isNumeric = /^\d+$/.test(ids[0]);
    // @ts-expect-error
    if (isNumeric) where.comp_grid.id = { in: ids.map(Number) };
    // @ts-expect-error
    else where.comp_grid.str_id = { in: ids };
  }

  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let count;

  if (req.query.limit) {
    count = await prisma.comp_grid_products.count({ where });
  }

  if (onlyTypes === 'true') {
    const data = await prisma.comp_grid_products.groupBy({
      where,
      by: ['type'],
    });
    return {
      data: data.map((e: { type: string }) => ({
        id: e.type,
        name: e.type,
      })),
    };
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const queryOptions: any = {
    where,
    include: {
      company_products: { select: { id: true }, where: { state: 'active' } },
    },
  };

  if (!isDynamicSelect) {
    queryOptions.take = take;
    queryOptions.skip = skip;
  }

  const data = await prisma.comp_grid_products.findMany(queryOptions);
  // TODO: Find a standardized way to represent dynamic selects on FE and BE.
  // This is a generated "column" to have a standardized way of representing the associated products.
  // In this case, we want them to be ids. Without this, datum.company_product[] is ids when created/added,
  // and objects when queried, and a mix when adding to a queried list.
  // @ts-expect-error
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data.forEach((d) => {
    d.company_product_ids =
      d.company_products && d.company_products.length > 0
        ? // @ts-expect-error
          d.company_products.map((cp) => cp.id)
        : [{ id: null }];
  });
  // If limit is provided, we need to return the count and data, backwards compatibility
  return req.query.limit ? { count, data } : data;
};

const createOne = async (
  req: ExtNextApiRequest,
  body: dto.CompGridProductCreateDTO,
  compGridProductsService: ICompGridProductsService
) => {
  if (!body.comp_grid_id) throw new Error('Missing comp grid id');
  if (!req.account_id) throw new Error('Missing account id');

  const productData: CompGridProduct = {
    comp_grid_id: body.comp_grid_id,
    name: body.name,
    // @ts-expect-error
    type: body.type,
    // @ts-expect-error
    notes: body.notes,
    company_product_ids: body.company_product_ids,
  };

  const response = await compGridProductsService.createCompGridProduct(
    productData,
    req.account_id,
    // @ts-expect-error
    req.uid,
    req.ouid
  );

  return response;
};

const updateOne = async (
  req: ExtNextApiRequest,
  body: dto.CompGridProductUpdateDTO,
  compGridProductsService: ICompGridProductsService
) => {
  if (!body.id) throw new Error('Missing id');
  if (!req.account_id) throw new Error('Missing account id');

  const id = new BigNumber(body.id);
  const companyProductIds =
    Array.isArray(body.company_product_ids) &&
    body.company_product_ids.every((id) => typeof id === 'number')
      ? body.company_product_ids
      : null;

  const productData: CompGridProduct = {
    id: id.toNumber(),
    name: body.name,
    // @ts-expect-error
    type: body.type,
    // @ts-expect-error
    notes: body.notes,
    comp_grid_id: body.comp_grid_id,
    // @ts-expect-error
    company_product_ids: companyProductIds,
  };

  const response = await compGridProductsService.updateCompGridProduct(
    productData,
    req.account_id,
    // @ts-expect-error
    req.uid,
    req.ouid
  );

  return response;
};

const deleteMany = async (
  req: ExtNextApiRequest,
  body: dto.CompGridProductDeleteDTO
) => {
  const { ids } = body;
  if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
  // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
    throw new Error('Invalid ids');
  const promises = ids.map((_id) => {
    return prisma.comp_grid_products.update({
      where: { id: Number(_id), account_id: String(req.account_id) },
      data: {
        state: 'deleted',
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    });
  });
  await prisma.$transaction(promises);
};

const generateSearchQueryForFields = (q: string, fields: string[]) => {
  // @ts-expect-error
  return fields.reduce((acc, field) => {
    // Mode: 'insensitive' is case-insensitive search
    // biome-ignore lint/performance/noAccumulatingSpread: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    return [...acc, { [field]: { contains: q, mode: 'insensitive' } }];
  }, []);
};
