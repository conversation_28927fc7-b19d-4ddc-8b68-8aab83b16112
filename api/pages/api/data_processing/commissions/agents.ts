import * as commissions from 'common/dto/data_processing/commissions';
import { nanoid } from 'nanoid';
import { createHandler, Delete, Post, Req, Res } from 'next-api-decorators';
import type { NextRequest, NextResponse } from 'next/server';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { AccountInfo, ZodBody } from '@/lib/decorators';
import { runInBatch } from '@/lib/helpers';
import { withAuth } from '@/lib/middlewares';
import { asyncLocalStorage } from '@/services/logger/appLogger';
import { CloudTaskService } from '@/services/cloud-task';
import { DataProcessService } from '@/services/data_processing';
import { Guard } from '@/services/permission/decorator';
import { CommonAction, EntityType } from '@/services/permission/interface';
import { QueueService } from '@/services/queue';
import { Queue } from '@/services/queue/types';
import {
  DataProcessingStatuses,
  DataProcessingTypes,
  type ExtAccountInfo,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
} from '@/types';
import {
  CommissionProcessor,
  policyScopedAccounts,
} from './commissionProcessor';
import { CommissionRetrieval } from '@/pages/api/data_processing/commissions/commissionRetrieval';

class Handler extends BaseHandler {
  private readonly commissionProcessor: CommissionProcessor;
  private readonly commissionRetrieval: CommissionRetrieval;

  constructor() {
    super();
    this.commissionProcessor =
      container.get<CommissionProcessor>(CommissionProcessor);
    this.commissionRetrieval =
      container.get<CommissionRetrieval>(CommissionRetrieval);
  }

  @Post()
  @Guard(CommonAction.RUN, EntityType.SETTINGS_DATA_PROCESSING)
  async calc(
    @Req() req: ExtNextApiRequest & NextRequest,
    @Res() res: ExtNextApiResponse & NextResponse,
    // biome-ignore format: compound decorator
    @(ZodBody(commissions.CommissionCalcSchema)())
    body: commissions.CommissionCalcDTO,
    @AccountInfo() account: ExtAccountInfo
  ) {
    if (body.isSync) {
      if (body.regressionTestMode) {
        req.body.regressionTestMode = true;
        req.account_id = body.regressionAccount;
        req.logger.info(`Regression account set to: ${body.regressionAccount}`);

        // Update asyncLocalStorage with regression account information
        const store = asyncLocalStorage.getStore();
        asyncLocalStorage.enterWith({
          ...store,
          account: {
            ...store?.account,
            // @ts-expect-error
            account_id: body.regressionAccount,
          },
        });
      }
      return await this.commissionProcessor.processCommissions(req, res);
    }
    // If isSync is false, dispatch tasks to the queue
    const queueService = container.get<QueueService>(QueueService);
    const taskService = container.get<CloudTaskService>(CloudTaskService);
    const dataProcessingService =
      container.get<DataProcessService>(DataProcessService);
    const params = {
      account,
      type: DataProcessingTypes.agent_commission_calc,
      queue: Queue.AGENT_COMMISSION_CALC,
      payload: {
        ...body,
      },
    };
    const statements = await this.commissionRetrieval.getTaskStatements(
      body,
      account.account_id
    );
    // @ts-expect-error
    const taskId = await queueService.createTask<commissions.CommissionCalcDTO>(
      params,
      {
        disableTask: true,
      }
    );
    const stats = {
      tasks: [],
      totalCount: statements.length,
    };
    if (statements.length === 0) {
      await dataProcessingService.updateTaskStatus({
        str_id: taskId,
        total: 0,
        status: DataProcessingStatuses.COMPLETED,
        stats: stats,
      });
      return { success: true, taskId };
    }
    try {
      const POLICY_SCOPED_ACCOUNT_BATCH_SIZE = 100;
      const DEFAULT_BATCH_SIZE = 1000;
      // @ts-expect-error
      stats.tasks = await runInBatch({
        name: 'Creating tasks',
        items: statements?.map((r) => r.id),
        onBatch: async (statementIds) => {
          const ret = await taskService.createTask({
            account,
            url: process.env.CLOUD_WORKER_URL,
            queue: Queue.AGENT_COMMISSION_CALC,
            type: DataProcessingTypes.agent_commission_calc,
            task_id: nanoid(),
            payload: {
              ...body,
              master_str_id: taskId,
              statementIds: statementIds,
            },
          });
          return [ret];
        },
        // Too lower batchSize may result in insuffient cache usage
        batchSize: policyScopedAccounts.includes(account.account_id)
          ? POLICY_SCOPED_ACCOUNT_BATCH_SIZE
          : +(process.env.WORKER_BATCH_SIZE ?? DEFAULT_BATCH_SIZE),
      });
    } catch (err) {
      // @ts-expect-error
      req.logger.warn(err);
    } finally {
      await dataProcessingService.updateTaskStatus({
        str_id: taskId,
        total: statements.length,
        status: DataProcessingStatuses.PROCESSING,
        stats: stats,
      });
    }
    return { success: true, taskId };
  }

  @Delete()
  @Guard(CommonAction.RUN, EntityType.SETTINGS_DATA_PROCESSING)
  async deleteResults(
    @Req() req: ExtNextApiRequest & NextRequest,
    @Res() res: ExtNextApiResponse & NextResponse
  ) {
    return await this.commissionProcessor.deleteResults(req, res);
  }
}

export default withAuth(createHandler(Handler));
