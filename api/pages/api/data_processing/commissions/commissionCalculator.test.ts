import { describe, it, expect, vi, beforeEach } from 'vitest';
import BigNumber from 'bignumber.js';

import { CommissionUtils } from './commissionUtils';
import { ReferralTypes } from '@/types';
import { CommissionCalculator } from './commissionCalculator';
import { CommissionCalcContext } from './commissionCalContext';
import type { CommissionCalcDTO } from 'common/dto/data_processing/commissions';
import { container } from '@/ioc';

vi.mock('./commissionUtils', () => {
  return {
    CommissionUtils: vi.fn().mockImplementation(() => ({
      getUsedSeenPolicyCommissions: vi.fn(),
      isActivateCustomMethod: vi.fn(),
      alignSign: vi.fn(),
      isIMO: vi.fn(),
      isCareMatters: vi.fn(),
      getValueSign: vi.fn(),
    })),
  };
});

describe('CommissionCalculator', () => {
  let calculator: CommissionCalculator;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let commissionUtilsMock: any;
  // @ts-expect-error
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let ctx;

  beforeEach(() => {
    ctx = new CommissionCalcContext({} as CommissionCalcDTO);
    commissionUtilsMock = container.get<CommissionUtils>(CommissionUtils);
    calculator = new CommissionCalculator();
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (calculator as any).commissionUtils = commissionUtilsMock;
  });

  describe('percentageToDecimal', () => {
    it.each([
      { input: 50, expected: 0.5 },
      { input: 100, expected: 1 },
      { input: 0, expected: 0 },
      { input: 123.45, expected: 1.2345 },
      { input: new BigNumber(50), expected: 0.5 },
      { input: new BigNumber('123.45'), expected: 1.2345 },
      { input: new BigNumber(0), expected: 0 },
    ])('should convert $input to decimal', ({ input, expected }) => {
      const result = calculator.percentageToDecimal(input);
      expect(result.toNumber()).toBe(expected);
    });

    it('should handle edge cases', () => {
      expect(calculator.percentageToDecimal(10000).toNumber()).toBe(100);
      expect(calculator.percentageToDecimal(-50).toNumber()).toBe(-0.5);
    });
  });
  describe('policyScopeCalc', () => {
    it('throws error if calculationParallelLevel is not 1', async () => {
      await expect(
        calculator.policyScopeCalc({
          agentCommissionProfile: {},
          accountId: 1,
          calcBasis: BigNumber(100),
          calcMethod: 'fixed',
          commissionAmountBasis: 'premium',
          payoutRate: 0.1,
          statement: {},
          contact: {},
          policyCommissionsSeenAmount: {},
          policyCommissionsSeenRecords: {},
          policyCommissionsUsedAmount: {},
          policyCommissionsUsedRecords: {},
          rule: {},
          calculationParallelLevel: 2,
        })
      ).rejects.toThrow(
        'PolicyScope parallel calculation is not supported yet'
      );
    });

    it('returns default values if statement.report.id is not provided', async () => {
      const result = await calculator.policyScopeCalc({
        agentCommissionProfile: {},
        accountId: 1,
        calcBasis: BigNumber(100),
        calcMethod: 'fixed',
        commissionAmountBasis: 'premium',
        payoutRate: 0.1,
        statement: {},
        contact: {},
        policyCommissionsSeenAmount: {},
        policyCommissionsSeenRecords: {},
        policyCommissionsUsedAmount: {},
        policyCommissionsUsedRecords: {},
        rule: {},
        calculationParallelLevel: 1,
      });
      expect(result).toEqual({
        commissionAmount: BigNumber(0),
        commissionRate: BigNumber(0),
        policyCommissionsUsed: BigNumber(0),
      });
    });

    it('retrieves and processes policy commissions accurately', async () => {
      const mockCommissions = { key: { used: 10, seen: 20 } };
      commissionUtilsMock.getUsedSeenPolicyCommissions.mockResolvedValue(
        mockCommissions
      );

      const result = await calculator.policyScopeCalc({
        agentCommissionProfile: {},
        accountId: 1,
        calcBasis: BigNumber(100),
        calcMethod: 'fixed',
        commissionAmountBasis: 'premium',
        payoutRate: 0.1,
        statement: { report: { id: 1 }, id: 'stmt1' },
        contact: { str_id: '123' },
        policyCommissionsSeenAmount: {},
        policyCommissionsSeenRecords: {},
        policyCommissionsUsedAmount: {},
        policyCommissionsUsedRecords: {},
        rule: {},
        calculationParallelLevel: 1,
      });

      expect(result).toHaveProperty('commissionAmount');
      expect(result).toHaveProperty('commissionRate');
      expect(result.policyCommissionsUsed.toNumber()).toEqual(10);
    });
  });

  describe('calcFixedOverrideNew', () => {
    it('calculates fixed override correctly', () => {
      vi.spyOn(commissionUtilsMock, 'alignSign').mockReturnValue(BigNumber(10));

      const result = calculator.calcFixedOverride({
        config: { rate: 10, payee_id: 1 },
        // @ts-expect-error
        statement: { premium_amount: 100, commission_amount: 50 },
        // @ts-expect-error
        contactsMap: { 1: { str_id: '123' } },
        runningTotal: BigNumber(0.5),
      });

      expect(result).toHaveProperty('commissionAmount');
      expect(result).toHaveProperty('payeeStrId', '123');
      // @ts-expect-error
      expect(result.commissionAmount.toNumber()).toBe(10);
      // @ts-expect-error
      expect(result.payoutRate.toNumber()).toBe(10);
    });
  });

  describe('calcOverrideBonusNew', () => {
    it('calculates override bonus correctly', () => {
      vi.spyOn(commissionUtilsMock, 'alignSign').mockReturnValue(
        BigNumber(100)
      );
      const result = calculator.calcOverrideBonus({
        config: { rate: 15, payee_id: 1 },
        runningTotal: BigNumber(0.5),
        // @ts-expect-error
        statement: { premium_amount: 200, split_percentage: 50 },
        // @ts-expect-error
        contactsMap: { 1: { str_id: '123' } },
        hierarchySplitPercentage: BigNumber(0.5),
      });

      expect(result).toHaveProperty('payeeStrId', '123');
      // @ts-expect-error
      expect(result.commissionAmount.toNumber()).toBe(100);
      // @ts-expect-error
      expect(result.payoutRate.toNumber()).toBe(15);
    });

    describe('When payee_id is provided', () => {
      beforeEach(async () => {
        vi.doUnmock('./commissionUtils');
        const Utils = await import('./commissionUtils');

        commissionUtilsMock = new CommissionUtils();
        calculator = new CommissionCalculator();
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        (calculator as any).commissionUtils = new Utils.CommissionUtils();
      });
      it("Should respect to statement's split_percentage", async () => {
        const result = calculator.calcOverrideBonus({
          config: { rate: 10, payee_id: 1 },
          runningTotal: BigNumber(0.5),
          // @ts-expect-error
          statement: { premium_amount: 200, split_percentage: 10 },
          // @ts-expect-error
          contactsMap: { 1: { str_id: '123' } },
          hierarchySplitPercentage: BigNumber(0.5),
        });

        // @ts-expect-error
        expect(result.commissionAmount.toNumber()).toBe(0.01);
      });

      it.each([
        {
          name: 'when split_percentage is not provided',
          statement: { premium_amount: 200 },
        },
        {
          name: 'when split_percentage is null',
          statement: { premium_amount: 200, split_percentage: null },
        },
        {
          name: 'when split_percentage is undefined',
          statement: { premium_amount: 200, split_percentage: undefined },
        },
      ])(
        'Should default split_percentage to 100 if $name',
        async ({ statement }) => {
          const result = calculator.calcOverrideBonus({
            config: { rate: 10, payee_id: 1 },
            runningTotal: BigNumber(0.5),
            // @ts-expect-error
            statement,
            // @ts-expect-error
            contactsMap: { 1: { str_id: '123' } },
            hierarchySplitPercentage: BigNumber(0.5),
          });

          // @ts-expect-error
          expect(result.commissionAmount.toNumber()).toBe(0.1);
        }
      );
    });

    describe('When payee_id is not provided', () => {
      it('Should return early', () => {
        const result = calculator.calcOverrideBonus({
          config: { rate: 10 },
          runningTotal: BigNumber(0.5),
          // @ts-expect-error
          statement: { premium_amount: 200, split_percentage: 50 },
          // @ts-expect-error
          contactsMap: {},
          hierarchySplitPercentage: BigNumber(0.5),
        });

        expect(result).toEqual(undefined);
      });
    });
  });

  describe('sequenceRunMethods', () => {
    it('processes fixed and bonus overrides correctly', async () => {
      const mockConfig = [
        { method: 'fixed', rate: 10, payee_id: 1 },
        { method: 'bonus', rate: 5 },
      ];

      const lookupData = {};
      const agentApplicableProfile = {
        agentProfileConfig: {
          config: mockConfig,
          agent_commission_schedule_profile_id: 123,
          agent_commission_schedule_profile: {
            name: 'Test Profile',
            str_id: 'abc123',
          },
        },
        rule: {
          keep_rate: 10,
        },
      };
      const statement = {
        id: 1,
        premium_amount: 100,
        split_percentage: 50,
      };
      const contactsMap = { 1: { str_id: '123' } };

      commissionUtilsMock.isActivateCustomMethod.mockResolvedValue(true);

      const result = await calculator.sequenceRunMethods({
        lookupData,
        agentApplicableProfile,
        // @ts-expect-error
        statement,
        // @ts-expect-error
        contactsMap,
        // @ts-expect-error
        ctx: ctx,
        req: {},
        hierarchySplitPercentage: BigNumber(100),
        useCompGrids: false,
        cache: {},
        agentUplines: [],
      });

      expect(result).toHaveProperty('runningTotal');
      expect(result.runningTotal.toNumber()).toBe(0);
    });
  });

  describe('referralCalc', () => {
    it('calculates referral commissions correctly', () => {
      const statement = {
        compensation_type: 'FYC',
        id: 1,
        split_percentage: 50,
        premium_amount: 200,
        effective_date: '2025-01-01',
      };

      const contactsMap = {
        1: {
          str_id: '123',
          contact_referrals: [
            {
              referrer_contact_id: 1,
              type: ReferralTypes.AGENT,
              config: {
                rate: 5,
              },
            },
          ],
          contact_level: [{ level_label: 'Level 1' }],
        },
      };

      commissionUtilsMock.isCareMatters.mockReturnValue(false);
      commissionUtilsMock.alignSign.mockReturnValue(BigNumber(10));
      commissionUtilsMock.getValueSign.mockReturnValue(1);

      const result = calculator.referralCalc({
        agentCommissionProfile: {
          agentProfileConfig: { multiplier: 100 },
        },
        calculationBasisAmountSplitted: BigNumber(200),
        statement,
        statementContactStrId: '1',
        contactsMap,
        isAnnuity: false,
        sRate: 0.1,
        a3Rate: 0.2,
        effectiveDate: new Date('2025-01-01'),
      });

      expect(result).toBeInstanceOf(Array);
      expect(result[0]).toHaveProperty('amount');
      expect(result[0].amount).toBeInstanceOf(BigNumber);

      expect(result[0].amount.toNumber()).toBeCloseTo(10);
      expect(result[0].logs).toHaveProperty('commissionAmount');
      expect(result[0].logs).toHaveProperty('commissionRate');
      expect(result[0].logs.commissionRate.toNumber()).toBe(0.05);
    });
  });

  describe('getFinalCalcBasisWithSign', () => {
    const defaultParams = {
      defaultBasis: new BigNumber(1000),
      runningTotal: new BigNumber(200),
      calculationBasis: 'some_other_basis',
      agentSplit: new BigNumber(0.5),
      statement: {
        premium_amount: 5000,
        commission_amount: 500,
      },
    };

    it('should return defaultBasis when calculationBasis is not "commissions_remaining"', () => {
      const result = calculator.getFinalCalcBasisWithSign(defaultParams);
      expect(result.basis.toString()).toBe('1000');
      expect(result.finalCalcBasis.toString()).toBe('500'); // 1000 * 0.5
    });

    it('should return premium_amount when calculationBasis is "premium"', () => {
      const result = calculator.getFinalCalcBasisWithSign({
        ...defaultParams,
        calculationBasis: 'premium',
      });
      expect(result.basis.toString()).toBe(
        defaultParams.statement.premium_amount.toString()
      );
      expect(result.finalCalcBasis.toString()).toBe('2500'); // 5000 * 0.5
    });

    it('should return defaultBasis for an empty calculationBasis', () => {
      const result = calculator.getFinalCalcBasisWithSign({
        ...defaultParams,
        calculationBasis: '',
      });
      expect(result.basis.toString()).toBe('1000');
      expect(result.finalCalcBasis.toString()).toBe('500'); // 1000 * 0.5
    });

    describe('when calculationBasis is "commissions_remaining"', () => {
      const basis = 'commissions_remaining';

      it('should calculate remaining commissions correctly', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          calculationBasis: basis,
        });
        expect(result.basis.toString()).toBe('300');
        expect(result.finalCalcBasis.toString()).toBe('150'); // 300 * 0.5
      });

      it('should handle zero commission_amount', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          calculationBasis: basis,
          statement: {
            ...defaultParams.statement,
            commission_amount: 0,
          },
        });
        // 0 - 200
        expect(result.basis.toString()).toBe('-200');
        expect(result.finalCalcBasis.toString()).toBe('-100'); // -200 * 0.5
      });

      it('should handle undefined commission_amount as 0', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          calculationBasis: basis,
          statement: {
            ...defaultParams.statement,
            // @ts-expect-error
            commission_amount: undefined,
          },
        });
        // 0 - 200
        expect(result.basis.toString()).toBe('-200');
        expect(result.finalCalcBasis.toString()).toBe('-100'); // -200 * 0.5
      });

      it('should handle zero runningTotal', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          calculationBasis: basis,
          runningTotal: new BigNumber(0),
        });
        // 500 - 0
        expect(result.basis.toString()).toBe('500');
        expect(result.finalCalcBasis.toString()).toBe('250'); // 500 * 0.5
      });

      it('should handle commission_amount and runningTotal being equal', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          calculationBasis: basis,
          runningTotal: new BigNumber(500),
        });
        // 500 - 500
        expect(result.basis.toString()).toBe('0');
        expect(result.finalCalcBasis.toString()).toBe('0'); // 0 * 0.5
      });
    });

    describe('agentSplit functionality', () => {
      it('should handle null agentSplit (defaults to 1)', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          agentSplit: null,
        });
        expect(result.basis.toString()).toBe('1000');
        expect(result.finalCalcBasis.toString()).toBe('1000'); // 1000 * 1 (default)
      });

      it('should handle zero agentSplit when isSalesVp is false', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          agentSplit: new BigNumber(0),
        });
        expect(result.basis.toString()).toBe('1000');
        expect(result.finalCalcBasis.toString()).toBe('0');
      });
      it('should handle zero agentSplit as 1 when isSalesVp is true', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          isSalesRep: true,
          agentSplit: new BigNumber(0),
        });
        expect(result.basis.toString()).toBe('1000');
        expect(result.finalCalcBasis.toString()).toBe('1000');
      });

      it('should handle agentSplit greater than 1', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          agentSplit: new BigNumber(2),
        });
        expect(result.basis.toString()).toBe('1000');
        expect(result.finalCalcBasis.toString()).toBe('2000'); // 1000 * 2
      });

      it('should apply agentSplit to premium calculation basis', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          calculationBasis: 'premium',
          agentSplit: new BigNumber(0.75),
        });
        expect(result.basis.toString()).toBe('5000');
        expect(result.finalCalcBasis.toString()).toBe('3750'); // 5000 * 0.75
      });

      it('should apply agentSplit to commissions_remaining calculation', () => {
        const result = calculator.getFinalCalcBasisWithSign({
          ...defaultParams,
          calculationBasis: 'commissions_remaining',
          agentSplit: new BigNumber(0.25),
        });
        expect(result.basis.toString()).toBe('300'); // 500 - 200
        expect(result.finalCalcBasis.toString()).toBe('75'); // 300 * 0.25
      });
    });
  });

  describe('getCalcBasis', () => {
    const defaultParams = {
      defaultBasis: new BigNumber(1000),
      statement: {
        premium_type: 'policy',
        premium_amount: 500,
        split_percentage: 0,
        report: {
          premium_amount: 1200,
          commissionable_premium_amount: 1000,
        },
      },
      calculation_basis: 'annual_premium',
      agentSplit: new BigNumber(0.5),
    };

    it('should return target premium when calculation_basis is target_premium', () => {
      const result = calculator.getCalcBasis({
        ...defaultParams,
        calculation_basis: 'target_premium',
      });
      expect(result.toString()).toBe(
        defaultParams.statement.report?.commissionable_premium_amount.toString()
      );
    });

    it('should return annual premium when calculation_basis is annual_premium', () => {
      const result = calculator.getCalcBasis({
        ...defaultParams,
        calculation_basis: 'annual_premium',
      });
      expect(result.toString()).toBe(
        defaultParams.statement.report?.premium_amount.toString()
      );
    });

    describe('normalized_premium calculations', () => {
      it('should return premium_amount for policy type', () => {
        const result = calculator.getCalcBasis({
          ...defaultParams,
          calculation_basis: 'normalized_premium',
          statement: {
            ...defaultParams.statement,
            premium_type: 'policy',
            premium_amount: 500,
          },
        });
        expect(result.toString()).toBe(
          defaultParams.statement.premium_amount.toString()
        );
      });

      it('should calculate normalized amount using split_percentage', () => {
        const result = calculator.getCalcBasis({
          ...defaultParams,
          calculation_basis: 'normalized_premium',
          statement: {
            ...defaultParams.statement,
            premium_type: 'split',
            premium_amount: 500,
            split_percentage: 50,
          },
        });
        expect(result.toString()).toBe((500 / (50 / 100)).toString());
      });

      it('should use agentSplit when split_percentage is 0', () => {
        const result = calculator.getCalcBasis({
          ...defaultParams,
          calculation_basis: 'normalized_premium',
          statement: {
            ...defaultParams.statement,
            premium_type: 'split',
            premium_amount: 500,
            split_percentage: 0,
          },
          agentSplit: new BigNumber(0.5),
        });
        expect(result.toString()).toBe((500 / 0.5).toString());
      });

      it('should return premium_amount when agentSplit and split_percentage are 0', () => {
        const result = calculator.getCalcBasis({
          ...defaultParams,
          calculation_basis: 'normalized_premium',
          statement: {
            ...defaultParams.statement,
            premium_type: 'split',
            premium_amount: 500,
            split_percentage: 0,
          },
          agentSplit: new BigNumber(0),
        });
        expect(result.toString()).toBe(
          defaultParams.statement.premium_amount.toString()
        );
      });
      it('should throw error for unsupported premium type', () => {
        expect(() =>
          calculator.getCalcBasis({
            ...defaultParams,
            calculation_basis: 'normalized_premium',
            statement: {
              ...defaultParams.statement,
              premium_type: 'invalid',
            },
          })
        ).toThrow('Unsupported premium type');
      });
    });

    it('returns correct calculation basis for different cases', () => {
      const defaultBasis = BigNumber(100);

      const result = calculator.getCalcBasis({
        defaultBasis,
        statement: {
          premium_type: 'policy',
          premium_amount: 200,
          split_percentage: 50,
        },
        calculation_basis: 'premium',
        agentSplit: BigNumber(0.5),
      });

      expect(result).toEqual(BigNumber(200));
    });
  });
});
