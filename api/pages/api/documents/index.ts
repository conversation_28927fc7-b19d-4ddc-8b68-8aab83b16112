import type { file_type, documents, report_data } from '@prisma/client';
import * as Sentry from '@sentry/nextjs';
import groupBy from 'lodash-es/groupBy';
import { nanoid } from 'nanoid';
import { getOrderBy, numberOrDefault } from 'common/helpers';
import {
  BadRequestException,
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Put,
  Req,
  Res,
} from 'next-api-decorators';
import type { NextApiRequest } from 'next';
import type { NextApiResponse } from 'next';
import { DocumentTypes } from 'common/constants/documents';
import type { DocumentProcessingDTO } from 'common/dto/document_processing/dto';
import { DocumentStatuses } from 'common/globalTypes';
import { virtual_type } from '@prisma/client';

import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  DataStates,
} from '@/types';
import Formatter from '@/lib/Formatter';
import { createProcessingLog } from '@/pages/api/v2/data_processing';
import { createImportsData } from '@/pages/api/data_imports';
import { container } from '@/ioc';
import { EmailerService } from '@/services/emailer';
import { calculateSkipAndTake } from '@/lib/prisma/utils';
import { BaseHandler } from '@/lib/baseHandler';
import { Guard } from '@/services/permission/decorator';
import {
  CommonAction,
  CrudAction,
  EntityType,
} from '@/services/permission/interface';
import { updateStartDateAndEndDate } from '@/lib/helpers/updateStartDateAndEndDate';
import { QueueService } from '@/services/queue';
import { DataProcessingTypes } from '@/types';
import { Queue } from '@/services/queue/types';
import dayjs from '@/lib/dayjs';
import { DocumentsCrudService } from '@/services/documents/crud';
import { isValidDateRange } from 'common/helpers/datetime';
import { getFileType } from 'common/tools/getFileType';

type Document = documents & {
  bank_total_amount: string | number;
  statement_amount: string | number;
  calc_imported_total?: number | string;
  calc_records?: number | string;
  calc_commission_total?: number | string;
  tipType?: 'success' | 'info' | 'warning';
  calc_company_name?: string;
  imports: {
    id: number;
    str_id: string;
    status: string;
    summed_total_amount: string;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    statement_total_amount: any;
  }[];
  companies: {
    id: number;
    str_id: string;
    company_name: string;
    account_id: string;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    alias_list: any[];
    created_at: string;
  };
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  extractions: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  created_by_user: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  statement_data: any;
  report_data?: report_data[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updated_by_user: any;

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  processing_log?: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  imports_log?: any;

  deleteRecords?: boolean;
  type?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  validations?: any;
  processing: boolean;
  profile_str_id?: string;
  processing_task_id?: string;
  adminMode?: boolean;
};

const emailerService = container.get<EmailerService>(EmailerService);

class Handler extends BaseHandler {
  private documentsCrudService: DocumentsCrudService;

  constructor() {
    super();
    this.documentsCrudService = container.get(DocumentsCrudService);
  }

  @Get()
  @Guard(CrudAction.READ, EntityType.DOCUMENTS)
  async getDocuments(@Req() req: ExtNextApiRequest & NextApiRequest) {
    return this.documentsCrudService.getDocuments({
      query: req.query,
      // @ts-expect-error
      account_id: req.account_id,
      fintaryAdmin: !!req.fintaryAdmin,
    });
  }

  @Post()
  @Guard([CrudAction.CREATE, CommonAction.UPLOAD], EntityType.DOCUMENTS)
  async createOne(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await createOne(req, res);
  }

  @Put()
  @Guard(CrudAction.UPDATE, EntityType.DOCUMENTS)
  async updateOne(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await updateOne(req, res);
  }

  @Patch()
  @Guard(CrudAction.UPDATE, EntityType.DOCUMENTS)
  async patchOne(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await updateOne(req, res);
  }

  @Delete()
  @Guard(CrudAction.DELETE, EntityType.DOCUMENTS)
  async deleteOne(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteOne(req, res);
  }
}

export const documentSubTableFields = {
  report_data: {
    where: {
      state: {
        in: [DataStates.ACTIVE, DataStates.GROUPED],
      },
    },
    select: {
      premium_amount: true,
    },
  },
  imports: {
    select: {
      id: true,
      str_id: true,
      status: true,
      summed_total_amount: true,
      statement_total_amount: true,
    },
  },
  companies: {
    where: {
      state: DataStates.ACTIVE,
    },
    select: {
      id: true,
      str_id: true,
      company_name: true,
      account_id: true,
      alias_list: true,
      created_at: true,
    },
  },
  extractions: {
    where: {
      state: DataStates.ACTIVE,
    },
    select: {
      id: true,
      str_id: true,
      created_at: true,
      method: true,
      status: true,
      output_format: true,
    },
  },
  created_by_user: {
    select: {
      first_name: true,
      last_name: true,
      id: true,
      str_id: true,
    },
  },
  statement_data: {
    where: {
      state: {
        in: [DataStates.ACTIVE, DataStates.GROUPED],
      },
    },
    select: {
      agent_commissions_status: true,
      agent_commissions_status2: true,
      commission_amount: true,
      premium_amount: true,
    },
  },
  updated_by_user: {
    select: {
      first_name: true,
      last_name: true,
      id: true,
      str_id: true,
    },
  },
};

const getDateWhere = ({
  startDate,
  endDate,
}: {
  startDate?: Date | null;
  endDate?: Date | null;
}) => {
  if (startDate && endDate) {
    return {
      gte: startDate,
      lt: endDate,
    };
  }
  if (startDate) {
    return {
      gte: startDate,
    };
  }
  if (endDate) {
    return {
      lt: endDate,
    };
  }
};

/**
 * @deprecated
 * Please use DocumentsCrudService -> getDocuments instead.
 * Todo: refactor where this function is used
 */
export const _getDocuments = async (req: ExtNextApiRequest) => {
  const {
    page,
    limit,
    is_dynamic_select = false,
    type,
    upload_date_start,
    upload_date_end,
    processing_date_start,
    processing_date_end,
    processing_date_empty,
    deposit_date_start,
    deposit_date_end,
    deposit_date_empty,
    orderBy = 'created_at',
    sort = 'desc',
    companies,
    sync_status,
    str_id,
    status,
  } = req.query;

  const companyStrIds = (
    Array.isArray(companies) ? companies : [companies]
  ).filter((c) => c);

  const { take, skip } = is_dynamic_select
    ? { take: 1000, skip: 0 }
    : calculateSkipAndTake({ page, limit });

  const uploadDates = updateStartDateAndEndDate(
    upload_date_start,
    upload_date_end
  );
  if (!isValidDateRange(uploadDates).isValid) {
    throw new BadRequestException('Invalid upload date range');
  }

  const processingDates = updateStartDateAndEndDate(
    processing_date_start,
    processing_date_end
  );
  if (!isValidDateRange(processingDates).isValid) {
    throw new BadRequestException('Invalid processing date range');
  }

  const depositDates = updateStartDateAndEndDate(
    deposit_date_start,
    deposit_date_end
  );
  if (!isValidDateRange(depositDates).isValid) {
    throw new BadRequestException('Invalid deposit date range');
  }

  const orderByData = getOrderBy(orderBy, sort, [
    { query_field: 'companies', db_field: 'company_name' },
    { query_field: 'statement_data', db_field: 'commission_amount' },
    { query_field: 'imports', db_field: 'summed_total_amount' },
  ]);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const where: any = {
    account_id: req.account_id,
    state: DataStates.ACTIVE,
    status: undefined,
    str_id: undefined,
    file_type: undefined,
    type: undefined,
    profile_str_id: undefined,
    sync_id: undefined,
    OR: [],
    AND: [],
  };

  const createdAtWhere = getDateWhere(uploadDates);
  if (createdAtWhere) {
    where.AND.push({
      created_at: createdAtWhere,
    });
  }

  const processingDateWhere = getDateWhere(processingDates);
  if (processingDateWhere) {
    where.AND.push({
      imported_at: processingDateWhere,
    });
  }

  const depositDateWhere = getDateWhere(depositDates);
  if (depositDateWhere) {
    where.AND.push({
      deposit_date: depositDateWhere,
    });
  }

  if (processing_date_empty === 'true') {
    where.OR.push({
      imported_at: null,
    });
  }

  if (deposit_date_empty === 'true') {
    where.OR.push({
      deposit_date: null,
    });
  }

  if (companies !== undefined) {
    where.company_str_id = { in: companyStrIds };
  }

  if (status) {
    where.status = Array.isArray(status) ? { in: status } : status;
  }

  if (req.query?.id) {
    where.str_id = {
      in: req.query.id.split(','),
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;
  }
  if (req.query?.file_type) {
    where.file_type = req.query.file_type;
  }
  if (req.query?.profile) {
    where.profile_str_id = req.query.profile;
  }
  if (sync_status) {
    where.sync_id = sync_status === 'not_synced' ? null : { not: null };
  }
  if (type) {
    where.type = type;
  }

  if (Array.isArray(str_id)) {
    where.str_id = {
      in: str_id,
    };
  }

  const _query = req.query.q as string;
  // OR search for filename, file_path, and company_name
  if (_query) {
    where.OR.push(
      {
        filename: {
          contains: _query,
          mode: 'insensitive',
        },
      },
      {
        file_path: {
          contains: _query,
          mode: 'insensitive',
        },
      },
      {
        companies: {
          company_name: {
            contains: _query,
            mode: 'insensitive',
          },
        },
      }
    );
  }

  const [data, count] = await Promise.all([
    prisma.documents.findMany({
      where,
      skip,
      take,
      orderBy: {
        ...orderByData,
      },
      select: is_dynamic_select
        ? {
            id: true,
            str_id: true,
            created_at: true,
            filename: true,
            override_filename: true,
            file_path: true,
            sync_id: true,
            sync_worker: true,
            company_str_id: true,
          }
        : {
            id: true,
            str_id: true,
            created_at: true,
            created_by: true,
            bank_total_amount: true,
            check_date: true,
            company_str_id: true,
            deposit_date: true,
            file_hash: true,
            file_path: true,
            file_type: true,
            filename: true,
            import_id: true,
            imported_at: true,
            mapping: true,
            method: true,
            notes: true,
            override_file_path: true,
            override_filename: true,
            override_mapping: true,
            processor: true,
            profile_str_id: true,
            prompt: true,
            status: true,
            sync_id: true,
            sync_worker: true,
            tag: true,
            type: true,
            statement_amount: true,
            statement_month: true,
            payment_method: true,
            validations: true,
            upload_source: true,
            process_method: true,
            processing_task_id: true,
            ...documentSubTableFields,
          },
    }),
    prisma.documents.count({
      where,
    }),
  ]);

  let dataWithProcessingNotes = data;

  if (!is_dynamic_select) {
    const taskIds = data
      // @ts-expect-error
      .filter((doc) => doc.processing_task_id)
      // @ts-expect-error
      .map((doc) => doc.processing_task_id);

    let processingMap = new Map<string, { notes?: string }>();

    if (taskIds.length > 0) {
      try {
        const processingTasks = await prisma.data_processing.findMany({
          where: {
            str_id: { in: taskIds },
            state: DataStates.ACTIVE,
          },
          select: {
            str_id: true,
            notes: true,
          },
        });

        processingMap = new Map(
          // @ts-expect-error
          processingTasks.map((task) => [task.str_id, task])
        );
      } catch (error) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.warn('Failed to fetch processing notes:', error);
        Sentry.captureException(error, {
          extra: { message: 'Failed to fetch processing notes' },
        });
      }
    }

    // @ts-expect-error
    dataWithProcessingNotes = data.map((doc) => ({
      ...doc,
      processing_notes: doc.processing_task_id
        ? processingMap.get(doc.processing_task_id)?.notes
        : null,
    }));
  }

  const formatData = formatDocumentsData(
    dataWithProcessingNotes,
    !!req.fintaryAdmin
  );
  return { data: formatData, count };
};

export const formatDocumentsData = (
  data: Document[],
  isFintaryAdmin: boolean = false
) => {
  return data.map((d) => {
    const _datum = { ...d };
    _datum.calc_company_name = _datum.companies?.company_name;

    const filteredStatementData =
      _datum.statement_data?.filter(
        // @ts-expect-error
        (item) =>
          !(item.is_virtual && item.virtual_type === virtual_type.grouped)
      ) || [];

    const groupedObj = groupBy(filteredStatementData, (item) => {
      return item.agent_commissions_status || 'NO_STATUS';
    });
    const totalCommission = _datum.statement_data?.reduce(
      // @ts-expect-error
      (acc, item) => acc + +item.commission_amount,
      0
    );
    const totalCount = _datum.statement_data?.length ?? 0;
    const groupedCountInfo = {};
    const groupedCommissionInfo = {};
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.keys(groupedObj).forEach((key) => {
      const totalCommissionForKey = groupedObj[key].reduce(
        (acc, item) => acc + +item.commission_amount,
        0
      );
      const totalCountForKey = groupedObj[key].length;
      // @ts-expect-error
      groupedCountInfo[key] = totalCountForKey;
      // @ts-expect-error
      groupedCommissionInfo[key] = totalCommissionForKey;
    });
    _datum.statement_data = {
      groupedCountInfo: groupedCountInfo,
      groupedCommissionInfo: groupedCommissionInfo,
      total_commission: totalCommission,
      total_count: totalCount,
    };

    if (_datum.type === DocumentTypes.REPORT) {
      const filteredReportData =
        _datum.report_data?.filter(
          (item) =>
            !(item.is_virtual && item.virtual_type === virtual_type.grouped)
        ) || [];

      _datum.statement_data.total_count = filteredReportData.length;
      _datum.statement_data.total_commission = filteredReportData.reduce(
        (prev, cur) => {
          return prev + (cur.premium_amount?.toNumber() || 0);
        },
        0
      );
    }

    // Calculate the imported total field
    const list = _datum.imports;
    // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (list && list.length) {
      const target = list[0];
      _datum.calc_imported_total = Formatter.currency(
        target?.summed_total_amount
      );
    }
    // Calculate the records field
    const groupedCountInfoStrList: string[] = [];
    const groupedCommissionInfoStrList: string[] = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(groupedCountInfo).forEach(([key, value]) => {
      if (key !== 'NO_STATUS') {
        groupedCountInfoStrList.push(`${key}: ${value}`);
      }
    });
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(groupedCommissionInfo).forEach(([key, value]) => {
      if (key !== 'NO_STATUS') {
        groupedCommissionInfoStrList.push(
          `${key}: ${Formatter.currency(value)}`
        );
      }
    });
    let records = '';
    if (totalCount) {
      records = `${totalCount}`;
      if (groupedCountInfoStrList.length) {
        records += ` (${groupedCountInfoStrList.join(', ')})`;
      }
    }
    if (totalCommission) {
      records += `\n${Formatter.currency(totalCommission)}`;
      if (groupedCommissionInfoStrList.length) {
        records += ` (${groupedCommissionInfoStrList.join(', ')})`;
      }
    }
    _datum.calc_records = records;

    // Calculate the commission total field
    let commissionTotal = '';
    const statementAmount = numberOrDefault(_datum.statement_amount, null, {
      toFixed: 2,
    });
    const totalCommissionAmount = numberOrDefault(totalCommission, null, {
      toFixed: 2,
    });
    const statementTotalAmount = numberOrDefault(
      _datum.imports?.[0]?.statement_total_amount,
      null,
      { toFixed: 2 }
    );
    if (
      _datum.status === DocumentStatuses.NEW ||
      _datum.status === DocumentStatuses.PROCESSING ||
      _datum.status === DocumentStatuses.PENDING_REVIEW
    ) {
      commissionTotal = `Statement amount: ${Formatter.currency(statementAmount) || 'n/a'}`;
    } else if (_datum.status === DocumentStatuses.PROCESSED) {
      const isEqualStatementAndCommissionTotalValid =
        statementAmount === totalCommissionAmount && statementAmount;
      const isEqualStatementAndCommissionTotalInValid =
        statementAmount === totalCommissionAmount && !statementAmount;
      const isNotEqualStatementAndCommissionTotalValid =
        statementAmount !== totalCommissionAmount &&
        statementAmount &&
        totalCommissionAmount;

      const isOnlyExistStatementTotal =
        statementAmount && !totalCommissionAmount && !statementTotalAmount;
      const isOnlyExistCommissionTotal =
        totalCommissionAmount && !statementAmount && !statementTotalAmount;

      if (isEqualStatementAndCommissionTotalValid) {
        // ✅
        commissionTotal = `${Formatter.currency(totalCommissionAmount)}`;
        _datum.tipType = 'success';
      } else if (
        isEqualStatementAndCommissionTotalInValid ||
        isOnlyExistStatementTotal ||
        isNotEqualStatementAndCommissionTotalValid
      ) {
        // ❌
        _datum.tipType = 'warning';
        commissionTotal = `Commission total: ${
          Formatter.currency(totalCommissionAmount) || 'n/a'
        }\nStatement amount: ${
          Formatter.currency(statementAmount) || 'n/a'
        }\nStatement total: ${
          Formatter.currency(statementTotalAmount) || 'n/a'
        }`;
      } else if (isOnlyExistCommissionTotal) {
        // ℹ️
        _datum.tipType = 'info';
        commissionTotal = `Commission total: ${Formatter.currency(
          totalCommissionAmount
        )}`;
      }
    }
    _datum.calc_commission_total = commissionTotal || 0;

    if (!isFintaryAdmin) {
      if (_datum.status === DocumentStatuses.PENDING_REVIEW) {
        _datum.status = DocumentStatuses.PROCESSING;
      }
    }

    return _datum;
  });
};

const createOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as Document;
  try {
    // @ts-expect-error
    const path = (body.override_file_path || body.file_path).toLowerCase();
    const { company_str_id: _company_str_id, ...rest } = body;

    if (
      !path.toLowerCase().startsWith(`uploads/${req.account_id}/`.toLowerCase())
    ) {
      const error = new Error(`Illegal file path: ${path}`);
      Sentry.captureException(error);
      throw error;
    }

    const fileType = getFileType(path);

    const account = await prisma.accounts.findUnique({
      where: { str_id: req.account_id },
      include: {
        user: true,
      },
    });
    const documentTypes = {
      statement: 'Commission statement',
      report: 'Policy report',
    };
    // @ts-expect-error
    const documentType = documentTypes[body.type] ?? 'Unknown';

    const data = await prisma.documents.create({
      data: {
        ...rest,
        str_id: nanoid(),
        uid: req.uid,
        account: { connect: { str_id: req.account_id } },
        companies: body.company_str_id
          ? { connect: { str_id: body.company_str_id } }
          : undefined,
        created_by_user: {
          connect: { uid: req.uid },
        },
        check_date: body.check_date ? new Date(body.check_date) : undefined,
        deposit_date: body.deposit_date
          ? new Date(body.deposit_date)
          : undefined,
        created_proxied_by: req.ouid,
        file_type: fileType,
      },
      include: {
        companies: {
          where: { state: DataStates.ACTIVE },
        },
      },
    });

    if (account?.user?.email && !account.user.email.endsWith('@fintary.com')) {
      await emailerService.sendEmail(
        [
          '<EMAIL>',
          '<EMAIL>',
        ],
        `New document: ${documentType} for ${account?.name}`,
        `New document: ${documentType} for ${account?.name}.<br /><br /><a href="https://app.fintary.com/admin/documents?id=${data.str_id}">Review this document</a><br /><br /><a href="https://app.fintary.com/admin/documents?qc=new">Review all unprocessed documents</a>`
      );
    }

    res.status(201).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('error', error);
    Sentry.captureException(error);
    // @ts-expect-error
    res.status(500).json({ error: error.message });
  }
};

const updateOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as Document;
  try {
    const {
      id: _id,
      created_by: _created_by,
      updated_by: _updated_by,
      company_str_id,
      profile_str_id,
      file_type,
      bank_total_amount,
      statement_amount,
      processing_log,
      imports_log,
      processing = false,
      adminMode = false,
      ...rest
    } = body;

    const currentDocument = await prisma.documents.findUnique({
      where: { id: body.id, account_id: req.account_id },
      select: {
        company_str_id: true,
        type: true,
        status: true,
      },
    });

    if (!currentDocument) {
      throw new Error('Document not found');
    }

    const account = await prisma.accounts.findUnique({
      where: { str_id: req.account_id },
      include: {
        user: true,
      },
    });

    if (rest.statement_month) {
      rest.statement_month = dayjs
        .utc(rest.statement_month)
        .startOf('month')
        .toDate();
    }

    const whereCondition = adminMode
      ? { id: body.id }
      : { id: body.id, account_id: req.account_id };

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const bodys: any = {
      ...rest,
      uid: req.uid,
      ...(adminMode
        ? {}
        : { account: { connect: { str_id: req.account_id } } }),
      file_type: file_type as file_type,
      companies: company_str_id
        ? { connect: { str_id: company_str_id } }
        : undefined,
      profiles: profile_str_id
        ? { connect: { str_id: profile_str_id } }
        : { disconnect: true },
      check_date: body.check_date ? new Date(body.check_date) : undefined,
      deposit_date: body.deposit_date ? new Date(body.deposit_date) : undefined,
      updated_by_user: {
        connect: { uid: req.uid },
      },
      updated_at: new Date(),
    };

    if ('bank_total_amount' in body) {
      bodys.bank_total_amount =
        bank_total_amount === null
          ? null
          : parseFloat(bank_total_amount.toString().replace(',', ''));
    }
    if ('statement_amount' in body) {
      bodys.statement_amount =
        statement_amount === null
          ? null
          : parseFloat(statement_amount.toString().replace(',', ''));
    }

    const data = await prisma.documents.update({
      where: whereCondition,
      data: bodys,
      include: {
        companies: {
          where: { state: DataStates.ACTIVE },
        },
      },
    });
    if (data.processor) {
      // Find the processor and update the status to processed
      const targetProcessor = await prisma.processors.findUnique({
        where: { str_id: data.processor, state: DataStates.ACTIVE },
      });
      if (!targetProcessor)
        throw new Error(`Processor not found: ${data.processor}`);
      await prisma.processors.update({
        where: { id: targetProcessor.id },
        data: {
          processor_status: 'processed',
          updated_at: new Date(),
        },
      });
    }

    if (processing_log) {
      req.logger.info('Create a processing log', processing_log);
      await createProcessingLog(req, processing_log);
    }

    if (imports_log) {
      req.logger.info('Create an imports_data log', imports_log);

      if (imports_log.feedback) {
        await emailerService.sendEmail(
          '<EMAIL>',
          'Document processing feedback',
          `<div>
            <p>From: <strong>${account?.name || 'Unknown account'}</strong></p>
            <p>Feedback: ${imports_log.feedback}</p>
            <p>Processor: ${data.processor ?? 'n/a'}</p>
            <p>uid: ${req.uid}, ouid: ${req.ouid}</p>
          </div>`
        );
      }

      await createImportsData(req, imports_log);
    }

    const shouldTriggerAutoProcessing = () => {
      const currentCompanyId = company_str_id || data.company_str_id;
      const currentType = rest.type || data.type;
      const hasRequiredFields = currentCompanyId && currentType;

      if (processing && hasRequiredFields) {
        return true;
      }

      if (data.status === DocumentStatuses.NEW && hasRequiredFields) {
        const previouslyMissingCompany =
          !currentDocument.company_str_id && company_str_id;
        const previouslyMissingType = !currentDocument.type && rest.type;
        if (previouslyMissingCompany || previouslyMissingType) {
          return true;
        }
      }

      return false;
    };

    if (shouldTriggerAutoProcessing()) {
      const queueService = container.get<QueueService>(QueueService);
      // @ts-expect-error
      await queueService.createTask<DocumentProcessingDTO>({
        account: {
          account_id: req.account_id,
          uid: req.uid,
          ouid: req.ouid,
          role_id: req.role_id,
        },
        queue: Queue.DOCUMENT_PROCESSING,
        type: DataProcessingTypes.document_processing,
        url: process.env.CLOUD_TASKS_URL,
        payload: {
          account_id: req.account_id,
          uid: req.uid,
          document_id: data.id,
          document_str_id: data.str_id,
          file_name: data.filename,
          file_path: data.file_path,
          file_type: data.file_type,
          type: data.type,
          companies: data.companies,
          statement_amount: data.statement_amount,
        },
      });

      req.logger.info('Auto-processing triggered for document', {
        document_id: data.str_id,
        reason: processing ? 'explicit_processing_request' : 'fields_completed',
        company_id: company_str_id || data.company_str_id,
        type: rest.type || data.type,
      });
    }

    res.status(200).json(data);
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('error', error);
    Sentry.captureException(error);
    // @ts-expect-error
    res.status(500).json({ error: error.message });
  }
};

const deleteOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as Document;

  try {
    // @ts-expect-error
    await prisma.$transaction(async (prisma) => {
      // Delete related records if needed
      if (body.deleteRecords) {
        const updateData = {
          where: {
            document_id: String(body.str_id),
            account_id: String(req.account_id),
          },
          data: {
            state: DataStates.DELETED,
          },
        };

        if (body.type === DocumentTypes.STATEMENT) {
          await prisma.statement_data.updateMany(updateData);
        } else if (body.type === DocumentTypes.REPORT) {
          await prisma.report_data.updateMany(updateData);
        } else {
          throw new Error(`Unsupported document type: ${body.type}`);
        }
      }

      await prisma.documents.update({
        where: {
          id: body.id,
          account_id: req.account_id,
        },
        data: {
          state: DataStates.DELETED,
        },
      });
    });

    res.status(200).json({ message: 'success' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('error', error);
    Sentry.captureException(error);
    // @ts-expect-error
    res.status(500).json({ error: error.message });
  }
};

export default withAuth(createHandler(Handler));
