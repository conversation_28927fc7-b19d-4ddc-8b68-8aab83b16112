import * as Sentry from '@sentry/nextjs';
import type { NextApiRequest, NextApiResponse } from 'next';
import { <PERSON>, Req, <PERSON><PERSON>, createHandler } from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import { _getData as getAccountingTransactionsData } from '@/pages/api/accounting/transactions/[[...params]]';
import { _getData as getCompGridData } from '@/pages/api/comp-grids';
import { _getData as getCompGridCriteriaData } from '@/pages/api/comp-grids/criteria/[[...params]]';
import { _getData as getCompGridProductsData } from '@/pages/api/comp-grids/products/[[...params]]';
import { _getData as getCompGridRatesData } from '@/pages/api/comp-grids/rates/[[...params]]';
import { _getData as _getCompaniesData } from '@/pages/api/companies';
import { _getData as _getProductsData } from '@/pages/api/companies/products';
import { _getContactsGroupsData } from '@/pages/api/contacts/groups';
import { _getDocuments } from '@/pages/api/documents';
import { _getReconciliers } from '@/pages/api/reconcilers';
import { _getReportData } from '@/pages/api/report_data';
import { _getRoles } from '@/pages/api/roles';
import { _getData as _getSavedReports } from '@/pages/api/saved_reports';
import { _getData as _getReportGroups } from '@/pages/api/saved_reports/groups';
import { _getCompProfileSetsData } from '@/pages/api/schedules/comp-profile-sets/[[...params]]';
import { _getCompProfileData } from '@/pages/api/schedules/comp-profiles/[[...params]]';
import { _getAccUsers } from '@/pages/api/users/get_account_users';
import { _getAdminProcessorsData } from '@/pages/api/admin/processors';
import { _getProcessorsData } from '@/pages/api/processors';
import { _getDocumentProfiles } from '@/pages/api/document_profiles';
import { ContactsQueries } from '@/queries/contactsQueries';
import { ContactService } from '@/services/contact';
import { DataUpdateDynamicSelects } from '@/services/data-update/dynamic_selects';
import { SettingsService } from '@/services/settings';
import { UserService } from '@/services/user';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { getCustomViewProducer } from '../accounts/settings/[[...params]]';
import { getCompGridLevelsDynamicSelects } from '@/pages/api/comp-grids/levels/[[...params]]';
import { CompaniesService } from '@/services/companies';

export class DynamicSelectsHandler extends BaseHandler {
  private contactService: ContactService;
  private userService: UserService;
  private settingsService: SettingsService;
  private contactsQueries: ContactsQueries;
  private dataUpdateDynamicSelects: DataUpdateDynamicSelects;
  private companiesService: CompaniesService;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private tables: any;

  constructor() {
    super();
    this.contactService = container.get<ContactService>(ContactService);
    this.userService = container.get<UserService>(UserService);
    this.settingsService = container.get<SettingsService>(SettingsService);
    this.contactsQueries = container.get<ContactsQueries>(ContactsQueries);
    this.dataUpdateDynamicSelects = container.get<DataUpdateDynamicSelects>(
      DataUpdateDynamicSelects
    );
    this.companiesService = container.get<CompaniesService>(CompaniesService);
    this.tables = {
      'accounting/transactions': getAccountingTransactionsData,
      'commission_schedules/agent_commission_schedule_profiles':
        _getCompProfileData,
      'comp-grids': getCompGridData,
      'comp-grids/criteria': getCompGridCriteriaData,
      'comp-grids/levels': getCompGridLevelsDynamicSelects,
      'comp-grids/products': getCompGridProductsData,
      'comp-grids/rates': getCompGridRatesData,
      companies: _getCompaniesData,
      'companies?admin_mode=true?all=1': _getCompaniesData,
      'companies/products': _getProductsData,
      contacts: this.contactsQueries._getBasicData,
      'contacts/groups': _getContactsGroupsData,
      documents: _getDocuments,
      reconcilers: _getReconciliers,
      report_data: _getReportData,
      roles: _getRoles,
      'saved_reports/groups': _getReportGroups,
      'saved_reports?include_grouped=true': _getSavedReports,
      'schedules/comp-profiles': _getCompProfileData,
      'users/get_account_users?state=active': _getAccUsers,
      'users/get_account_users?state=active&admin=true': _getAccUsers,
      'schedules/comp-profiles/sets': _getCompProfileSetsData,
      'data-update/criteria':
        this.dataUpdateDynamicSelects._getDataUpdateCriteria,
      'data-update/actions':
        this.dataUpdateDynamicSelects._getDataUpdateActions,
      'data-update/groups': this.dataUpdateDynamicSelects._getDataUpdateGroups,
      processors: _getProcessorsData,
      'admin/processors': _getAdminProcessorsData,
      document_profiles: _getDocumentProfiles,
      account_role_settings_producer: getCustomViewProducer,
    };
  }

  /**
   * This endpoint dynamically selects and calls a data handler based on the request body.
   *
   * Note: While most handlers expect only a single argument (the request), this endpoint
   * always calls them with multiple arguments: req, res, settingsService, contactService,
   * userService, companiesService, and queryNestedContacts. Handlers that don't need extra
   * arguments can simply ignore them.
   *
   * To add a new handler, add it to the `tables` object in the constructor. Ensure your handler can accept
   * (and ignore, if necessary) the extra arguments passed by this endpoint.
   */
  @Post()
  async dynamicSelectsData(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    try {
      const response = await this.handleDynamicSelectsData(req, res);
      res.json(response);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error: ', error);
      Sentry.captureException(error);
      // @ts-expect-error
      res.status(500).json({ error: error.message });
    }
  }

  async handleDynamicSelectsData(
    req: NextApiRequest & ExtNextApiRequest,
    res: ExtNextApiResponse & NextApiResponse
  ) {
    const queryNestedContacts = this.queryNestedContacts;
    const body = req.body;
    const response = [];
    for (const index in body) {
      const reqCopy = { ...req, query: { ...req.query } };

      if (
        !Object.keys(this.tables).includes(body[index].table) ||
        !this.tables[body[index].table]
      ) {
        throw new Error(`Data source not found: ${body[index].table}`);
      }

      // Edge case for contact_group_id_add we set it to null to know that we are creating a brand new contact group
      // body[index].queryParamValue only has a value if we are updating an existing contact group
      if (body[index].queryParamName === 'contact_group_id_add') {
        // @ts-expect-error
        reqCopy.query[body[index].queryParamName] = null;
      }

      if (body[index].queryParamValue && body[index].queryParamName) {
        reqCopy.query[body[index].queryParamName] = body[index].queryParamValue;
      }

      if (body[index].queryParamTable && body[index].queryParamNameTable) {
        reqCopy.query[body[index].queryParamNameTable] =
          body[index].queryParamTable;
      }

      if (body[index].table === 'saved_reports?include_grouped=true') {
        // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        reqCopy.query['include_grouped'] = 'true';
      }

      if (body[index].table === 'users/get_account_users?state=active') {
        // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        reqCopy.query['state'] = 'active';
      }

      if (
        body[index].table === 'users/get_account_users?state=active&admin=true'
      ) {
        // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        reqCopy.query['state'] = 'active';
        // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        reqCopy.query['admin'] = 'true';
      }

      if (body[index].table === 'companies?admin_mode=true?all=1') {
        // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        reqCopy.query['admin_mode'] = 'true';
        // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        reqCopy.query['all'] = '1';
      }

      reqCopy.query.is_dynamic_select = 'true';

      const data = await this.tables[body[index].table](
        reqCopy,
        res,
        this.settingsService,
        this.contactService,
        this.userService,
        this.companiesService,
        queryNestedContacts
      );
      response.push({ [body[index].table]: data });
    }
    return response;
  }
}

export default withAuth(createHandler(DynamicSelectsHandler));
