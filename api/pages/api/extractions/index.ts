import * as Sentry from '@sentry/nextjs';
import { extractions_status } from '@prisma/client';
import { nanoid } from 'nanoid';

import { isValidId } from '@/lib/prisma/utils';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import {
  DataStates,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
} from '@/types';

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await getExtractions(req, res);
      break;
    case 'POST':
      await postExtraction(req, res);
      break;
    case 'PATCH':
    case 'PUT':
      await patchExtraction(req, res);
      break;
    case 'DELETE':
      await deleteExtraction(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export default withAuth(handler);

export const config = {
  api: {
    responseLimit: false,
  },
};

const getExtractions = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const where = {
    account_id: req.account_id,
    state: 'active',
    str_id: undefined,
    document_id: undefined,
  };
  if (req.query?.id) {
    // @ts-expect-error
    where.str_id = req.query.id;
  }
  if (req.query?.document_id) {
    // @ts-expect-error
    where.document_id = Number(req.query.document_id);
  }

  const data = await prisma.extractions.findMany({
    where,
    select: {
      id: true,
      str_id: true,
      created_at: true,
      document_id: true,
      method: true,
      result: true,
      status: true,
      // Output: true,
      output_format: true,
      documents: {
        select: {
          id: true,
          company_str_id: true,
          type: true,
          file_type: true,
          str_id: true,
          created_at: true,
          override_file_path: true,
          file_path: true,
          filename: true,
        },
      },
    },
    orderBy: {
      created_at: 'desc',
    },
  });
  res.json(data);
};

const postExtraction = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const body = req.body;
  try {
    const data = await prisma.extractions.create({
      data: {
        ...body,
        account_id: req.account_id,
        uid: req.uid,
        str_id: nanoid(),
      },
    });
    res.status(201).json(data);
  } catch (error) {
    // @ts-expect-error
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error: ${error.message}`);
    Sentry.captureException(error);
    // @ts-expect-error
    res.status(500).json({ error: error.message });
  }
};

const patchExtraction = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const body = req.body;
  try {
    if (!Number.isFinite(body.id)) {
      throw new Error('Invalid or missing extraction ID');
    }

    const queryByAccountId =
      req.account_id && !req.fintaryAdmin
        ? { account_id: String(req.account_id) }
        : {};

    const data = await prisma.extractions.update({
      where: {
        id: Number(body.id),
        ...queryByAccountId,
      },
      data: {
        ...body,
        status: extractions_status.extracted,
        updated_at: new Date(),
      },
    });
    res.status(200).json(data);
  } catch (error) {
    // @ts-expect-error
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error: ${error.message}`);
    Sentry.captureException(error);
    // @ts-expect-error
    res.status(500).json({ error: error.message });
  }
};

const deleteExtraction = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0 || ids.some(isValidId)) {
      return res
        .status(400)
        .json({ message: 'Found invalid id before performing the operation' });
    }

    const promises = ids.map(async (id) => {
      try {
        await prisma.extractions.update({
          where: { id: Number(id), account_id: String(req.account_id) },
          data: {
            state: DataStates.DELETED,
          },
        });
      } catch (error) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(
          `Error on updating extraction, id: ${id}, account_id: ${req.account_id}, ${error}`
        );
        Sentry.captureException(error);
        throw error;
      }
    });

    await prisma.$transaction(promises);
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting company: ${error}`);
    Sentry.captureException(error);
    // @ts-expect-error
    res.status(500).json({ error: error.message });
  }
};
