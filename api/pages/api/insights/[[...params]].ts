import * as Sentry from '@sentry/nextjs';
import { DEFAULT_FILTER, WidgetGroup } from 'common/constants';
import * as dto from 'common/dto/insights';
import { dateOrDefault, setNextDay } from 'common/helpers';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Body,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { Timezone, ZodBody } from '@/lib/decorators';
import Formatter from '@/lib/Formatter';
import { parseDate } from '@/lib/helpers';
import { withAuth } from '@/lib/middlewares';
import { ContactsQueries } from '@/queries/contactsQueries';
import { DashboardService } from '@/services/dashboard';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';
import { UserService } from '@/services/user';
import { WidgetService } from '@/services/widget';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  Roles,
} from '@/types';
import { WidgetDefinitionSchema } from 'common/dto/widgets';

class Handler extends BaseHandler {
  private userService: UserService;
  private contacstQuery: ContactsQueries;
  private dashboardService: DashboardService;
  private widgetService: WidgetService;

  constructor() {
    super();
    this.contacstQuery = container.get<ContactsQueries>(ContactsQueries);
    this.dashboardService = container.get<DashboardService>(DashboardService);
    this.userService = container.get<UserService>(UserService);
    this.widgetService = container.get<WidgetService>(WidgetService);
  }

  @Get()
  @Guard(CrudAction.READ, EntityType.INSIGHTS)
  async getInsights(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Timezone() timezone: string
  ) {
    const {
      query: {
        agent_group,
        agent,
        unselected_agent,
        compensation_type,
        tag,
        end_date,
        policy_status,
        product_type,
        start_date,
        dashboard,
        view_as,
        include_blanks,
        is_preview,
      },
    } = req;
    const startTime = Date.now();
    if (is_preview) {
      const widgetMockData = await this.widgetService.getInsightsPreviewData(
        // @ts-expect-error
        req.account_id
      );
      const widgets = widgetMockData?.widgets ?? [];
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const response: any = {
        dashboardLabel: 'Preview',
        boxes: widgets.filter(
          (widget) => widget.widgetGroup === WidgetGroup.BOX
        ),
        charts: widgets
          .filter((widget) => widget.widgetGroup === WidgetGroup.CHART)
          // @ts-expect-error
          .sort((a, b) => a.order - b.order),
        tables: widgets.filter(
          (widget) => widget.widgetGroup === WidgetGroup.TABLE
        ),
      };
      return res.json(response);
    }

    const sharedWidgets = await this.widgetService.getAccountWidgetList(
      // @ts-expect-error
      req.account_id
    );

    let contact_str_id = null;
    // Get contact_str_id from user if the user is producer
    if (req.role_id === (Roles.PRODUCER as unknown as string)) {
      // @ts-expect-error
      const user = await this.userService.getUserByUid(req.uid);
      contact_str_id = user?.user_contact[0]?.str_id;
      if (!user || !contact_str_id) {
        return res.status(404).json({ message: 'User/contact not found' });
      }
    }
    const dateOfLast2Months = new Date();
    dateOfLast2Months.setMonth(dateOfLast2Months.getMonth() - 2);
    // Input validation
    const startDate = parseDate(start_date) || dateOfLast2Months;
    const endDate =
      // @ts-expect-error
      setNextDay(dateOrDefault(end_date, undefined)) || new Date();
    this.widgetService.init({
      role_id: req.role_id,
      uid: req.uid,
      account_id: req.account_id,
      agent_group,
      agent,
      unselected_agent,
      compensation_type,
      tag,
      policy_status,
      product_type,
      startDate,
      endDate,
      contact_str_id,
      include_blanks: include_blanks === 'true',
      timezone,
    });

    let dashboardSettings = await this.dashboardService.getDashboardSettings(
      // @ts-expect-error
      req.account_id,
      dashboard
    );
    // Set default widgets if no dashboard settings found
    if (!dashboardSettings && !dashboard) {
      dashboardSettings = await this.dashboardService.storeDashboardToDB({
        account_id: req.account_id,
        widgetsSettings: {
          [Roles.ACCOUNT_ADMIN]: (sharedWidgets ?? []).map(
            (widget) => widget.id
          ),
          [Roles.PRODUCER]: (sharedWidgets ?? []).map((widget) => widget.id),
          [Roles.DATA_SPECIALIST]: (sharedWidgets ?? []).map(
            (widget) => widget.id
          ),
        },
      });
    }

    let widgetsToLoad =
      dashboardSettings?.widgets[view_as] ??
      // @ts-expect-error
      dashboardSettings?.widgets[req.role_id] ??
      [];
    // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (req.role_id == Roles.PRODUCER.toString()) {
      widgetsToLoad = dashboardSettings?.widgets[Roles.PRODUCER] ?? [];
    }

    const result = await this.widgetService.getWidgets(widgetsToLoad);

    // Merge account widgets with default widgets if the accountWidgets is not empty
    const widgets = result?.widgets ?? [];
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const response: any = {
      dashboardLabel: dashboardSettings?.label,
      boxes: widgets.filter((widget) => widget.widgetGroup === WidgetGroup.BOX),
      charts: widgets
        .filter((widget) => widget.widgetGroup === WidgetGroup.CHART)
        // @ts-expect-error
        .sort((a, b) => a.order - b.order),
      tables: widgets.filter(
        (widget) => widget.widgetGroup === WidgetGroup.TABLE
      ),
      filterValues: {
        ...(result?.filters ?? {}),
      },
      dashboardSettings: dashboardSettings?.widgets,
      dashboardId: dashboardSettings?.id,
      sharedWidgets,
      accessRoles: dashboardSettings?.access_roles,
    };
    if (result?.dashboard_filter_by_agent) {
      const contactsQueryResult = await this.contacstQuery._getData(req);

      const contactsMap = Object.fromEntries(
        contactsQueryResult.data.map((contact) => [
          contact.str_id,
          // @ts-expect-error
          Formatter.contact(contact, { account_id: req.account_id }),
        ])
      );
      const agentsFilterOptions = [
        { id: '-1', name: DEFAULT_FILTER.BLANK_OPTION },
        // Expand contacts map set, use the key as id, value as name
        ...Object.entries(contactsMap).map(([id, name]) => ({ id, name })),
      ];
      response.filterValues.agent = agentsFilterOptions;
    }
    const endTime = Date.now();
    const loadTimeInSeconds = (endTime - startTime) / 1000;
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.log(
      `Dashboard load time for account_id ${req.account_id}: ${loadTimeInSeconds} seconds`
    );
    // Sentry send message if the load time is more than 60 seconds
    if (loadTimeInSeconds > 60) {
      Sentry.captureMessage(
        `Dashboard load time for account_id ${req.account_id}: ${loadTimeInSeconds} seconds`
      );
    }

    res.json(response);
  }

  @Get('/accountWidgetsSettings')
  @Guard(CrudAction.READ, EntityType.INSIGHTS)
  async getSharedWidgets(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Query('dashboardName') dashboardName: string
  ) {
    const widgets = await this.widgetService.getAccountWidgetList(
      // @ts-expect-error
      req.account_id
    );

    const accountWidgetsSettings =
      await this.dashboardService.getDashboardSettings(
        // @ts-expect-error
        req.account_id,
        dashboardName
      );
    if (!accountWidgetsSettings) {
      return res.json({
        sharedWidgets: widgets,
        adminWidgetsSettings: [],
        producerWidgetsSettings: [],
        dataSpecialistWidgetsSettings: [],
      });
    }
    return res.json({
      sharedWidgets: widgets,
      label: accountWidgetsSettings.label,
      adminWidgetsSettings:
        accountWidgetsSettings.widgets?.[Roles.ACCOUNT_ADMIN] || [],
      producerWidgetsSettings:
        accountWidgetsSettings.widgets?.[Roles.PRODUCER] || [],
      dataSpecialistWidgetsSettings:
        accountWidgetsSettings.widgets?.[Roles.DATA_SPECIALIST] || [],
    });
  }

  @Post('/accountWidgetsSettings')
  @Guard(CrudAction.CREATE, EntityType.INSIGHTS)
  async saveWidgetSettingForAccount(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Body()
    body: {
      adminWidgetsSettings: string[];
      producerWidgetsSettings: string[];
      dataSpecialistWidgetsSettings: string[];
      dashboardName?: string;
    }
  ) {
    const {
      adminWidgetsSettings,
      producerWidgetsSettings,
      dataSpecialistWidgetsSettings,
      dashboardName,
    } = body;

    const result = await this.dashboardService.storeDashboardToDB({
      label: dashboardName,
      account_id: req.account_id,
      widgetsSettings: {
        [Roles.ACCOUNT_ADMIN]: adminWidgetsSettings,
        [Roles.PRODUCER]: producerWidgetsSettings,
        [Roles.DATA_SPECIALIST]: dataSpecialistWidgetsSettings,
      },
    });

    return res.json(result);
  }

  @Post('/layout')
  @Guard(CrudAction.CREATE, EntityType.INSIGHTS)
  async saveLayout(
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Body()
    body: {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      layout: any;
      roleId: Roles;
      dashboardId: number;
    }
  ) {
    const { layout, roleId, dashboardId } = body;
    const result = await this.dashboardService.saveDashboardLayout({
      dashboardId: dashboardId,
      role_id: roleId,
      layout: layout,
    });

    return res.json(result);
  }

  @Put('/accountWidgetsSettings')
  @Guard(CrudAction.UPDATE, EntityType.INSIGHTS)
  async updateWidgetSettingForAccount(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Body()
    body: {
      adminWidgetsSettings: string[];
      producerWidgetsSettings: string[];
      dataSpecialistWidgetsSettings: string[];
      dashboardName?: string;
      accessRoles: Roles[];
    }
  ) {
    const {
      adminWidgetsSettings,
      producerWidgetsSettings,
      dataSpecialistWidgetsSettings,
      dashboardName,
      accessRoles,
    } = body;
    // Find existing dashboard settings
    const existingDashboardSettings =
      await this.dashboardService.getDashboardSettings(
        // @ts-expect-error
        req.account_id,
        dashboardName
      );
    if (!existingDashboardSettings) {
      await this.dashboardService.storeDashboardToDB({
        label: dashboardName,
        account_id: req.account_id,
        widgetsSettings: {
          [Roles.ACCOUNT_ADMIN]: adminWidgetsSettings,
          [Roles.PRODUCER]: producerWidgetsSettings,
          [Roles.DATA_SPECIALIST]: dataSpecialistWidgetsSettings,
        },
        accessRoles,
      });
      return res.json({
        message: 'Dashboard settings not found, created new settings',
      });
    }
    const result = await this.dashboardService.updateDashboardSettings({
      name: dashboardName,
      account_id: req.account_id,
      widgetsSettings: {
        [Roles.ACCOUNT_ADMIN]: adminWidgetsSettings,
        [Roles.PRODUCER]: producerWidgetsSettings,
        [Roles.DATA_SPECIALIST]: dataSpecialistWidgetsSettings,
      },
      accessRoles,
    });

    return res.json(result);
  }

  @Put('/accountWidgetsSettings/:dashboardId')
  @Guard(CrudAction.UPDATE, EntityType.INSIGHTS)
  async updateWidgetSettingForAccountByDashboardId(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.UpdateDashboardSchema)())
    body: dto.UpdateDashboard,
    @Param('dashboardId') dashboardId: number
  ) {
    const {
      adminWidgetsSettings,
      producerWidgetsSettings,
      dataSpecialistWidgetsSettings,
      dashboardName,
      accessRoles,
    } = body;
    // Find existing dashboard settings
    const result = await this.dashboardService.updateDashboardSettingsById(
      {
        label: dashboardName,
        account_id: req.account_id,
        widgetsSettings: {
          [Roles.ACCOUNT_ADMIN]: adminWidgetsSettings,
          [Roles.PRODUCER]: producerWidgetsSettings,
          [Roles.DATA_SPECIALIST]: dataSpecialistWidgetsSettings,
        },
        accessRoles,
      },
      +dashboardId
    );

    return res.json(result);
  }

  @Post('/preview')
  @Guard(CrudAction.READ, EntityType.INSIGHTS)
  async buildWidget(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Timezone() timezone: string
  ) {
    const {
      body: {
        agent_group,
        agent,
        unselected_agent,
        compensation_type,
        end_date,
        policy_status,
        product_type,
        start_date,
        widgetDefinition,
        include_blanks,
      },
    } = req;
    const dateOfLast2Months = new Date();
    dateOfLast2Months.setMonth(dateOfLast2Months.getMonth() - 2);
    const startDate = parseDate(start_date) || dateOfLast2Months;
    const endDate =
      // @ts-expect-error
      setNextDay(dateOrDefault(end_date, undefined)) || new Date();
    this.widgetService.init({
      role_id: req.role_id,
      uid: req.uid,
      account_id: req.account_id,
      agent_group,
      agent,
      unselected_agent,
      compensation_type,
      policy_status,
      product_type,
      startDate,
      endDate,
      include_blanks: include_blanks === 'true',
      timezone,
    });
    try {
      WidgetDefinitionSchema.parse(widgetDefinition);
      const result = await this.widgetService.createWidget(widgetDefinition);
      res.json(result);
    } catch (error) {
      return res
        .status(400)
        .json({ message: 'Invalid widget definition', error });
    }
  }

  @Post('')
  @Guard(CrudAction.CREATE, EntityType.INSIGHTS)
  async saveWidget(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const {
      body: { name, spec, access },
    } = req;
    const account_id = req.account_id;
    const result = await this.widgetService.storeWidgetToDB({
      name,
      spec,
      // @ts-expect-error
      account_id,
      access,
    });
    res.json(result);
  }

  @Put('')
  @Guard(CrudAction.UPDATE, EntityType.INSIGHTS)
  async updateWidget(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const {
      body: { id, name, spec },
    } = req;
    const result = await this.widgetService.updateWidgetInDB({
      id,
      name,
      spec,
    });
    res.json(result);
  }

  @Delete('')
  @Guard(CrudAction.DELETE, EntityType.INSIGHTS)
  async deleteWidget(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Body() _body: { id: string }
  ) {
    const {
      body: { id },
    } = req;
    const result = await this.widgetService.deleteWidgetInDB(id);
    res.json(result);
  }

  @Delete('/delete_dashboard')
  @Guard(CrudAction.DELETE, EntityType.INSIGHTS)
  async deleteDashboard(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Body() _body: { dashboardName: string; dashboardId: number }
  ) {
    const {
      body: { dashboardName, dashboardId },
    } = req;
    if (!dashboardId && !dashboardName) {
      return res.status(400).json({ message: 'Invalid request' });
    }
    const result = await this.dashboardService.deleteDashboard(
      // @ts-expect-error
      req.account_id,
      dashboardName,
      dashboardId
    );
    res.json(result);
  }

  @Get('/:name')
  @Guard(CrudAction.READ, EntityType.INSIGHTS)
  async getInsightsById(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Param('name') name: string,
    @Timezone() timezone: string
  ) {
    const {
      query: {
        agent_group,
        agent,
        unselected_agent,
        compensation_type,
        end_date,
        policy_status,
        product_type,
        start_date,
      },
    } = req;
    const dateOfLast2Months = new Date();
    dateOfLast2Months.setMonth(dateOfLast2Months.getMonth() - 2);
    const startDate = parseDate(start_date) || dateOfLast2Months;
    const endDate = parseDate(end_date) || new Date();

    this.widgetService.init({
      role_id: req.role_id,
      uid: req.uid,
      account_id: req.account_id,
      agent_group,
      agent,
      unselected_agent,
      compensation_type,
      policy_status,
      product_type,
      startDate,
      endDate,
      timezone,
    });
    const result = await this.widgetService.getWidgetById(name);
    return res.json(result);
  }
}
export default withAuth(createHandler(Handler));
