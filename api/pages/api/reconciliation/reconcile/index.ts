import {
  Prisma,
  type reconcilers,
  type report_data,
  type statement_data,
} from '@prisma/client';
import * as dto from 'common/dto/reconcilers/dto';
import Formatter from 'common/Formatter';
import { numberOrDefault } from 'common/helpers';
import dayjs from 'dayjs';
import didYouMean from 'didyoumean2';
import chunk from 'lodash-es/chunk';
import isEqual from 'lodash-es/isEqual';
import { nanoid } from 'nanoid';
import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Post,
  Req,
  Res,
  UnauthorizedException,
  createHandler,
} from 'next-api-decorators';
import pRetry from 'p-retry';
import {
  ReconciliationStatusV1,
  STATEMENT_RECONCILIATION_STATUS,
} from 'common/constants/reconciliation';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { AccountInfo, ZodBody } from '@/lib/decorators';
import { BusinessException } from '@/lib/exceptionHandler';
import { isNill, limitConcurrency, runInBatch } from '@/lib/helpers';
import { matchesFilter } from '@/lib/matcher';
import { withAuth } from '@/lib/middlewares';
import { prismaClient } from '@/lib/prisma';
import {
  funcLib,
  getFieldConfigs,
  getKeyMethods,
  getScoreMethod,
} from '@/lib/reconciliation';
import { DataProcessService } from '@/services/data_processing';
import { QueueService } from '@/services/queue';
import { Queue } from '@/services/queue/types';
import {
  CommissionStatuses,
  DataProcessingStatuses,
  DataProcessingTypes,
  type ExtAccountInfo,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
} from '@/types';
import { CommonAction } from '@/services/permission/interface';
import { Guard } from '@/services/permission/decorator';
import { EntityType } from '@/services/permission/interface';
import { COMMISSION_STATUS_EDITABLE } from '@/pages/api/data_processing/commissions/commissions.constant';
import { AccountConfigService } from '@/services/account/config';
import { reconcileDataV2 } from '../reconcileV2';
import { RECONCILIATION_VERSION } from '@/constants/reconciliation-data';

const UPDATE_CONCURRENCY_LIMIT = 30;

class ReconcileDataHandler extends BaseHandler {
  accountConfigService: AccountConfigService;

  constructor() {
    super();
    this.accountConfigService =
      container.get<AccountConfigService>(AccountConfigService);
  }
  @Post()
  @Guard(CommonAction.RUN, EntityType.SETTINGS_DATA_PROCESSING)
  async reconcile(
    @AccountInfo() account: ExtAccountInfo,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.ReconcileDTOSchema)()) body: dto.ReconcileDTO
  ) {
    if (body.isSync) {
      return await selectReconcileVersion(req, res, account, body);
    }
    // If isSync is false, dispatch tasks to the queue
    const queueService = container.get<QueueService>(QueueService);
    const params = {
      account,
      type: DataProcessingTypes.reconciliation,
      queue: Queue.RECONCILIATION,
      url: process.env.CLOUD_WORKER_URL,
      payload: {
        ...body,
      },
    };
    // @ts-expect-error
    const taskId = await queueService.createTask<dto.ReconcileDTO>(params);
    return { success: true, taskId };
  }
}

export const selectReconcileVersion = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  account: ExtAccountInfo,
  body: dto.ReconcileDTO
) => {
  const accountConfigService =
    container.get<AccountConfigService>(AccountConfigService);

  const accountId = account.account_id;

  if (!accountId) {
    throw new UnauthorizedException('Account ID is required');
  }

  const accountConfig = (await accountConfigService.getConfigByType(
    accountId,
    'reconciliation'
  )) as { version: string };

  if (accountConfig?.version === RECONCILIATION_VERSION.V2) {
    return await reconcileDataV2(req, res, account, body);
  } else {
    return await reconcileData(req, res, account, body);
  }
};

const reconcileData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  account: ExtAccountInfo,
  body: dto.ReconcileDTO
) => {
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let dataProcessing;
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let startTime;
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let reconciliationStats;
  // @ts-expect-error
  if (body.isManual && (!body.policyId || !body.statementIds.length)) {
    throw new BusinessException(
      'Policy id and statement ids are required for manual reconciliation'
    );
  }
  try {
    const dataProcessingService =
      container.get<DataProcessService>(DataProcessService);
    if (!body.taskId) {
      const isLocked = await dataProcessingService.lockSyncTask({
        account,
        type: DataProcessingTypes.reconciliation,
      });
      if (!isLocked) {
        throw new BusinessException(
          'A reconciliation is already running, please try again later'
        );
      }
    }
    startTime = Date.now();
    dataProcessing = await (body.taskId
      ? dataProcessingService.updateTaskStatus({
          str_id: body.taskId,
          status: DataProcessingStatuses.PROCESSING,
        })
      : dataProcessingService.create({
          str_id: nanoid(),
          account: { connect: { str_id: account.account_id } },
          user: { connect: { uid: account.uid } },
          params: JSON.stringify(body),
          proxy_user: req.ouid ? { connect: { uid: req.ouid } } : undefined,
          type: DataProcessingTypes.reconciliation,
          status: DataProcessingStatuses.PROCESSING,
        }));
    req.logger.profile('reconcileData()');
    const { redo, testRun, flowId, documentId } = body;
    req.logger.info('options: ', { redo, testRun, flowId });
    const allData = await getAllData({
      account_id: account.account_id,
      document_str_id: documentId,
      policyId: body.policyId,
      statementIds: body.statementIds,
      isManual: body.isManual,
    });
    const {
      mode,
      fieldConfigs,
      contacts,
      reportData,
      reconcilerFlows,
      reconciliationThreshold,
    } = allData;
    let { statementData } = allData;

    const reportsTotalCount = reportData.length;
    const statementsTotalCount = statementData.length;
    const reportDataMap = reportData.reduce((acc, cur) => {
      // @ts-expect-error
      acc[cur.id] = cur;
      // @ts-expect-error
      acc[cur.str_id] = cur;
      return acc;
    }, {});

    const statementDataMap = statementData.reduce((acc, cur) => {
      // @ts-expect-error
      acc[cur.id] = cur;
      // @ts-expect-error
      acc[cur.str_id] = cur;
      return acc;
    }, {});

    const contactsMap = contacts.reduce((acc, cur) => {
      // @ts-expect-error
      acc[cur.id] = cur;
      // @ts-expect-error
      acc[cur.str_id] = cur;
      return acc;
    }, {});

    const reconciliationSettings = {
      mode,
      reconciliationThreshold,
    };

    // @ts-expect-error
    const stats = [];
    const processedData = {};
    const processedDataLogs = {};
    const statementIdsMatched = [];
    const statementMatchedReconcilers = {};
    const statementNeedUpdateStatus = {};

    // Originally designed to support multiple flows, but only running one now.
    const _flowsToUse = reconcilerFlows
      .filter((flow) => flow.state === 'active')
      .filter((flow) => (flowId ? flow.id === flowId : true));

    // @ts-expect-error
    const reconcilerFlow = reconcilerFlows.find((flow) => flow.id === +flowId);

    req.logger.info(
      `Reconciliation Flow: [${reconcilerFlow?.id}]: ${reconcilerFlow?.name || 'manual'}`
    );
    req.logger.info(`Report data length: ${reportData.length}`);
    req.logger.info(`Statement data length: ${statementData.length}`);

    req.logger.info(
      `Reconcilers: ${
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        (reconcilerFlow as any)?.reconcilers?.length
      }`
    );
    req.logger.info(`Remaining report data: ${reportData.length}`);
    req.logger.info(`Remaining statement data: ${statementData.length}`);

    if (body.isManual) {
      // @ts-expect-error
      processedData[body.policyId] = {
        // @ts-expect-error
        ...(processedData[body.policyId] ?? {}),
        statement_ids: body.statementIds,
        // @ts-expect-error
        statement_str_ids: body.statementIds.map(
          // @ts-expect-error
          (id) => statementDataMap[id].str_id
        ),
        report_id: body.policyId,
        // @ts-expect-error
        report_str_id: reportDataMap[body.policyId].str_id,
        reconciliation_methods: ['0'],
        reconciliation_status: 'matched',
      };
      // @ts-expect-error
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      body.statementIds.forEach((statementId) => {
        // @ts-expect-error
        statementMatchedReconcilers[statementId] = '0';
        // @ts-expect-error
        processedDataLogs[body.policyId] = {
          // @ts-expect-error
          ...processedDataLogs[body.policyId],
          [statementId]: {
            reconcilerId: '0',
            // @ts-expect-error
            contacts: reportDataMap[body.policyId].contacts ?? [],
          },
        };
      });
    } else {
      try {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        (reconcilerFlow as any).reconcilers.forEach(
          (reconciler: reconcilers, i: number) => {
            // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const startTime = new Date().getTime();

            const processedDataCur: {
              [key: string]: {
                statement_ids?: number[];
                statement_str_ids?: string[];
                report_ids?: number[];
                report_id?: number;
                report_str_id?: string;
                report_str_ids?: string[];
                reconciliation_status?: string;
                reconciliation_methods?: number[];
              };
            } = {};
            let statementCount = 0;
            let reportCount = 0;
            req.logger.info(`Reconciler ${i}: ${reconciler.name}`);
            const reconcilerConfig = reconciler.config;
            const matchCommissionsWithoutPolicy =
              Array.isArray(reconcilerConfig) ||
              typeof reconcilerConfig === 'string'
                ? reconcilerConfig.includes('matchCommissionsWithoutPolicy')
                : false;
            const oneCommissionOneAgent =
              body?.assignOneAgent ||
              ((Array.isArray(reconcilerConfig) ||
                typeof reconcilerConfig === 'string') &&
                reconcilerConfig.includes('oneCommissionOneAgent'));
            const reportIdsCheckedSet = new Set();
            // @ts-expect-error
            const statementIdsMatchedCur = [];
            //
            // Key method
            //
            // @ts-expect-error
            if (reconciler.method_type.toLowerCase().startsWith('key')) {
              const { getStatementKey, getPolicyKey } =
                getKeyMethods(reconciler);

              // Key statement data
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              statementData
                .filter((row) =>
                  matchesFilter(row, reconciler.filter_statement_params)
                )
                .forEach((row) => {
                  statementCount += 1;
                  const statementKey = getStatementKey(row);
                  if (!statementKey) return;

                  processedDataCur[statementKey] = {
                    ...(processedDataCur[statementKey] ?? {}),
                    statement_ids: [
                      ...(processedDataCur[statementKey]?.statement_ids ?? []),
                      row.id,
                    ],
                    statement_str_ids: [
                      // @ts-expect-error
                      ...(processedDataCur[statementKey]?.statement_str_ids ??
                        []),
                      // @ts-expect-error
                      row.str_id,
                    ],
                  };
                });

              // Key report data
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              reportData
                .filter((row) =>
                  matchesFilter(row, reconciler.filter_report_params)
                )
                .forEach((row) => {
                  reportIdsCheckedSet.add(row.id);
                  reportCount += 1;
                  const reportKey = getPolicyKey(row);
                  if (!reportKey) return;
                  // ProcessedDataCur[reportKey] = {
                  //   ...(processedDataCur[reportKey] ?? {}),
                  //   report_id: row.id,
                  //   report_str_id: row.str_id,
                  // };

                  processedDataCur[reportKey] = {
                    ...(processedDataCur[reportKey] ?? {}),
                    report_ids: [
                      ...(processedDataCur[reportKey]?.report_ids ?? []),
                      row.id,
                    ],
                    report_str_ids: [
                      // @ts-expect-error
                      ...(processedDataCur[reportKey]?.report_str_ids ?? []),
                      // @ts-expect-error
                      row.str_id,
                    ],
                  };
                });

              // Remove unmatched from processedDataCur
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.entries(processedDataCur).forEach(([k, v]) => {
                const cur = v;
                // Default matching
                if (!matchCommissionsWithoutPolicy) {
                  if (
                    !(
                      cur.statement_ids &&
                      cur.statement_ids.length > 0 &&
                      cur.report_ids &&
                      cur.report_ids.length > 0
                    )
                  ) {
                    delete processedDataCur[k];
                  } else {
                    statementIdsMatchedCur.push(...cur.statement_ids);
                  }
                } else if (matchCommissionsWithoutPolicy) {
                  // TODO: We should make this secondary, and only consider match if subsequent reconcilers don't match it
                  // For now, consider 3+ a match
                  if (!(cur.statement_ids && cur.statement_ids.length >= 3)) {
                    delete processedDataCur[k];
                  } else {
                    statementIdsMatchedCur.push(...cur.statement_ids);
                  }
                }
              });
              // @ts-expect-error
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              statementIdsMatchedCur.forEach((statementId) => {
                // @ts-expect-error
                statementMatchedReconcilers[statementId] = reconciler.id;
              });
            }

            //
            // Similarity method
            //
            // @ts-expect-error
            else if (reconciler.method_type.startsWith('similarity')) {
              const scoreMethod = getScoreMethod(reconciler);
              statementData
                .filter((row) =>
                  matchesFilter(row, reconciler.filter_statement_params)
                )
                .forEach((statementRow, _idxStatement) => {
                  statementCount += 1;

                  // if (idxStatement > 100) return;

                  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  let bestMatchRecord;
                  let bestMatchScore = 0;
                  const possibleMatches = [];
                  // console.log(
                  //   '----- Statement         ',
                  //   `${statementRow.customer_name ?? ''}::${
                  //     statementRow.writing_carrier_name ?? ''
                  //   }::${statementRow.product_type ?? ''}`
                  // );
                  reportData
                    .filter((row) =>
                      matchesFilter(row, reconciler.filter_report_params)
                    )
                    .forEach((reportRow, _idxReport) => {
                      reportIdsCheckedSet.add(reportRow.id);
                      const score = scoreMethod(
                        reportRow,
                        statementRow,
                        funcLib
                      );
                      if (score > bestMatchScore) {
                        // if (score > 0)
                        //   console.log(
                        //     `Found better match (${score}) `,
                        //     `${reportRow.customer_name ?? ''}::${
                        //       reportRow.writing_carrier_name ?? ''
                        //     }::${reportRow.product_type ?? ''}`
                        //   );
                        // TODO: Handle ties. Should be a list or reports?
                        // TODO: Also record possible matches to help user find other possible matches.
                        bestMatchRecord = reportRow;
                        bestMatchScore = score;
                      }
                      // @ts-expect-error
                      if (score > +reconciler.method_threshold_match) {
                        possibleMatches.push({
                          score,
                          report: reportRow,
                        });
                      }
                      // TODO: combine with statementRow
                      // console.log('score', score);
                    });

                  // @ts-expect-error
                  if (bestMatchScore >= +reconciler.method_threshold_match) {
                    // What should key be for scoring methods?
                    statementIdsMatchedCur.push(statementRow.id);
                    // @ts-expect-error
                    statementMatchedReconcilers[statementRow.id] =
                      reconciler.id;
                    statementIdsMatched.push(statementRow.id);
                    // @ts-expect-error
                    processedDataCur[bestMatchRecord.id] = {
                      // @ts-expect-error
                      ...(processedDataCur[bestMatchRecord.id] ?? {}),
                      reconciliation_status: 'matched',
                      reconciliation_methods: [reconciler.id],
                      // @ts-expect-error
                      report_id: bestMatchRecord.id,
                      // @ts-expect-error
                      report_str_id: bestMatchRecord.str_id,
                      statement_ids: [
                        // @ts-expect-error
                        ...(processedDataCur[bestMatchRecord.id]
                          ?.statement_ids ?? []),
                        statementRow.id,
                      ],
                      statement_str_ids: [
                        // @ts-expect-error
                        ...(processedDataCur[bestMatchRecord.id]
                          ?.statement_str_ids ?? []),
                        // @ts-expect-error
                        statementRow.str_id,
                      ],
                      // PossibleMatches
                    };
                  }
                });
              reportCount = reportIdsCheckedSet.size;
              // StatementIdsMatchedCur.forEach((statementId) => {
              //   const idx = statementData.findIndex((e) => e.id === statementId);
              //   if (idx >= 0) {
              //     statementData.splice(idx, 1);
              //   }
              // });
              // Object.entries(processedDataCur).forEach(([k, v]) => {
              //   processedData[k] = {
              //     ...(processedData[k] ?? {}),
              //     ...(v as any),
              //   };
              // });
            }

            let unmatchStatementIds = {};
            // Merge into processedData, key by original report.id so key is consistent across reconcilers
            // TODO: Up until here we only have: report_id, statement_id, statement_str_ids, reconciliation_status, and reconciliation_methods
            // so merging of statement ids requires concating the statement_ids and statement_str_ids arrays
            // Now that we consider 3+ statement keys without a policy as a match, we can't use policy id as key, so we use the key as a match with a prefix key::
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            Object.entries(processedDataCur).forEach(([k, v]) => {
              const reportIds = (v.report_ids || [v.report_id]).filter(
                (r) => r
              );
              const reportStrIds = (
                v.report_str_ids || [v.report_str_id]
              ).filter((r) => r);
              // @ts-expect-error
              // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              let key;
              // TODO: Bring back matching commissions without policy
              if (
                matchCommissionsWithoutPolicy &&
                (!reportIds || !reportIds.length)
              ) {
                key = `key::${k}`;
                return;
              }
              if (reportIds.length !== reportStrIds.length)
                req.logger.error(
                  `reportIds and reportStrIds should be the same length, but differ (${reportIds.length} != ${reportStrIds.length}).`
                );
              const reportIdsStrIds = [];
              for (let i = 0; i < reportIds.length; i += 1) {
                reportIdsStrIds.push({
                  reportId: reportIds[i],
                  reportStrId: reportStrIds[i],
                });
              }
              let statementIds = v.statement_ids;
              let statementStrIds = v.statement_str_ids;
              const _unmatchStatementIds = {};
              const _unmatchStatementStrIds = {};
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              reportIdsStrIds.forEach(({ reportId, reportStrId }) => {
                key = reportId;
                let _statementIds = statementIds;
                let _statementStrIds = statementStrIds;
                const startBufferMonth =
                  +(typeof reconciler?.key_condition_config === 'object' &&
                  reconciler?.key_condition_config !== null &&
                  'effective_date_range' in reconciler.key_condition_config
                    ? // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      (reconciler.key_condition_config as any)
                        .effective_date_range?.startBufferMonth
                    : 0) || 0;
                const bufferMonth =
                  +(typeof reconciler?.key_condition_config === 'object' &&
                  reconciler?.key_condition_config !== null &&
                  'effective_date_range' in reconciler.key_condition_config
                    ? // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      (reconciler.key_condition_config as any)
                        .effective_date_range?.bufferMonth
                    : 0) || 0;
                // @ts-expect-error
                const isEffectiveDateRange = (key) => {
                  if (
                    // @ts-expect-error
                    !statementDataMap[key].period_date ||
                    // @ts-expect-error
                    !reportDataMap[reportId].effective_date
                  )
                    return true;
                  return (
                    // @ts-expect-error
                    dayjs(statementDataMap[key].period_date).startOf('day') >=
                      // @ts-expect-error
                      dayjs(reportDataMap[reportId].effective_date)
                        .startOf('day')
                        .subtract(startBufferMonth, 'months') &&
                    // @ts-expect-error
                    (!reportDataMap[reportId].policy_term_months ||
                      // @ts-expect-error
                      statementDataMap[key].period_date <=
                        // @ts-expect-error
                        dayjs(reportDataMap[reportId].effective_date)
                          .startOf('day')
                          .add(
                            // @ts-expect-error
                            numberOrDefault(
                              // @ts-expect-error
                              reportDataMap[reportId].policy_term_months,
                              0
                            ) + bufferMonth,
                            'months'
                          ))
                  );
                };
                if (
                  Array.isArray(reconciler?.key_condition) &&
                  reconciler.key_condition.includes('effective_date_range')
                ) {
                  // @ts-expect-error
                  _statementIds = _statementIds.filter((id) => {
                    const isEffective = isEffectiveDateRange(id);
                    if (!isEffective) {
                      // @ts-expect-error
                      _unmatchStatementIds[id] =
                        // @ts-expect-error
                        true && _unmatchStatementIds[id] !== false;
                    } else {
                      // @ts-expect-error
                      _unmatchStatementIds[id] = false;
                    }

                    // @ts-expect-error
                    const statement = statementDataMap[id];
                    // @ts-expect-error
                    const report = reportDataMap[reportId];

                    const statusToUpdate =
                      determineStatementReconciliationStatus({
                        statement,
                        report,
                        startBufferMonth,
                        bufferMonth,
                        _unmatchStatementIds,
                      });

                    if (statusToUpdate !== undefined) {
                      // @ts-expect-error
                      statementNeedUpdateStatus[id] = statusToUpdate;
                    }

                    return isEffective;
                  });

                  // @ts-expect-error
                  _statementStrIds = _statementStrIds.filter((str_id) => {
                    const isEffective = isEffectiveDateRange(str_id);
                    if (!isEffective) {
                      // @ts-expect-error
                      _unmatchStatementStrIds[str_id] =
                        // @ts-expect-error
                        true && _unmatchStatementStrIds[str_id] !== false;
                    } else {
                      // @ts-expect-error
                      _unmatchStatementStrIds[str_id] = false;
                    }
                    return isEffective;
                  });
                }

                // @ts-expect-error
                if (_statementIds.length > 0) {
                  const statementIdsSet = new Set(_statementIds);
                  const statementStrIdsSet = new Set(_statementStrIds);
                  // @ts-expect-error
                  statementIds = statementIds.filter(
                    (r) => !statementIdsSet.has(r)
                  );
                  // @ts-expect-error
                  statementStrIds = statementStrIds.filter(
                    (r) => !statementStrIdsSet.has(r)
                  );
                  // @ts-expect-error
                  processedData[key] = {
                    // @ts-expect-error
                    ...(processedData[key] ?? {
                      report_id: reportId,
                      report_str_id: reportStrId,
                    }),
                  };
                  // @ts-expect-error
                  processedData[key].statement_ids = [
                    ...new Set([
                      // @ts-expect-error
                      ...(processedData[key].statement_ids ?? []),
                      ...(_statementIds ?? []),
                    ]),
                  ];
                  // @ts-expect-error
                  processedData[key].statement_str_ids = [
                    ...new Set([
                      // @ts-expect-error
                      ...(processedData[key].statement_str_ids ?? []),
                      ...(_statementStrIds ?? []),
                    ]),
                  ];
                  // @ts-expect-error
                  processedData[key].reconciliation_status = 'matched';
                  // TODO: Keep track of statements by reconciliation method or update statement records with reconciliations used
                  // @ts-expect-error
                  processedData[key].reconciliation_methods = [
                    ...new Set([
                      // @ts-expect-error
                      ...(processedData[key].reconciliation_methods ?? []),
                      reconciler.id,
                    ]),
                  ];
                  // Keep track of which reconciler matched the statement
                  // And oneCommissionOneAgent if configured
                  // This setting selects the agent that received commissions closest to theh split %.
                  // @ts-expect-error
                  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  _statementIds.forEach((statementId) => {
                    // @ts-expect-error
                    const report = reportDataMap[reportId];
                    // @ts-expect-error
                    const reportContacts = reportDataMap[reportId].contacts;
                    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    let statementContacts;
                    // @ts-expect-error
                    const statement = statementDataMap[statementId];
                    if (oneCommissionOneAgent) {
                      const split = statement.split_percentage;
                      const portion =
                        (statement.commission_amount /
                          (statement.premium_amount *
                            statement.new_commission_rate)) *
                        100;
                      const candidates =
                        // @ts-expect-error
                        reportContacts?.map((contactStrId) => ({
                          // @ts-expect-error
                          name: Formatter.contact(contactsMap[contactStrId]),
                          contactStrId,
                        })) ?? [];
                      if (candidates?.length > 0) {
                        if (statement.agent_name) {
                          let agent_name = statement.agent_name.includes(',')
                            ? statement.agent_name
                                .split(',')
                                .reverse()
                                .join(' ')
                            : statement.agent_name;
                          agent_name = agent_name.replaceAll('*', '').trim();
                          // @ts-expect-error
                          const winner: { name: string; contactStrId: string } =
                            didYouMean(agent_name, candidates, {
                              matchPath: ['name'],
                              threshold: 0.75,
                            });
                          if (winner?.contactStrId) {
                            statementContacts = [winner.contactStrId];
                          }
                        }
                        if (
                          !statementContacts &&
                          split &&
                          report.contacts_split
                        ) {
                          // @ts-expect-error
                          reportContacts.sort((a, b) => {
                            const splitA = report.contacts_split[a];
                            const splitB = report.contacts_split[b];
                            return (
                              Math.abs(+splitA - split) -
                              Math.abs(+splitB - split)
                            );
                          });
                          if (
                            Math.abs(
                              +split - +report.contacts_split[reportContacts[0]]
                            ) <= 1
                          ) {
                            statementContacts = [reportContacts[0]];
                          }
                        }
                        if (
                          !statementContacts &&
                          portion &&
                          report.contacts_split
                        ) {
                          // @ts-expect-error
                          reportContacts.sort((a, b) => {
                            const splitA = report.contacts_split[a];
                            const splitB = report.contacts_split[b];
                            return (
                              Math.abs(+splitA - portion) -
                              Math.abs(+splitB - portion)
                            );
                          });
                          if (
                            Math.abs(
                              +portion -
                                +report.contacts_split[reportContacts[0]]
                            ) <= 1
                          ) {
                            statementContacts = [reportContacts[0]];
                          }
                        }
                        if (!statementContacts) {
                          req.logger.warn(
                            `oneCommissionOneAgent specified, but no agent found for statement: ${statementId}`
                          );
                        }
                      }
                    } else {
                      statementContacts = reportContacts;
                    }
                    // @ts-expect-error
                    processedDataLogs[key] = {
                      // @ts-expect-error
                      ...(processedDataLogs[key] ?? {}),
                      [statementId]: {
                        reconcilerId: reconciler.id,
                        contacts: statementContacts ?? [],
                      },
                    };
                  });
                }
              });
              // @ts-expect-error
              processedDataCur[k].statement_ids = processedDataCur[
                k
                // @ts-expect-error
              ].statement_ids.filter((e) => !_unmatchStatementIds[e]);
              // @ts-expect-error
              processedDataCur[k].statement_str_ids = processedDataCur[
                k
                // @ts-expect-error
              ].statement_str_ids.filter((e) => !_unmatchStatementStrIds[e]);
              unmatchStatementIds = {
                ...unmatchStatementIds,
                ..._unmatchStatementIds,
              };
            });

            // Delete matched from statementData to not re-match by subsequent reconcilers
            const statementIdsToRemove = new Set();

            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            Object.values(processedDataCur).forEach((v) => {
              // @ts-expect-error
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              v.statement_ids.forEach((statementId) => {
                // @ts-expect-error
                if (!unmatchStatementIds[statementId]) {
                  statementIdsToRemove.add(statementId);
                }
              });
            });

            statementData = statementData.filter(
              (item) => !statementIdsToRemove.has(item.id)
            );

            // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const durationMilliseconds = new Date().getTime() - startTime;

            const curStats = {
              id: reconciler.id,
              name: reconciler.name,
              type: reconciler.method_type,
              statementCount,
              reportCount,
              matchedCount: statementIdsMatchedCur.length,
              durationMilliseconds,
            };
            req.logger.info('stats', curStats);
            stats.push(curStats);
          }
        );
      } catch (e) {
        // @ts-expect-error
        req.logger.error('Error while running reconciler', e);
        throw e;
      }
    }

    if (!body.isManual) {
      req.logger.info('Unmatched');
      req.logger.info(
        `  reportData: ${reportData.length - Object.keys(processedData).length}`
      );
      req.logger.info(`  statementData: ${statementData.length}`);
      // @ts-expect-error
      req.logger.info(`  stats:  ${stats}`);
      // Req.logger.info('processedData', processedData);

      // Add unmatched reportData and statementData to processedData
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      statementData.forEach((row) => {
        // Using str_id for statements to not conflict with reportData
        // TODO: Should everything be str_id? Two different objects? Prefix key with source?
        // @ts-expect-error
        processedData[row.str_id] = {
          // @ts-expect-error
          ...(processedData[row.str_id] ?? {}),
          statement_ids: [row.id],
          statement_str_ids: [row.str_id],
          reconciliation_status: 'unmatched',
        };
      });
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      reportData.forEach((row) => {
        if (!(row.id in processedData)) {
          // @ts-expect-error
          processedData[row.id] = {
            // @ts-expect-error
            ...(processedData[row.id] ?? {}),
            report_id: row.id,
            report_str_id: row.str_id,
            reconciliation_status: 'unmatched',
          };
        }
      });
    }

    req.logger.info(
      `processedData.length: ${Object.keys(processedData).length}`
    );

    //
    // Populate/calculate fields
    //
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(processedData).forEach(([_key, row]: [any, any], i) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      fieldConfigs.forEach((header) => {
        // Skip ones that we already have
        if (header.fieldId in row) return;

        if (['field', 'invisifield'].includes(header.type)) {
          let source = header.source;
          // @ts-expect-error
          if (!['reports', 'statements'].includes(header.source)) {
            throw new Error(
              `Unsupported source ${header.source} for reconciliation data`
            );
          }

          // If unmatched, use its type as source (since no other option)
          if (row.reconciliation_status === 'unmatched') {
            source = row.report_id ? 'reports' : 'statements';
          } else if (
            row.reconciliation_status === 'matched' &&
            !row.report_id
          ) {
            source = 'statements';
          }
          const [dataSource1, dataSource2] =
            source === 'reports'
              ? [reportDataMap, statementDataMap]
              : [statementDataMap, reportDataMap];
          const [dataKey1, dataKey2] =
            source === 'reports'
              ? [row.report_id, row.statement_ids?.[0]]
              : [row.statement_ids?.[0], row.report_id];

          // @ts-expect-error
          const colVal1 = dataSource1[dataKey1][header.fieldId];
          // @ts-expect-error
          const colVal2 = dataSource2?.[dataKey2]?.[header.fieldId];
          const colVal = colVal1 ?? colVal2; // Use colVal2 if colVal1 is null or undefined

          const colKey = header.keyAs ?? header.fieldId;
          row[colKey] = colVal;

          // Check for conflicts
          if (!['id'].includes(header.fieldId)) {
            // Skip id col
            if (
              row.reconciliation_status === 'matched' &&
              source === 'reports'
            ) {
              // @ts-expect-error
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              row.statement_ids.forEach((statementId) => {
                // @ts-expect-error
                if (isNill(statementDataMap[statementId][header.fieldId]))
                  return;
                let isDiff =
                  // @ts-expect-error
                  colVal !== statementDataMap[statementId][header.fieldId];
                if (header.fieldId.endsWith('_amount')) {
                  isDiff =
                    // @ts-expect-error
                    +statementDataMap[statementId][header.fieldId] !== +colVal;
                }
                if (isDiff) {
                  row.log = [
                    ...(row.log ?? []),
                    `diff[${header.fieldId}]: report: "${colVal}" / statement(${
                      row.statementId
                    }): "${row[header.fieldId]}"`,
                  ];
                }
              });
            } else if (
              row.reconciliation_status === 'matched' &&
              source === 'statements'
            ) {
              if (
                // @ts-expect-error
                reportDataMap[row.report_id] &&
                // @ts-expect-error
                !isNill(reportDataMap[row.report_id][header.fieldId])
              ) {
                let isDiff =
                  // @ts-expect-error
                  reportDataMap[row.report_id][header.fieldId] !== colVal;
                if (header.fieldId.endsWith('_amount')) {
                  isDiff =
                    // @ts-expect-error
                    +reportDataMap[row.report_id][header.fieldId] !== +colVal;
                }
                if (isDiff) {
                  row.log = [
                    ...(row.log ?? []),
                    `diff[${header.fieldId}]: report: "${
                      // @ts-expect-error
                      reportDataMap[row.report_id][header.fieldId]
                    }" / statement(${row.statement_ids[0]}): "${colVal}"`,
                  ];
                }
              }
              // @ts-expect-error
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              row.statement_ids.forEach((statementId) => {
                // @ts-expect-error
                if (isNill(statementDataMap[statementId][header.fieldId]))
                  return;
                let isDiff =
                  // @ts-expect-error
                  statementDataMap[statementId][header.fieldId] !== colVal;
                if (header.fieldId.endsWith('_amount')) {
                  isDiff =
                    // @ts-expect-error
                    +statementDataMap[statementId][header.fieldId] !== +colVal;
                }
                if (isDiff) {
                  row.log = [
                    ...(row.log ?? []),
                    `diff[${header.fieldId}]: statement (${
                      row.statement_ids[0]
                    }): "${colVal}" / statement(${statementId}): "${
                      // @ts-expect-error
                      statementDataMap[statementId][header.fieldId]
                    }"`,
                  ];
                }
              });
            }
          }
        } else if (header.type === 'aggregate') {
          if (
            (header.source === 'reports' && row.report_id) ||
            (header.source === 'statements' &&
              row.statement_ids &&
              row.statement_ids.length > 0)
          ) {
            const dataSource =
              header.source === 'reports' ? reportDataMap : statementDataMap;
            const dataSourceData =
              header.source === 'reports'
                ? // @ts-expect-error
                  dataSource[row.report_id]
                : // @ts-expect-error
                  row.statement_ids.map((id) => dataSource[id]);

            // @ts-expect-error
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            dataSourceData.forEach((datum) => {
              // @ts-expect-error
              if (!row[header.keyAs]) {
                // @ts-expect-error
                row[header.keyAs] = {
                  // @ts-expect-error
                  [header.getKey(datum)]: {
                    // @ts-expect-error
                    [header.keyAs]:
                      header.method && typeof header.method === 'function'
                        ? datum[header.fieldId]
                        : numberOrDefault(datum[header.fieldId]),
                  },
                };
              } else {
                const newVal = {
                  // @ts-expect-error
                  [header.keyAs]: row[header.keyAs][header.getKey(datum)]
                    ? header.method && typeof header.method === 'function'
                      ? header.method(
                          // @ts-expect-error
                          row[header.keyAs][header.getKey(datum)][header.keyAs],
                          datum[header.fieldId]
                        )
                      : // @ts-expect-error
                        row[header.keyAs][header.getKey(datum)][header.keyAs] +
                        numberOrDefault(datum[header.fieldId])
                    : numberOrDefault(datum[header.fieldId]), // Does this ever get hit?
                };
                // @ts-expect-error
                row[header.keyAs] = {
                  // @ts-expect-error
                  ...row[header.keyAs],
                  // @ts-expect-error
                  [header.getKey(datum)]: newVal,
                };
              }
            });
          }
        }
      });
    });

    // console.log(
    //   'processed processedData',
    //   Object.fromEntries(
    //     Object.entries(processedData).filter(
    //       ([k, v]) => (v as any).reconciliation_status === 'matched'
    //     )
    //   )
    // );

    const matchedData = Object.fromEntries(
      Object.entries(processedData).filter(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        ([k, v]) => (v as any).reconciliation_status === 'matched'
      )
    );

    req.logger.info(`processedData: ${Object.keys(matchedData).length}`);
    // Res.status(200).json({ status: 'test' });
    // return;

    // Above builds processedData (regardless of flow)
    // The following is processing based on matched report data / statement data records

    // TODO: Hack to bring payment_date to top level. Enable this for aggregate fields instead.
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.values(processedData).forEach((row: any) => {
      row.payment_date_first =
        row.payment_date_first?.payment_date_first?.payment_date_first;
      row.payment_date_last =
        row.payment_date_last?.payment_date_last?.payment_date_last;
    });

    req.logger.profile('reconcileData() - computedFields');
    // Computed vals
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    fieldConfigs
      .filter((header) => header.type === 'computed')
      .forEach((header) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.entries(processedData).forEach(([k, v]) => {
          // @ts-expect-error
          processedData[k] = {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            ...(v as any),
            // @ts-expect-error
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            [header.fieldId]: header.compute(v as any, reconciliationSettings),
          };
        });
      });
    req.logger.profile('reconcileData() - computedFields');

    req.logger.profile('reconcileData()');

    const reconciliationStats = {
      created_at: new Date(),
      status: 'completed',
      reportsTotal: reportsTotalCount,
      statementsTotal: statementsTotalCount,
      reconciliationsTotal: Object.keys(processedData).length,
      missingPolicyData: Object.values(processedData).filter(
        (e) =>
          (e as ReconciliationsModel).reconciled ===
          ReconciliationStatusV1.MISSING_POLICY
      ).length,
      missingCommissionData: Object.values(processedData).filter(
        (e) =>
          (e as ReconciliationsModel).reconciled ===
          ReconciliationStatusV1.MISSING_COMMISSIONS
      ).length,
      reportsReconciled: Object.values(processedData).filter((e) =>
        (e as ReconciliationsModel).reconciled.startsWith('✅')
      ).length,
      statementsReconciled: Object.values(processedData)
        .filter((e) => (e as ReconciliationsModel).reconciled.startsWith('✅'))
        .reduce(
          (acc: number, cur) =>
            acc + ((cur as ReconciliationsModel)?.statement_ids?.length ?? 0),
          0
        ),
      commissionsShortfall: Object.values(processedData).filter(
        (e) =>
          (e as ReconciliationsModel).reconciled ===
          ReconciliationStatusV1.COMMISSIONS_SHORTFALL
      ).length,
      // @ts-expect-error
      reconcilerStats: stats,
      failedStatement: [],
    };

    req.logger.info('reconciliationStats', reconciliationStats);

    if (!testRun && !body.isManual) {
      // Update previous reconciliation record
      // Increamental reconciliation don't need to archive previous records
      await pRetry(
        () =>
          prismaClient.reconciliations.updateMany({
            where: {
              AND: [{ account_id: account.account_id }, { state: 'active' }],
            },
            data: {
              // State: 'pending_archive',
              state: 'archived',
            },
          }),
        { retries: 3 }
      );
      // Delete previous reconciliation results
      // TODO: This collapses 'grouped' state into 'archived'. Would be better to maintain grouped state (use diff col?)
      await pRetry(
        () =>
          prismaClient.reconciliation_data.updateMany({
            where: {
              AND: [
                { account_id: account.account_id },
                { state: { in: ['active', 'grouped'] } },
              ],
            },
            data: {
              // State: 'pending_archive',
              state: 'archived',
            },
          }),
        { retries: 3 }
      );
    }

    // Add new reconciliation record
    const reconciliation_str_id = nanoid();
    const reconciliation = await prismaClient.reconciliations.create({
      data: {
        str_id: reconciliation_str_id,
        account_id: account.account_id,
        uid: account.uid,
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        result: reconciliationStats as any,
        settings: {
          mode,
          reconciliationThreshold,
        },
        notes: testRun ? 'Test' : null,
        state: testRun ? 'test' : 'active',
        reconciler_flow_id: reconcilerFlow?.id,
        reconciler_flow_str_id: reconcilerFlow?.str_id,
        created_by: account.uid,
        created_proxied_by: account.ouid,
      },
    });

    const reportIdsToUpdate: Record<
      string,
      { first_payment_date: Date | null; first_processed_date: Date | null }
    > = {};
    const statementIdsToUpdate = {};
    // @ts-expect-error
    const statementToClear = [];
    // @ts-expect-error
    const statementCommissionToClear = [];

    // Add new reconciliation results
    req.logger.profile('Adding new reconciliations');
    const reconciliations = Object.entries(processedData).map(([k, v]) => {
      // @ts-expect-error
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const reportState = reportDataMap[(v as any).report_id]?.state; // 'active' or 'grouped'
      const result = {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        ...(v as any),
        normalized_id: k,
        account_id: account.account_id,
        uid: account.uid,
        str_id: nanoid(),
        state: testRun ? 'test' : (reportState ?? 'active'),
        // @ts-expect-error
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        contacts: reportDataMap[(v as any).report_id]?.contacts ?? [],
        created_by: account.uid,
        created_proxied_by: account.ouid,
      };
      if (
        result.report_id &&
        result.statement_ids &&
        result.statement_ids.length > 0
      ) {
        // Each statement record should only be matched to one policy record
        // @ts-expect-error
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        result.statement_ids.forEach((statementId) => {
          if (
            // @ts-expect-error
            statementDataMap[statementId]?.report_data_id !==
            // @ts-expect-error
            processedDataLogs[k][statementId]?.report_data_id
          ) {
            statementCommissionToClear.push(statementId);
          }
          if (
            !isEqual(
              // @ts-expect-error
              statementDataMap[statementId]?.contacts,
              // @ts-expect-error
              processedDataLogs[k][statementId]?.contacts
            ) ||
            !isEqual(
              // @ts-expect-error
              statementDataMap[statementId]?.contacts,
              // @ts-expect-error
              reportDataMap[result.report_id].contacts
            ) ||
            // @ts-expect-error
            statementDataMap[statementId]?.report_data_id !== result.report_id
          ) {
            const previousPolicy =
              // @ts-expect-error
              reportDataMap[statementIdsToUpdate[statementId]?.report_data_id];
            // @ts-expect-error
            const currentPolicy = reportDataMap[result.report_id];

            // Use contacts from processedDataLogs if available (for oneCommissionOneAgent), otherwise use policy contacts
            const contactsToUse =
              // @ts-expect-error
              processedDataLogs[k]?.[statementId]?.contacts ??
              // @ts-expect-error
              reportDataMap[result.report_id].contacts;

            // @ts-expect-error
            statementIdsToUpdate[statementId] = {
              contacts: contactsToUse,
              report_data_id:
                previousPolicy?.effective_date &&
                currentPolicy?.effective_date &&
                dayjs(previousPolicy?.effective_date).isBefore(
                  dayjs(currentPolicy?.effective_date)
                )
                  ? result.report_id
                  : // @ts-expect-error
                    statementIdsToUpdate[statementId]?.report_data_id ||
                    result.report_id,
            };
          }
        });

        // Calculate first payment date
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        const first_payment_date = (result.statement_ids as any[])
          .map((statementId) => {
            const { payment_date = undefined } =
              // @ts-expect-error
              statementDataMap[statementId] ?? {};
            return payment_date;
          })
          .filter((payment_date): payment_date is Date => !!payment_date)
          // @ts-expect-error
          .reduce((acc, cur) => {
            if (acc === undefined) {
              return cur;
            }

            // @ts-expect-error
            return cur < acc ? cur : acc;
          }, undefined);

        // Calculate first processed date
        const first_processed_date = result.statement_ids.reduce(
          // @ts-expect-error
          (acc, statementId) => {
            const { processing_date: cur = undefined } =
              // @ts-expect-error
              statementDataMap[statementId] ?? {};
            if (cur === undefined) {
              return acc;
            }
            if (acc === undefined) {
              return cur;
            }

            return cur < acc ? cur : acc;
          },
          undefined
        );

        reportIdsToUpdate[result.report_id] = {
          first_payment_date: first_payment_date ?? null,
          first_processed_date: first_processed_date ?? null,
        };
      } else if (!result.report_id) {
        if (result.statement_ids.length > 0) {
          // @ts-expect-error
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          result.statement_ids.forEach((statementId) => {
            if (
              // @ts-expect-error
              statementDataMap[statementId]?.report_data_id !==
              // @ts-expect-error
              processedData[k][statementId]?.report_data_id
            ) {
              statementToClear.push(statementId);
            }
          });
        }
      }
      delete result.id;
      // Delete result.reconciliation_methods;
      // TODO: Make this a proper json array in db
      // result.statement_ids = result.statement_ids;
      // result.statement_str_ids = result.statement_str_ids;
      result.reconciliation_id = reconciliation.id;
      result.reconciliation_str_id = reconciliation_str_id;
      // TODO: Convert report_id to string...eventually make int
      if (result.report_id) {
        result.report_data_id = +result.report_id;
        result.report_id = String(result.report_id);
      }
      return result;
    });
    req.logger.info('-----reconciliations-----');
    req.logger.info(`reconciliations.length: ${reconciliations.length}`);

    req.logger.profile('Create new reconciliation_data records');
    await limitConcurrency(
      async (data) => {
        const { statement_ids, ...rest } = data;
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        const createData: any = {
          ...rest,
        };
        if (statement_ids?.length > 0) {
          createData.statements = {
            // @ts-expect-error
            connect: statement_ids.map((id) => ({ id })),
          };
          createData.statement_ids = statement_ids;
        }
        return await prismaClient.reconciliation_data.create({
          data: createData,
        });
      },
      reconciliations,
      100,
      {
        retries: 0,
      }
    );

    req.logger.profile('Create new reconciliation_data records');
    const groupedRecords = await prismaClient.reconciliation_data.findMany({
      where: {
        account_id: account.account_id,
        reconciliation_id: reconciliation.id,
        state: 'grouped',
      },
      include: {
        report_data: true,
      },
    });
    req.logger.info(
      `Link grouped reconciliation records (${groupedRecords.length}): ${groupedRecords.map((e) => e.id).join(', ')}`
    );
    req.logger.profile('Link grouped reconciliation records');
    const groupedRecordsGroupByParentId = groupedRecords.reduce((acc, cur) => {
      // @ts-expect-error
      acc[cur.report_data.parent_id] = [
        // @ts-expect-error
        ...(acc[cur.report_data.parent_id] ?? []),
        cur.id,
      ];
      return acc;
    }, {});

    const records = await prismaClient.reconciliation_data.findMany({
      where: {
        account_id: account.account_id,
        reconciliation_id: reconciliation.id,
      },
      select: { id: true, report_data_id: true },
    });
    const parentIds = Object.keys(groupedRecordsGroupByParentId);
    const matchedIds = records
      .filter((e) => parentIds.includes(`${e.report_data_id}`))
      .map((r) => ({
        parent_id: r.report_data_id,
        // @ts-expect-error
        children: groupedRecordsGroupByParentId[r.report_data_id],
        reconciliation_id: r.id,
      }));

    const BATCH_SIZE = 10;
    const tasks = chunk(
      matchedIds.map((item) => {
        return () =>
          pRetry(
            () =>
              prismaClient.reconciliation_data.updateMany({
                where: { id: { in: item.children } },
                data: { parent_id: item.reconciliation_id },
              }),
            { retries: 3, minTimeout: 100 }
          );
      }),
      BATCH_SIZE
    );
    for (const task of tasks) {
      await Promise.all(task.map((action) => action()));
    }
    req.logger.profile('Link grouped reconciliation records');

    // Build map of contact lists from statementIdContactStrIdsMap, so we can update all statement_ids at once.
    // const contactStrIdsStatementIdsMap = {};
    // Object.entries(statementIdsToUpdate).forEach(
    //   ([statementId, statementData]: [
    //     number,
    //     { contacts: string[]; report_data_id: number },
    //   ]) => {
    //     const contactStrIdsKey = (statementData.contacts as string[])
    //       .sort()
    //       .join();
    //     contactStrIdsStatementIdsMap[contactStrIdsKey] = [
    //       ...(contactStrIdsStatementIdsMap[contactStrIdsKey] ?? []),
    //       parseInt(statementId),
    //     ];
    //   }
    // );
    // const promises = Object.entries(contactStrIdsStatementIdsMap).map(
    //   ([contactStrIds, statementIds]) => {
    //     const contactStrIdsUnique = Array.from(
    //       new Set(contactStrIds.split(','))
    //     );
    //     const statementIdsUnique = Array.from(
    //       new Set(statementIds as number[])
    //     );
    //     return prismaClient.statement_data.updateMany({
    //       where: {
    //         id: { in: statementIdsUnique },
    //       },
    //       data: {
    //         contacts: contactStrIdsUnique,
    //       },
    //     });
    //   }
    // );

    await updateStatementDataStatus(statementNeedUpdateStatus);

    req.logger.info(
      `Updating ${Object.keys(statementIdsToUpdate).length} commission statement records`
    );

    const updateReportData = async ({
      reportId,
      data,
    }: {
      reportId: string;
      data: { first_payment_date: unknown; first_processed_date: unknown };
    }) => {
      const { first_payment_date, first_processed_date } = data;
      if (
        // @ts-expect-error
        dayjs(reportDataMap[reportId]?.first_payment_date).isSame(
          dayjs(first_payment_date as Date)
        ) &&
        // @ts-expect-error
        dayjs(reportDataMap[reportId]?.first_processed_date).isSame(
          first_processed_date as Date
        )
      ) {
        return;
      }

      // @ts-expect-error
      return prismaClient.report_data.update({
        where: { id: Number(reportId) },
        data: {
          first_payment_date,
          first_processed_date,
        },
      });
    };
    await limitConcurrency(
      updateReportData,
      Object.entries(reportIdsToUpdate).map(([reportId, data]) => ({
        reportId,
        data,
      })),
      UPDATE_CONCURRENCY_LIMIT,
      {
        onFail: (context: {
          // @ts-expect-error
          error;
          data: Parameters<typeof updateReportData>[0];
        }) => {
          req.logger.warn('Failed to update report data', context.data);
          // @ts-expect-error
          reconciliationStats.failedStatement.push(context.data.reportId);
        },
      }
    );

    req.logger.profile('Update statement_data contacts');
    const updateStatementData = async (data: {
      statementId: string;
      statementData: { contacts: string[]; report_data_id: number };
    }) => {
      const { statementId, statementData } = data;
      // @ts-expect-error
      const originStatement = statementDataMap[statementId];
      if (
        originStatement?.contacts?.sort().join() ===
          statementData?.contacts?.sort().join() &&
        originStatement?.report_data_id === statementData?.report_data_id
      ) {
        return;
      }
      if (originStatement?.report_data_id !== statementData?.report_data_id) {
        await prismaClient.statement_data.update({
          where: {
            id: Number(statementId),
          },
          data: {
            report: statementData.report_data_id
              ? { connect: { id: statementData.report_data_id } }
              : { disconnect: true },
          },
        });
      }
      if (
        originStatement?.contacts?.sort().join() !==
          statementData?.contacts?.sort().join() ||
        originStatement?.report_data_id !== statementData?.report_data_id
      ) {
        const condition = body.disableUpdatePaidApprovedContacts
          ? {
              id: Number(statementId),
              OR: [
                { agent_commissions_status: { equals: Prisma.DbNull } },
                {
                  AND: [
                    {
                      agent_commissions_status: {
                        not: CommissionStatuses.APPROVED,
                      },
                    },
                    {
                      agent_commissions_status: {
                        not: CommissionStatuses.PAID,
                      },
                    },
                  ],
                },
              ],
            }
          : {
              id: Number(statementId),
            };
        // Use updateMany instead of update, since update will throw error if no record is found
        await prismaClient.statement_data.updateMany({
          where: condition,
          data: {
            contacts: [...new Set(statementData.contacts)],
          },
        });
      }
    };

    await limitConcurrency(
      updateStatementData,
      Object.entries(statementIdsToUpdate).map((r) => ({
        statementId: r[0],
        statementData: r[1],
      })),
      UPDATE_CONCURRENCY_LIMIT,
      {
        onFail: (context: {
          // @ts-expect-error
          error;
          data: Parameters<typeof updateStatementData>[0];
        }) => {
          req.logger.warn('Failed to update statement data', context.data);
          // @ts-expect-error
          reconciliationStats.failedStatement.push(context.data.statementId);
        },
      }
    );
    req.logger.profile('Update statement_data contacts');

    req.logger.profile('Update statement_data reconciliation_method');
    // Update statement_data reconciliation_method
    const reconciledAt = new Date();
    await limitConcurrency(
      async ([id, reconcilerId]) => {
        // @ts-expect-error
        const originStatement = statementDataMap[id];
        if (+originStatement?.reconciliation_method === reconcilerId) {
          return;
        }
        return await prismaClient.statement_data.update({
          where: { id: +id },
          data: {
            reconciliation_method: `${reconcilerId}`,
          },
        });
      },
      Object.entries(statementMatchedReconcilers),
      10
    );

    req.logger.profile('Update statement_data reconciliation_method');

    const GROUP_SIZE_RECONCILED_AT = 30000;
    const batched = chunk(
      Object.entries(statementMatchedReconcilers).map((r) => +r[0]),
      GROUP_SIZE_RECONCILED_AT
    );
    // Batch update matched statement_data reconciled_at
    await limitConcurrency(
      async (batchIds) => {
        return await prismaClient.statement_data.updateMany({
          where: { id: { in: batchIds } },
          data: { reconciled_at: reconciledAt },
        });
      },
      batched,
      10
    );

    const BATCH_SIZE_CLEAR = 30000;

    if (!body.disableClearCommissionCalcs) {
      req.logger.profile('Clear changed statement_data');
      const clearItems = await runInBatch({
        name: 'Clear statement_data',
        // @ts-expect-error
        items: statementToClear,
        batchSize: BATCH_SIZE_CLEAR,
        onBatch: async (batch) => {
          const stats = await prismaClient.statement_data.updateMany({
            where: {
              id: { in: batch },
              OR: COMMISSION_STATUS_EDITABLE,
            },
            data: {
              report_data_id: null,
              reconciliation_method: null,
              agent_commissions: Prisma.DbNull,
              agent_commissions_v2: Prisma.DbNull,
              agent_commissions_log: Prisma.DbNull,
              agent_commissions_status: Prisma.DbNull,
            },
          });
          return [stats];
        },
      });
      req.logger.info(
        `Cleared ${clearItems.reduce((acc, cur) => acc + cur.count, 0)} statement_data records`
      );
      const commissionCleared = await runInBatch({
        name: 'Clear commission calcs',
        // @ts-expect-error
        items: statementCommissionToClear,
        batchSize: BATCH_SIZE_CLEAR,
        onBatch: async (batch) => {
          const stats = await prismaClient.statement_data.updateMany({
            where: {
              id: { in: batch },
              OR: COMMISSION_STATUS_EDITABLE,
            },
            data: {
              agent_commissions: Prisma.DbNull,
              agent_commissions_v2: Prisma.DbNull,
              agent_commissions_log: Prisma.DbNull,
              agent_commissions_status: Prisma.DbNull,
            },
          });
          return [stats];
        },
      });
      req.logger.info(
        `Cleared ${commissionCleared.reduce(
          (acc, cur) => acc + cur.count,
          0
        )} commission_data records`
      );
      req.logger.profile('Clear changed statement_data');
    }

    // TODO/WIP
    // if (!testRun) {
    //   await prismaClient.reconciliations.update({
    //     where: {
    //       id: reconciliation.id,
    //     },
    //     data: {
    //       state: 'active',
    //     },
    //   });
    // }
    const endTime = Date.now();

    await prismaClient.data_processing.update({
      where: { id: dataProcessing.id },
      data: {
        status:
          reconciliationStats.failedStatement.length > 0
            ? DataProcessingStatuses.ERROR
            : DataProcessingStatuses.COMPLETED,
        stats: reconciliationStats as Prisma.InputJsonObject,
        duration: endTime - startTime,
        created_by: account.uid,
        created_proxied_by: account.ouid,
      },
    });
    req.logger.info(`${dataProcessing.id}`);

    req.logger.profile('Adding new reconciliations');
    req.logger.info('\n\n');

    const results = {
      stats: reconciliationStats,
      // @ts-expect-error
      stats2: stats,
    };

    if (body.isSync) {
      res.status(200).json(results);
    }
  } catch (e) {
    // @ts-expect-error
    req.logger.error('Error encountered while reconciling data', e);
    if (dataProcessing?.id) {
      const endTime = Date.now();
      await prismaClient.data_processing.update({
        where: { id: dataProcessing.id },
        data: {
          status: DataProcessingStatuses.ERROR,
          // @ts-expect-error
          stats: reconciliationStats as Prisma.InputJsonObject,
          // @ts-expect-error
          duration: endTime - startTime,
          created_by: account.uid,
          created_proxied_by: account.ouid,
        },
      });
    }
    throw e;
  }
  return;
};

/**
 * Get all data needed for running reconciliation
 *   This includes settings, mappings, processors, companies, normalizers,
 *   commission schedules, report data, statement data, etc.
 */
const getAllData = async ({
  // @ts-expect-error
  account_id,
  // @ts-expect-error
  document_str_id,
  // @ts-expect-error
  policyId,
  // @ts-expect-error
  statementIds,
  // @ts-expect-error
  isManual,
}) => {
  // AccountSettings
  const accountSettings = await prismaClient.accounts.findFirst({
    where: { str_id: account_id, state: 'active' },
  });
  const mode = accountSettings?.mode ?? 'insurance'; // Default to insurance
  const reconciliationThreshold =
    accountSettings?.reconciliation_threshold ?? 1;

  // const showGroupedPolicies = accountSettings?.show_grouped_policies ?? false;
  // Match regardless of showGroupedPolicies setting, filter out when showing data

  // Reconciliation configurations
  const fieldConfigs = getFieldConfigs(mode);

  const contacts = await prismaClient.contacts.findMany({
    where: {
      account_id,
      state: 'active',
    },
  });

  // Get reconciliation workflows and methods

  const reconcilerFlows = await prismaClient.reconciler_flows.findMany({
    where: {
      OR: [
        { account_id, state: { in: ['active', 'disabled'] } },
        { access: 'global', state: 'active' },
      ],
    },
    accountInject: false,
  });
  const reconcilers = await prismaClient.reconcilers.findMany({
    where: {
      OR: [
        { account_id, state: { in: ['active', 'disabled'] } },
        { access: 'global', state: 'active' },
      ],
    },
    accountInject: false,
  });

  // Figure out how to do this using Postgres/Prisma instead.
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  reconcilerFlows.forEach((reconcilerFlow) => {
    const actualReconcilers = (reconcilerFlow.flow as Array<string>)
      ?.map((reconcilerStrId) => {
        return reconcilers.find((e) => {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          if ((e as any).str_id === reconcilerStrId) {
            return true;
          }
          return false;
        });
      })
      .filter((e) => e);
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (reconcilerFlow as any).reconcilers = actualReconcilers;
  });

  const getFullStatements = async (
    statementIds: number[],
    isManual?: boolean
  ) => {
    const data: statement_data[] = [];
    let page = 0;
    const size = 3000;
    let hasNext = true;
    const condition: Prisma.statement_dataWhereInput = {
      account_id,
      document_id: document_str_id,
      state: 'active',
      id: { in: statementIds },
      // Skip statements that have already been manualy reconciled
      OR: [
        { reconciliation_method: { not: '0' } },
        { reconciliation_method: null },
      ],
    };
    if (isManual) {
      delete condition.OR;
    }
    while (hasNext) {
      const items = await pRetry(
        () =>
          prismaClient.statement_data.findMany({
            where: condition,
            take: size,
            skip: page * size,
            orderBy: { id: 'asc' },
          }),
        { retries: 3 }
      );
      data.push(...items);
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.log('getFullStatements', items.length, 'all', data.length);
      if (items.length < size) {
        hasNext = false;
        break;
      }
      page += 1;
    }
    return data;
  };

  // Get policy report and commission statement data, if similarity matching, get full statement and policy data
  console.time('Get statement_data');
  const statementData = await getFullStatements(statementIds, isManual);
  const invalidCommissions = statementData.filter((e) =>
    [
      CommissionStatuses.PAID,
      CommissionStatuses.APPROVED,
      CommissionStatuses.MANUAL,
    ].includes(e.agent_commissions_status as CommissionStatuses)
  );
  if (isManual && invalidCommissions.length > 0) {
    throw BusinessException.from(
      `Only editable statements (${invalidCommissions
        .map((r) => r.id)
        .join(', ')}) can be reconciled`
    );
  }

  console.timeEnd('Get statement_data');

  console.time('Get report_data');
  const reportData = await prismaClient.report_data.findMany({
    where: {
      account_id,
      state: { in: ['active', 'grouped'] },
      id: { in: policyId ? [policyId] : undefined },
    },
    orderBy: {
      effective_date: 'desc',
    },
  });
  console.timeEnd('Get report_data');

  return {
    mode,
    fieldConfigs,
    contacts,
    reportData,
    statementData,
    reconcilerFlows,
    reconciliationThreshold,
  };
};

interface ReconciliationsModel {
  id: number;
  log: string;
  reconciled: string;
  report_id: string;
  policy_status: string;
  customer_name: string;
  normalized_id: string;
  uid: string;
  writing_carrier_name: string;
  policy_id: string;
  carrier_name: string;
  balance: number;
  statement_id: string;
  statement_ids: number[];
  statement_str_ids: string[];
  state: string;
  str_id: string;
  product_type: string;
  created_at: string | number;
  updated_at: string | number;
  effective_date: string | number;
  commissions_expected: number;
  premium_amount: number;
  amount_paid: number;
  aggregate_premiums: number;
  commission_amount_monthly: object;
  commission_expected_monthly: object;
  commission_balance_monthly: object;
  payment_date_first: string | number;
  payment_date_last: string | number;
}
const _getTableFields = (post: ReconciliationsModel) => {
  // Pick up all the fields form the post
  const {
    log,
    reconciled,
    report_id,
    policy_status,
    customer_name,
    normalized_id,
    uid,
    writing_carrier_name,
    policy_id,
    carrier_name,
    balance,
    statement_id,
    statement_ids,
    statement_str_ids,
    state,
    str_id,
    product_type,
    created_at,
    updated_at,
    effective_date,
    commissions_expected,
    premium_amount,
    amount_paid,
    aggregate_premiums,
    commission_amount_monthly,
    commission_expected_monthly,
    commission_balance_monthly,
    id,
    payment_date_first,
    payment_date_last,
  } = post;

  const tableFields = {
    log,
    reconciled,
    report_id,
    policy_status,
    customer_name,
    normalized_id,
    uid,
    writing_carrier_name,
    policy_id,
    carrier_name,
    balance,
    statement_id,
    statement_ids,
    statement_str_ids,
    state,
    str_id,
    product_type,
    created_at,
    updated_at,
    effective_date,
    commissions_expected,
    premium_amount,
    amount_paid,
    aggregate_premiums,
    commission_amount_monthly,
    commission_expected_monthly,
    commission_balance_monthly,
    id,
    payment_date_first,
    payment_date_last,
  };
  // Pick up valid fileds
  const validFields = Object.keys(tableFields).reduce((acc, key) => {
    // @ts-expect-error
    if (tableFields[key]) {
      // @ts-expect-error
      acc[key] = tableFields[key];
    }
    return acc;
  }, {});
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.log('reconcilations', validFields);
  return validFields;
};

export const updateStatementDataStatus = async (
  statementNeedUpdateStatus: Record<string, string | null>,
  batchSize: number = 1000,
  concurrency: number = 5
) => {
  // Group statements by status for more efficient batch updates
  const statusGroups: Record<string, number[]> = {};

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  Object.entries(statementNeedUpdateStatus).forEach(([statementId, status]) => {
    const statusKey = status;
    // @ts-expect-error
    if (!statusGroups[statusKey]) {
      // @ts-expect-error
      statusGroups[statusKey] = [];
    }
    // @ts-expect-error
    statusGroups[statusKey].push(Number(statementId));
  });

  // Create update tasks for each status group
  const updateTasks = Object.entries(statusGroups).map(
    ([status, statementIds]) => {
      return async () => {
        // Split into smaller batches to avoid timeout with large datasets
        const batches = chunk(statementIds, batchSize);

        for (const batch of batches) {
          await pRetry(
            () =>
              prismaClient.statement_data.updateMany({
                where: { id: { in: batch } },
                data: {
                  reconciliation_status: status,
                },
              }),
            {
              retries: 3,
            }
          );
        }
      };
    }
  );

  await limitConcurrency((task) => task(), updateTasks, concurrency);
};

export const determineStatementReconciliationStatus = ({
  statement,
  report,
  startBufferMonth,
  bufferMonth,
  _unmatchStatementIds,
}: {
  statement: statement_data;
  report: report_data;
  startBufferMonth?: number;
  bufferMonth?: number;
  _unmatchStatementIds: Record<string, boolean>;
}) => {
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let statusToUpdate;

  // If the statement is unmatch and not have report_data_id, we need to update the status
  if (_unmatchStatementIds[statement.id] && !statement.report_data_id) {
    const statementPeriodDate = dayjs(statement.period_date).startOf('day');

    const reportEffectiveDate = dayjs(report.effective_date)
      .startOf('day')
      // @ts-expect-error
      .subtract(startBufferMonth, 'months');

    const reportPolicyTermMonths = dayjs(report.effective_date)
      .startOf('day')
      .add(
        // @ts-expect-error
        numberOrDefault(report.policy_term_months, 0) + bufferMonth,
        'months'
      );

    if (statement.policy_id === report.policy_id) {
      if (
        statementPeriodDate > reportEffectiveDate &&
        (!report.policy_term_months ||
          statementPeriodDate > reportPolicyTermMonths)
      ) {
        if (
          statement.reconciliation_status !==
          STATEMENT_RECONCILIATION_STATUS.EXPIRED_POLICY
        ) {
          statusToUpdate = STATEMENT_RECONCILIATION_STATUS.EXPIRED_POLICY;
        }
      } else if (
        statementPeriodDate < reportEffectiveDate &&
        (!report.policy_term_months ||
          statementPeriodDate < reportPolicyTermMonths)
      ) {
        if (
          statement.reconciliation_status !==
          STATEMENT_RECONCILIATION_STATUS.COMMISSION_PERIOD_OUT_OF_RANGE
        ) {
          statusToUpdate =
            STATEMENT_RECONCILIATION_STATUS.COMMISSION_PERIOD_OUT_OF_RANGE;
        }
      }
    } else {
      if (
        statement.reconciliation_status !==
        STATEMENT_RECONCILIATION_STATUS.MISSING_POLICY
      ) {
        statusToUpdate = STATEMENT_RECONCILIATION_STATUS.MISSING_POLICY;
      }
    }
  } else {
    if (statement.reconciliation_status !== null) {
      statusToUpdate = null;
    }
  }

  return statusToUpdate;
};

export default withAuth(createHandler(ReconcileDataHandler));
