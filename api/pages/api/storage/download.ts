import { captureException } from '@sentry/nextjs';
import { create<PERSON><PERSON><PERSON>, Post, Req, Res } from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { storage } from '@/lib/firebase-admin';
import { getMimeType } from '@/lib/helpers';
import prisma from '@/lib/prisma';

class Handler extends BaseHandler {
  @Post()
  async post(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await downloadFile(req, res);
  }
}

type DownloadFileBody = {
  endpoint_str_id: string;
  file_preview_type: 'override' | 'original';
  endpoint?: 'documents' | 'accounts' | 'admin/processors/descriptions';
};

const streamFile = async (
  filePath: string,
  res: ExtNextApiResponse,
  req: ExtNextApiRequest,
  customFilename?: string
) => {
  const fileRef = storage.file(filePath);
  const [exists] = await fileRef.exists();

  if (!exists) {
    res.status(404).json({ error: 'File not found' });
    return;
  }

  const readStream = fileRef.createReadStream();
  const filename = customFilename || filePath.split('/').pop() || '';
  const mimeType = getMimeType(filename);
  const encodedFilename = encodeURIComponent(filename);

  res.setHeader('Content-Type', mimeType);
  res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
  res.setHeader(
    'Content-Disposition',
    `attachment; filename="${encodedFilename}"`
  );

  readStream.on('error', (error) => {
    req.logger.error(`Error: ${error.message}`);
    captureException(error);
    res.status(500).json({ error: error.message });
  });

  return readStream.pipe(res);
};

const findFileWithFallback = async (
  filePath: string
): Promise<string | null> => {
  let fileRef = storage.file(filePath);
  let [exists] = await fileRef.exists();

  if (!exists) {
    const fallbackPath = filePath.replace('uploads/', 'documents/');
    fileRef = storage.file(fallbackPath);
    [exists] = await fileRef.exists();

    if (exists) {
      return fallbackPath;
    }
    return null;
  }

  return filePath;
};

const downloadFile = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  try {
    const {
      endpoint_str_id,
      file_preview_type,
      endpoint = 'documents',
    } = req.body as DownloadFileBody;

    if (
      !['documents', 'accounts', 'admin/processors/descriptions'].includes(
        endpoint
      )
    ) {
      req.logger.error('Invalid endpoint.');
      res.status(404).json({ error: 'Invalid endpoint.' });
      return res;
    }

    if (!endpoint_str_id) {
      req.logger.error('Document ID is required');
      res.status(400).json({ error: 'Document ID is required' });
      return res;
    }

    if (endpoint === 'admin/processors/descriptions') {
      return await streamFile(endpoint_str_id, res, req);
    }
    const isAdmin = await prisma.admin.findUnique({
      where: { uid: req.ouid ?? req.uid },
    });

    const whereClause = {
      str_id: endpoint_str_id,
      account_id: req.account_id,
      state: 'active',
    };

    if (isAdmin || endpoint === 'accounts') {
      delete whereClause.account_id;
    }

    const targetDocument = await prisma[endpoint].findUnique({
      where: whereClause,
    });

    if (!targetDocument) {
      req.logger.error('Document not found');
      res.status(404).json({ error: 'Document not found' });
      return res;
    }

    let filePath = '';
    let filename = '';
    if (endpoint === 'documents') {
      filePath =
        file_preview_type === 'override'
          ? targetDocument.override_file_path
          : targetDocument.file_path;
      filename =
        file_preview_type === 'override'
          ? targetDocument.override_filename
          : targetDocument.filename;
    } else if (endpoint === 'accounts') {
      filePath = targetDocument.logo_url ?? '';
      filename = filePath.split('/').pop() ?? '';
    }
    const actualFilePath = await findFileWithFallback(filePath);

    if (!actualFilePath) {
      res.status(404).json({ error: 'File not found' });
      return res;
    }

    return await streamFile(actualFilePath, res, req, filename);
  } catch (error) {
    // @ts-expect-error
    req.logger.error(`Error: ${error.message}`);
    captureException(error);
    // @ts-expect-error
    res.status(500).json({ error: error.message });
    return res;
  }
};

export default withAuth(createHandler(Handler));
