-- Migrate groupBy -> groupBys, sortingField -> sortBys, timePeriod -> groupBys[?].timePeriod, and removed similarityGroupBy
UPDATE widgets
SET spec = (
  spec
  || jsonb_build_object(
    'groupBys',
    CASE
      WHEN spec ? 'groupBy' AND spec->>'groupBy' IS NOT NULL AND spec->>'groupBy' != ''
        THEN jsonb_build_array(
          jsonb_build_object(
            'id', 1,
            'field', spec->>'groupBy',
            'timePeriod',
              CASE
                WHEN spec ? 'timePeriod' THEN spec->>'timePeriod'
                ELSE NULL
              END
          )
        )
      ELSE '[]'::jsonb
    END
  )
  || jsonb_build_object(
    'sortBys',
    CASE
      WHEN spec ? 'sortingField' AND spec->'sortingField' IS NOT NULL
        THEN jsonb_build_array(
          jsonb_build_object(
            'id', 1,
            'field', spec->'sortingField'->>'field',
            'order', spec->'sortingField'->>'order',
            'limit', 
              CASE
                WHEN (spec->'sortingField'->>'limit') IS NOT NULL AND spec->'sortingField'->>'limit' != ''
                  THEN (spec->'sortingField'->>'limit')::int
                ELSE NULL
              END
          )
        )
      ELSE '[]'::jsonb
    END
  )
)
- 'groupBy'
- 'sortingField'
- 'timePeriod'
- 'similarityGroupBy'
WHERE (spec ? 'groupBy' OR spec ? 'sortingField');
