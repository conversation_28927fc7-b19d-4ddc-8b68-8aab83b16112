import { AccountIds } from 'common/constants';
import { SavedReportStatuses } from 'common/globalTypes';
import { inject, injectable } from 'inversify';
import set from 'lodash-es/set';

import { ContactService } from '@/services/contact';
import { SettingsService } from '@/services/settings';
import { UserService } from '@/services/user';
import {
  type ChildRelationshipWithContact,
  DataStates,
  type ExtNextApiRequest,
  Roles,
} from '@/types';
import type { ContactWhereGetType } from './types';
import { calculateSkipAndTake } from '@/lib/prisma/utils';
import { AccountService } from '@/services/account';

@injectable()
export class ContactsQueries {
  // @ts-expect-error
  @inject(ContactService) contactService: ContactService;
  // @ts-expect-error
  @inject(UserService) userService: UserService;
  // @ts-expect-error
  @inject(SettingsService) settingsService: SettingsService;
  // @ts-expect-error
  @inject(AccountService) accountService: AccountService;

  getNestedContacts = (
    allowedContacts: string[] = [],
    hierarchy_depth: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    queryNestedContacts: any,
    getParentChildren = false
  ) => {
    const initialParentObject =
      +hierarchy_depth > 0
        ? {
            where: {
              state: 'active',
              AND: [],
            },
            select: {
              id: true,
              start_date: true,
              end_date: true,
              contact_group_id: true,
              sync_id: true,
              sync_worker: true,
              config: true,
              parent: {
                where: { AND: [] },
                select: {
                  id: true,
                  status: true,
                  str_id: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                  sync_id: true,
                  sync_worker: true,
                  contact_level: {
                    select: {
                      id: true,
                      created_at: true,
                      created_by: true,
                      created_proxied_by: true,
                      updated_at: true,
                      updated_by: true,
                      updated_proxied_by: true,
                      level: true,
                      level_label: true,
                      product_type: true,
                      loa: true,
                      sync_id: true,
                      sync_worker: true,
                      contact_id: true,
                      start_date: true,
                      end_date: true,
                    },
                  },
                },
              },
            },
          }
        : false;

    const initialChildObject =
      +hierarchy_depth > 0
        ? {
            where: {
              state: 'active',
              AND: [],
            },
            select: {
              id: true,
              start_date: true,
              end_date: true,
              contact_group_id: true,
              sync_id: true,
              sync_worker: true,
              contact: {
                select: {
                  id: true,
                  str_id: true,
                  status: true,
                  first_name: true,
                  last_name: true,
                  email: true,
                  sync_id: true,
                  sync_worker: true,
                  contact_level: {
                    select: {
                      id: true,
                      created_at: true,
                      created_by: true,
                      created_proxied_by: true,
                      updated_at: true,
                      updated_by: true,
                      updated_proxied_by: true,
                      level: true,
                      level_label: true,
                      product_type: true,
                      loa: true,
                      sync_id: true,
                      sync_worker: true,
                      contact_id: true,
                      start_date: true,
                      end_date: true,
                    },
                  },
                  child_relationships: {
                    where: {
                      AND: [],
                    },
                  },
                },
              },
            },
          }
        : false;

    if (initialParentObject && allowedContacts && allowedContacts.length > 0) {
      // @ts-expect-error
      initialParentObject.where.AND.push({ id: { in: allowedContacts } });
      // @ts-expect-error
      initialParentObject.select.parent.where.AND.push({
        id: { in: allowedContacts },
      });
    }

    const depth = parseInt(hierarchy_depth);
    const parentResult =
      typeof queryNestedContacts === 'function'
        ? queryNestedContacts(
            initialParentObject,
            depth,
            'parent',
            getParentChildren
          )
        : undefined;
    const childResult =
      typeof queryNestedContacts === 'function'
        ? queryNestedContacts(initialChildObject, depth, 'child')
        : undefined;

    if (allowedContacts && allowedContacts.length > 0) {
      if (parentResult?.where) {
        parentResult.where.AND = parentResult.where.AND || [];
        parentResult.where.AND.push({ parent_id: { in: allowedContacts } });
      }
    }

    return { parentResult, childResult };
  };

  // @ts-expect-error
  _getBasicData = async (req: ExtNextApiRequest, queryNestedContacts) => {
    const accountSettings =
      await this.settingsService.getRoleSettingsByAccountAndRole(
        // @ts-expect-error
        req.account_id,
        // @ts-expect-error
        parseInt(req.role_id)
      );

    const allowedContacts = await this.getAllowedContacts(accountSettings, req);

    const { childResult } = this.getNestedContacts(
      // @ts-expect-error
      allowedContacts,
      '9',
      queryNestedContacts
    );

    const where: ContactWhereGetType = {
      // @ts-expect-error
      account_id: req.account_id,
      state: 'active',
      AND: [],
    };

    if (allowedContacts && allowedContacts.length > 0) {
      // @ts-expect-error
      where.AND.push({ id: { in: allowedContacts } });
    }

    // This query parameter comes from the contact group page it should return all
    // contacts that are in the group and level 0 contacts that doesn't have a group
    if ('contact_group_id_add' in req.query) {
      where.OR = [
        { contact_group_id: Number(req.query.contact_group_id_add) },
        // @ts-expect-error
        { contact_group_id: null },
      ];
      where.AND = [
        {
          parent_relationships: {
            none: {},
          },
        },
      ];
    }

    const data = await this.contactService.getDynamicSelectContacts(
      where,
      req,
      childResult
    );

    // Modify the contact response based on the data access config
    await this.processContacts(accountSettings, req.uid, data);
    return data;
  };

  // @ts-expect-error
  getAllowedContacts = async (accountSettings, req) => {
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let allowedUplineContacts;
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let allowedDownlineContacts;

    if (
      accountSettings?.agent_settings?.uplineHierarchyAccessLevel ||
      accountSettings?.agent_settings?.downlineHierarchyAccessLevel
    ) {
      const userInfo = await this.userService.getUserByUid(req.uid);
      if (userInfo?.user_contact[0]?.id) {
        if (accountSettings?.agent_settings?.uplineHierarchyAccessLevel)
          allowedUplineContacts =
            await this.contactService.getContactIdListByHierarchyDepth(
              userInfo.user_contact[0].id,
              +accountSettings?.agent_settings?.uplineHierarchyAccessLevel,
              'upline'
            );
        if (accountSettings?.agent_settings?.downlineHierarchyAccessLevel)
          allowedDownlineContacts =
            await this.contactService.getContactIdListByHierarchyDepth(
              userInfo.user_contact[0].id,
              +accountSettings?.agent_settings?.downlineHierarchyAccessLevel,
              'downline'
            );
      }
    }
    const allowedContacts = new Set([
      ...(allowedUplineContacts || []),
      ...(allowedDownlineContacts || []),
    ]);
    return Array.from(allowedContacts);
  };

  customizeSelectObject = (
    // @ts-expect-error
    isDynamicSelect,
    // @ts-expect-error
    isDetailedView,
    // @ts-expect-error
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    isListView,
    // @ts-expect-error
    parentResult,
    // @ts-expect-error
    childResult,
    // @ts-expect-error
    _roleId
  ) => {
    const selectObject = {
      id: true,
      str_id: true,
      email: true,
      first_name: true,
      last_name: true,
      nickname: true,

      // TODO: These should probably go into detailed view below,
      // but leaving as is, since we're likely going to revamp dynamic selects
      account_role_settings_id: true,
      account_role_settings: true,
      agent_code: true,
      bank_info: true,
      config: true,
      end_date: true,
      start_date: true,
      sync_id: true,
      sync_worker: true,
      // Common fields are always included
      created_at: isDynamicSelect ? undefined : true,
      created_by: isDynamicSelect ? undefined : true,
      updated_at: isDynamicSelect ? undefined : true,
      updated_by: isDynamicSelect ? undefined : true,
      contact_group_id: isDynamicSelect ? undefined : true,
      user_contact: isDynamicSelect
        ? undefined
        : {
            where: { state: 'active' },
            select: {
              state: true,
              account_user_roles: {
                select: {
                  account_id: true,
                  state: true,
                },
              },
            },
          },

      // Detailed view specific fields
      ...(isDetailedView && {
        agent_code: true,
        birthday: true,
        city: true,
        company_name: true,
        customer_id: true,
        country: true,
        gender: true,
        geo_state: true,
        level: true,
        middle_name: true,
        phone: true,
        phone_type: true,
        status: true,
        payable_status: true,
        title: true,
        type: true,
        user_str_id: true,
        zip: true,
        name: true,
        notes: true,
        phone2: true,
        phone2_type: true,
        reports: true,
        created_proxied_by: true,
        updated_proxied_by: true,
        balance: true,
        contact_group: {
          where: { state: 'active' },
        },
        contacts_agent_commission_schedule_profiles: {
          where: {
            state: 'active',
            agent_commission_schedule_profile: { state: 'active' },
          },
          select: {
            str_id: true,
            created_at: true,
            created_by: true,
            created_proxied_by: true,
            updated_at: true,
            updated_by: true,
            updated_proxied_by: true,
            contact_id: true,
            agent_commission_schedule_profile_id: true,
            start_date: true,
            end_date: true,
            multiplier: true,
            method: true,
            rate: true,
            config: true,
            downline_group_contact_id: true,
            payee_id: true,
            agent_commission_schedule_profile: {
              where: { state: 'active' },
            },
          },
        },
        contacts_agent_commission_schedule_profiles_sets: {
          where: {
            state: 'active',
            agent_commission_schedule_profiles_sets: { state: 'active' },
          },
          select: {
            str_id: true,
            created_at: true,
            created_by: true,
            created_proxied_by: true,
            updated_at: true,
            updated_by: true,
            updated_proxied_by: true,
            contact_id: true,
            agent_commission_schedule_profile_set_id: true,
            start_date: true,
            end_date: true,
            multiplier: true,
            method: true,
            rate: true,
            config: true,
            agent_commission_schedule_profiles_sets: {
              where: { state: 'active' },
              select: {
                id: true,
                str_id: true,
                created_at: true,
                created_by: true,
                created_proxied_by: true,
                updated_at: true,
                updated_by: true,
                updated_proxied_by: true,
                name: true,
                notes: true,
              },
            },
          },
        },
        parent_relationships: parentResult,
        child_relationships: childResult,
        contact_level: {
          where: { state: 'active' },
          select: {
            id: true,
            created_at: true,
            created_by: true,
            created_proxied_by: true,
            updated_at: true,
            updated_by: true,
            updated_proxied_by: true,
            level: true,
            level_label: true,
            product_type: true,
            sync_id: true,
            loa: true,
            company_id: true,
            contact_id: true,
            start_date: true,
            end_date: true,
          },
        },
        agency_contact_levels: {
          where: { state: DataStates.ACTIVE },
          select: {
            id: true,
            created_at: true,
            created_by: true,
            created_proxied_by: true,
            updated_at: true,
            updated_by: true,
            updated_proxied_by: true,
            level: true,
            level_label: true,
            product_type: true,
            sync_id: true,
            loa: true,
            company_id: true,
            contact_id: true,
            start_date: true,
            end_date: true,
          },
        },
        contact_memos: {
          where: { state: 'active' },
          select: {
            id: true,
            created_at: true,
            created_by: true,
            created_proxied_by: true,
            updated_at: true,
            updated_by: true,
            updated_proxied_by: true,
            approver_name: true,
            approver_contact_id: true,
            contact_id: true,
            description: true,
            end_date: true,
            recorded_by_user_id: true,
            remarks: true,
            start_date: true,
          },
        },
        contact_referrals: {
          where: { state: 'active' },
          select: {
            id: true,
            created_at: true,
            created_by: true,
            created_proxied_by: true,
            updated_at: true,
            updated_by: true,
            updated_proxied_by: true,
            config: true,
            contact_id: true,
            end_date: true,
            notes: true,
            referrer_contact_id: true,
            payer_contact_id: true,
            remarks: true,
            start_date: true,
            type: true,
            referrer: {
              select: { status: true },
            },
            payer: {
              select: { status: true },
            },
          },
        },
        saved_reports: {
          where: {
            state: 'active',
            AND: [
              {
                OR: [
                  { status: SavedReportStatuses.APPROVED },
                  { status: SavedReportStatuses.PAID },
                ],
              },
            ],
          },
          select: {
            id: true,
            str_id: true,
            created_at: true,
            created_by: true,
            created_proxied_by: true,
            updated_at: true,
            updated_by: true,
            updated_proxied_by: true,
            name: true,
          },
        },
      }),
    };
    return selectObject;
  };

  // @ts-expect-error
  _getData = async (req, queryNestedContacts = null) => {
    const accountSettings =
      await this.settingsService.getRoleSettingsByAccountAndRole(
        req.account_id,
        req.role_id
      );
    const allowedContacts = await this.getAllowedContacts(accountSettings, req);

    const {
      page,
      limit,
      orderBy = 'created_at',
      sort = 'desc',
      q = '',
      hierarchy_depth = '0',
    } = req.query;

    const queryTerms = q
      .split(' ')
      // @ts-expect-error
      .map((s) => s.trim())
      // @ts-expect-error
      .filter((s) => s);

    const where: ContactWhereGetType = {
      account_id: req.account_id,
      state: 'active',
      // @ts-expect-error
      AND: queryTerms.map((queryTerm) => ({
        OR: [
          { first_name: { contains: queryTerm, mode: 'insensitive' } },
          { last_name: { contains: queryTerm, mode: 'insensitive' } },
          { nickname: { contains: queryTerm, mode: 'insensitive' } },
          { email: { contains: queryTerm, mode: 'insensitive' } },
          { agent_code: { contains: queryTerm, mode: 'insensitive' } },
        ],
      })),
    };

    if (allowedContacts && allowedContacts.length > 0) {
      // @ts-expect-error
      where.AND.push({ id: { in: allowedContacts } });
    }

    if (req.query?.id) {
      where.str_id = req.query.id;
    }
    if (req.query?.contact_group_id) {
      where.contact_group_id = Number(req.query.contact_group_id);
    }
    if (req.query?.user_contact) {
      where.OR = [
        { user_str_id: String(req.query.user_contact) },
        // @ts-expect-error
        { user_str_id: null },
      ];
    }

    // Special handling for TransGlobal - they have too many agents and we want to filter out status=inactive by default
    // TODO: Implement a way to do this that's configurable by account managers
    const showInactive = req.query?.show_inactive === 'true';
    if (
      req.account_id === AccountIds.TRANSGLOBAL &&
      !showInactive &&
      !req.query?.id
    ) {
      where.status = { not: 'inactive', mode: 'insensitive' };
    }

    const { skip, take } = calculateSkipAndTake({
      page,
      limit,
    });

    const isDynamicSelect = req.query.is_dynamic_select === 'true';
    const isDetailedView = req.query.is_detailed_view === 'true';
    const isListView = req.query.is_list_view === 'true';
    const getParentChildren = req.query.get_parent_children === 'true';

    const { parentResult, childResult } = this.getNestedContacts(
      // @ts-expect-error
      allowedContacts,
      hierarchy_depth,
      queryNestedContacts,
      getParentChildren
    );

    const selectObject = this.customizeSelectObject(
      isDynamicSelect,
      isDetailedView,
      isListView,
      parentResult,
      childResult,
      req.role_id
    );

    const { data, count } =
      await this.contactService.getCustomizeSelectContacts(
        where,
        take,
        skip,
        orderBy,
        sort,
        selectObject
      );

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((contact) => {
      if (+req.role_id === Roles.PRODUCER && allowedContacts) {
        const childRelationships = (
          contact.child_relationships as ChildRelationshipWithContact[]
        )
          // @ts-expect-error
          ?.filter((child) => allowedContacts.includes(child?.contact?.id));

        set(contact, 'child_relationships', childRelationships);
      }
    });
    // Modify the contact response based on the data access config
    await this.processContacts(accountSettings, req.uid, data);

    return { data: data, count: count };
  };

  // @ts-expect-error
  findLevel = (contact, id, level = 0, direction = null) => {
    if (contact?.id === id) {
      return { level, direction };
    }

    if (contact?.parent_relationships)
      for (const relationship of contact.parent_relationships) {
        // 'downline' is used here because we're moving down from the parent to the child in the hierarchy
        // @ts-expect-error
        const found = this.findLevel(
          relationship.parent,
          id,
          level + 1,
          // @ts-expect-error
          'downline'
        );
        if (found.level !== -1) {
          return found;
        }
      }

    if (contact?.child_relationships)
      for (const relationship of contact.child_relationships) {
        // 'upline' is used here because we're moving up from the child to the parent in the hierarchy
        // @ts-expect-error
        const found = this.findLevel(
          relationship.contact,
          id,
          level + 1,
          // @ts-expect-error
          'upline'
        );
        if (found.level !== -1) {
          return found;
        }
      }

    return { level: -1, direction: null };
  };

  modifyContact = (
    // @ts-expect-error
    contact,
    // @ts-expect-error
    config,
    isExtended = false,
    extendedType = '',
    extendedConfig = {}
  ) => {
    // Modify the contact based on the config
    if (config && config.emailConfig === 'No') {
      contact.email = '';
    }
    if (config && config.nameConfig === 'First name, last initial') {
      contact.last_name = `${contact.last_name?.charAt(0)}.`;
    }
    if (config && config.nameConfig === 'Initials') {
      contact.first_name = `${contact.first_name?.charAt(0)}.`;
      contact.last_name = `${contact.last_name?.charAt(0)}.`;
    }

    if (isExtended) {
      switch (extendedType) {
        case 'upline':
          // @ts-expect-error
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          contact.parent_relationships?.forEach((relationship) => {
            this.modifyContact(
              relationship.parent,
              extendedConfig,
              isExtended,
              'upline',
              extendedConfig
            );
          });
          break;
        case 'downline':
          // @ts-expect-error
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          contact.child_relationships?.forEach((relationship) => {
            this.modifyContact(
              relationship.contact,
              extendedConfig,
              isExtended,
              'downline',
              extendedConfig
            );
          });
          break;
      }
    }
  };

  // @ts-expect-error
  processContacts = async (accountSettings, uid, data) => {
    if (
      accountSettings?.agent_settings?.directUplineDataAccess ||
      accountSettings?.agent_settings?.directDownlineDataAccess ||
      accountSettings?.agent_settings?.extendedUplineDataAccess ||
      accountSettings?.agent_settings?.extendedDownlineDataAccess
    ) {
      const userInfo = await this.userService.getUserByUid(uid);
      const userContactId = userInfo?.user_contact[0]?.id;
      if (userContactId) {
        // @ts-expect-error
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        data.forEach((contact) => {
          const { level, direction } = this.findLevel(contact, userContactId);
          if (level === 0) {
            if (Array.isArray(contact.parent_relationships)) {
              // @ts-expect-error
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              contact.parent_relationships.forEach((relationship) => {
                this.modifyContact(
                  relationship.parent,
                  accountSettings.agent_settings.directUplineDataAccess
                );
              });
            }
            if (Array.isArray(contact.child_relationships)) {
              // @ts-expect-error
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              contact.child_relationships.forEach((relationship) => {
                this.modifyContact(
                  relationship.contact,
                  accountSettings.agent_settings.directDownlineDataAccess
                );
              });
            }
          }

          if (direction === 'upline' && level === 1) {
            this.modifyContact(
              contact,
              accountSettings.agent_settings.directUplineDataAccess,
              true,
              'upline',
              accountSettings?.agent_settings?.extendedUplineDataAccess
                ? accountSettings?.agent_settings?.extendedUplineDataAccess
                : accountSettings.agent_settings.directUplineDataAccess
            );
          }
          if (direction === 'upline' && level > 1) {
            this.modifyContact(
              contact,
              accountSettings.agent_settings.extendedUplineDataAccess,
              true,
              'upline',
              accountSettings.agent_settings.extendedUplineDataAccess
            );
          }
          if (direction === 'downline' && level === 1) {
            this.modifyContact(
              contact,
              accountSettings.agent_settings.directDownlineDataAccess,
              true,
              'downline',
              accountSettings?.agent_settings?.extendedDownlineDataAccess
                ? accountSettings?.agent_settings?.extendedDownlineDataAccess
                : accountSettings.agent_settings.directDownlineDataAccess
            );
          }
          if (direction === 'downline' && level > 1) {
            this.modifyContact(
              contact,
              accountSettings.agent_settings.extendedDownlineDataAccess,
              true,
              'downline',
              accountSettings.agent_settings.extendedDownlineDataAccess
            );
          }
        });
      }
    }
  };
}
