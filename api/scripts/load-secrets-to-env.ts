import fs from 'node:fs';
import path from 'node:path';
import dotenv from 'dotenv';

import { getSecret } from '../services/secret-manager';

const ENV_KEYS = [
  'AI_ENDPOINT',
  'APP_ID',
  'AUTH_DOMAIN',
  'BEN<PERSON>IT_POINT_ENDPOINT',
  'SMART_OFFICE_ENDPOINT',
  'CLOUD_TASKS_JSON',
  'CLOUD_TASKS_URL',
  'CLOUD_TASKS_URL_PREVIEW',
  'CLOUD_TASKS_URL_DEV',
  'CLOUD_TASKS_URL_PROD',
  'DATABASE_URL',
  'DOCUMENT_AI_PROCESSOR_ID',
  'ENCODER_URL',
  'EXTRACT_TABLE_KEY',
  'EXTRACT_TABLE_TRIGGER',
  'EXTRACT_TABLE_GET_RESULT',
  'GHOST_SCRIPT_OPTIMIZER',
  'FIREBASE_JSON',
  'FIREBASE_PROJECT_ID',
  'NEXTAUTH_URL',
  'PROJECT_ID',
  'PROJECT_NUMBER',
  'SECRET',
  'SENDGRID_API_KEY',
  'SENTRY_IGNORE_API_RESOLUTION_ERROR',
  'SERVICE_ACCOUNT_EMAIL',
  'STORAGE_BUCKET',
  'TRANSGLOBAL_SECRET',
  'TRANSGLOBAL_SIGNIN_URL',
  'TRANSGLOBAL_KEY',
  'STATSIG_API_KEY',
  'ENVIRONMENT',
  'ONE_HQ_ENDPOINT',
  'RUNTIME',
  'TZ',
  'ADOBE_PDF_SERVICES_CLIENT_ID',
  'ADOBE_PDF_SERVICES_CLIENT_SECRET',
  'NANONETS_MODEL_ID',
  'NANONETS_API_KEY',
  'DOCUMENT_CLASSIFICATION_URL',
  'DOCUMENT_CLASSIFICATION_TOKEN',
  'NEXT_RUNTIME',
  'NODE_ENV',
  'CLOUD_WORKER_URL_PROD',
  'CLOUD_WORKER_URL_DEV',
  'CLOUD_WORKER_URL_PREVIEW',
  'DOCUMENT_CLASSIFICATION_URL_PROD',
  'DOCUMENT_CLASSIFICATION_URL_DEV',
  'DOCUMENT_CLASSIFICATION_URL_PREVIEW',
  'WORKER_BATCH_SIZE',
  'FE_URL',
  'GOOGLE_AUTH_CLIENT_ID',
  'GOOGLE_AUTH_CLIENT_SECRET',
  'GOOGLE_AUTH_REDIRECT_URI',
  'GOOGLE_AUTH_SCOPES',
  'UPLOADS_EMAIL',
  'EXTRACT_TABLE_VALIDATOR',
  'PDF_OPTIMIZER_TOKEN',
  'OPENAI_API_KEY',
  'CLAUDE_API_KEY',
  'GROK_API_KEY',
  'STRIPE_SECRET_KEY',
  'STRIPE_WEBHOOK_SECRET',
  'PLAID_CLIENT_ID',
  'PLAID_SECRET',
  'PLAID_CLIENT_NAME',
  'PLAID_REDIRECT_URI',
  'LOG_LEVEL',
  'GCLOUD_LOGGING',
];

function parseEnvFile(content: string) {
  // Use dotenv to parse env file content
  return dotenv.parse(content);
}

function serializeEnvFile(env: Record<string, string>) {
  return `${Object.entries(env)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n')}\n`;
}

async function main() {
  const fileName = process.argv[2] || '.env';
  const envPath = path.resolve(process.cwd(), fileName);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.log('Resolved env path:', envPath);

  let existingEnv: Record<string, string> = {};
  if (fs.existsSync(envPath)) {
    const content = fs.readFileSync(envPath, 'utf-8');
    existingEnv = parseEnvFile(content);
  }

  // Fetch all secrets in parallel
  const secretResults = await Promise.all(
    ENV_KEYS.map(async (key) => {
      try {
        // Special case for DATABASE_URL: load DATABASE_URL_LOCAL
        if (key === 'DATABASE_URL') {
          const secret = await getSecret('DATABASE_URL_LOCAL');
          return { key, value: secret ?? '' };
        }
        const secret = await getSecret(key);
        return { key, value: secret ?? '' };
      } catch (err) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(`❌ Failed to fetch secret for ${key}:`, err);
        return { key, value: '' };
      }
    })
  );

  // Overwrite or add keys from secrets
  for (const { key, value } of secretResults) {
    if (value !== '') {
      existingEnv[key] = key === 'DATABASE_URL' ? `'${value}'` : value;
    } else if (!(key in existingEnv)) {
      // If the key is not in the file and secret is empty, add as empty
      existingEnv[key] = '';
    }
  }

  try {
    fs.writeFileSync(envPath, serializeEnvFile(existingEnv), 'utf-8');
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.log('✅ Secrets loaded and written to', fileName);
  } catch (err) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error('❌ Failed to write to env file:', err);
    throw err;
  }
}

main().catch((err) => {
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.error('Failed to load secrets to env:', err);
  process.exit(1);
});
