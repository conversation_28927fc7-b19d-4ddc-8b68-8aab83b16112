import { config } from 'dotenv';
import trim from 'lodash-es/trim';
import uniq from 'lodash-es/uniq';
import type { PrismaClient } from '@prisma/client/extension';

import type { Data } from '../seed-local-database';

const getDatabaseUrl = (envPath: string): string => {
  const result = config({ path: envPath });
  if (result.error) {
    throw result.error;
  }
  if (!result.parsed || !result.parsed.DATABASE_URL) {
    throw new Error(`DATABASE_URL not found in ${envPath}`);
  }
  return result.parsed.DATABASE_URL;
};

const getPrimaryKeyValuesInTableData = (args: {
  foreignKey: string;
  tableData: Data[];
  isSelfReferencing: boolean;
}) => {
  const primaryKeyValues = [];

  for (const item of args.tableData) {
    const foreignIdOrStrId = item[args.foreignKey] as number | string;

    const isValid =
      foreignIdOrStrId !== null &&
      foreignIdOrStrId !== undefined &&
      // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      ((typeof foreignIdOrStrId === 'number' && !isNaN(foreignIdOrStrId)) ||
        (typeof foreignIdOrStrId === 'string' &&
          trim(foreignIdOrStrId).length > 0));

    if (!isValid) {
      continue;
    }

    if (args.isSelfReferencing) {
      const alreadyExists = args.tableData.some(
        (item) =>
          item.id === foreignIdOrStrId || item.str_id === foreignIdOrStrId
      );

      if (alreadyExists) {
        continue;
      }
    }

    primaryKeyValues.push(foreignIdOrStrId);
  }

  return uniq(primaryKeyValues);
};

const isDebugMessage = (msg: string) => {
  return msg.includes('Waiting for the debugger to disconnect...');
};

const getAvailableTablesInClient = (client: PrismaClient) => {
  const CONFIG_TABLES_PREFIX = ['$', '_'];

  return Object.keys(client)
    .filter((key) => {
      return CONFIG_TABLES_PREFIX.every((prefix) => !key.startsWith(prefix));
    })
    .sort((a, b) => a.localeCompare(b));
};

const sleep = (time: number) =>
  new Promise((resolve) => setTimeout(resolve, time));

export {
  getDatabaseUrl,
  getPrimaryKeyValuesInTableData,
  isDebugMessage,
  getAvailableTablesInClient,
  sleep,
};
