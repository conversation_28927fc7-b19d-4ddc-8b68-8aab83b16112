import type { Prisma, PrismaClient, account_configs } from '@prisma/client';
import { injectable } from 'inversify';
import type { Entity } from 'common/dto/data_processing/sync';
import type { WorkerNames } from 'common/constants';

import { prismaClient } from '@/lib/prisma';
import type { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';
import { DataStates } from '@/types';

@injectable()
export class AccountProcessorConfigService {
  async getAccountConfigByType<T>(
    filter: { type: string; account_id: string; states?: DataStates[] },
    prisma: Prisma.TransactionClient | PrismaClient = prismaClient
  ) {
    const { states, ...restFilter } = filter;
    if (!filter.account_id || !filter.type) {
      return null;
    }
    const configItems = await prisma.account_configs.findMany({
      where: {
        ...restFilter,
        state: states ? { in: states } : DataStates.ACTIVE,
      },
    });
    return configItems as (account_configs & { value: T })[];
  }

  async getWorkerConfig<T>(query: { account_id: string; worker: string }) {
    if (!query.account_id) {
      return null;
    }
    const configs = await this.getAccountConfigByType<T>({
      account_id: query.account_id,
      type: 'dataSync',
    });
    if (configs?.length === 1) {
      return configs?.[0];
    }
    return configs?.find(
      (config) => (config.value as { worker: string })?.worker === query.worker
    );
  }

  async updateWorkerLastSyncedAt(params: {
    account_id: string;
    worker: string;
    lastSyncedAt: string;
  }) {
    const config = await this.getWorkerConfig<
      ConfigItemValueForDataSync<unknown>
    >({ account_id: params.account_id, worker: params.worker });
    if (!config) return null;
    const value: ConfigItemValueForDataSync<unknown> = {
      ...(config.value as unknown as ConfigItemValueForDataSync<unknown>),
      lastSyncedAt: params.lastSyncedAt,
    };

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    return await (prismaClient.account_configs.update as any)({
      where: { id: config.id },
      data: { value },
    });
  }

  async listConfigs(
    filter: { type?: string; account_id?: string } = {},
    prisma: Prisma.TransactionClient | PrismaClient = prismaClient
  ) {
    const configs = await (
      prisma as typeof prismaClient
    ).account_configs.findMany({
      where: { ...filter, state: 'active', type: 'dataSync' },
      accountInject: false,
    });
    return configs as (account_configs & {
      value: ConfigItemValueForDataSync<unknown>;
    })[];
  }

  async getDataSyncConfig(accountId: string) {
    const configItem = await this.getAccountConfigByType<
      ConfigItemValueForDataSync<unknown>
    >({
      type: 'dataSync',
      account_id: accountId,
    });
    // @ts-expect-error
    return configItem[0]?.value || { entities: [] };
  }
  async getDataSyncConfigs(accountId: string) {
    const configItems = await this.getAccountConfigByType<
      ConfigItemValueForDataSync<unknown>
    >({
      type: 'dataSync',
      account_id: accountId,
    });
    return configItems?.map((item) => item.value) || [];
  }

  async getEntitiesToSync(
    accountId: string,
    entities: Entity[],
    worker?: WorkerNames
  ) {
    const configs = await this.getDataSyncConfigs(accountId);
    const config = worker
      ? configs.find((c) => c.worker === worker)
      : configs[0];
    return entities.filter((entity) => config?.entities?.includes(entity));
  }
}
