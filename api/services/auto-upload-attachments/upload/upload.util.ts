import type { users } from '@prisma/client';
import { nanoid } from 'nanoid';
import type { GetSignedUrlConfig } from '@google-cloud/storage';
import { UploadSource } from 'common/globalTypes';

import prisma from '@/lib/prisma';
import type { UploadParams } from '@/services/auto-upload-attachments/types';
import { getMimeType } from '@/lib/helpers';
import { storage } from '@/lib/firebase-admin';
import { getFileType } from 'common/tools/getFileType';

export const getFilePath = (fileName: string, accountId: string) => {
  const filename = `${nanoid()}-${fileName}`;
  let filePath = 'uploads/';
  if (accountId) {
    filePath += `${accountId}/`;
  }
  filePath += filename;
  return filePath;
};

export const generateDocumentBody = async (file: File, accountId: string) => {
  const filePath = getFilePath(file.name, accountId);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.log('filePath', filePath);
  const file_hash = await getChecksumSha256(file);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.log('file_hash', file_hash);
  const fileType = getFileType(file.name);

  return {
    filename: file.name,
    file_path: filePath,
    type: file.type,
    method: '',
    processor: '',
    file_hash,
    status: 'new',
    company_str_id: null,
    tag: null,
    bank_total_amount: null,
    check_date: null,
    deposit_date: null,
    upload_source: UploadSource.EMAIL,
    file_type: fileType,
  };
};

export const handleDocumentParser = async (
  file: File,
  accountId: string,
  sender: users,
  syncId: string
) => {
  const body = await generateDocumentBody(file, accountId);

  const updateData = {
    ...body,
    uid: sender?.uid,
    account_id: accountId,
    str_id: generateStrId(),
    created_by: sender?.uid,
    created_proxied_by: sender?.uid,
    sync_id: syncId,
  };

  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.log('updateData', updateData);

  await prisma.documents.create({
    data: updateData,
  });

  return updateData;
};

export const handleSignedUrl = async (params: UploadParams) => {
  const entity = await getFirebaseEntity(params);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.log('Signed URL entity:', entity);
  const filePath = getEntityFilePath(entity);
  const { url } = await getSignedUrl(params, filePath);

  return url;
};

const getFirebaseEntity = async (params: UploadParams) => {
  const { endpoint_str_id, accountId } = params;

  const whereClause = {
    str_id: endpoint_str_id,
    account_id: accountId,
    state: 'active',
  };

  const entity = await prisma.documents.findUnique({
    where: whereClause,
  });

  return entity;
};

// @ts-expect-error
const getEntityFilePath = (entity): string => {
  return entity.file_path || entity.override_file_path;
};

const getSignedUrl = async (
  params: UploadParams,
  filePath: string,
  autoGenerateAction = false
): Promise<{ error?: string; url: string; exists?: boolean }> => {
  try {
    const { action } = params;
    let urlAction = action;

    const fileRef = storage.file(filePath);
    const [exists] = await fileRef.exists();

    if (autoGenerateAction) {
      if (!exists) {
        urlAction = 'write';
      } else {
        urlAction = 'read';
      }
    }

    if (urlAction === 'read') {
      if (!exists) {
        return {
          url: '',
        };
      }
    }

    const mimeType = getMimeType(filePath);
    const expiresTime = 1000 * 60 * 15;
    const options: GetSignedUrlConfig = {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      action: urlAction as any,
      expires: Date.now() + expiresTime,
      contentType: mimeType,
    };

    const [url] = await fileRef.getSignedUrl(options);
    return {
      url,
      exists,
    };
  } catch (error) {
    // @ts-expect-error
    return { error: error.message, url: '' };
  }
};

async function getChecksumSha256(file: File): Promise<string> {
  const arrayBuffer = await file.arrayBuffer();

  // Compute the SHA-256 hash
  const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);

  // Convert the hash to a hexadecimal string
  const hashHex = Array.from(new Uint8Array(hashBuffer))
    .map((byte) => byte.toString(16).padStart(2, '0'))
    .join('');

  return hashHex;
}

const generateStrId = (): string => {
  // Define the allowed characters for the `str_id`
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-';

  // Generate 21 random characters
  let strId = '';
  for (let i = 0; i < 21; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    strId += characters[randomIndex];
  }

  return strId;
};

export const convertMsToSeconds = (ms: number): number => {
  return Math.floor(ms / 1000);
};

export const isUploadedFile = async (syncId: string) => {
  const result = await prisma.documents.findFirst({
    where: {
      sync_id: syncId,
      state: 'active',
    },
    select: {
      id: true,
      str_id: true,
      sync_id: true,
    },
    accountInject: false,
  });

  return Boolean(result);
};
