import { beforeEach, describe, expect, it } from 'vitest';
import { CommissionCalculationProfileService } from './comp-profile';

describe('CommissionCalculationProfileService', () => {
  let service: CommissionCalculationProfileService;

  beforeEach(async () => {
    service = new CommissionCalculationProfileService();
  });

  describe('getEffectiveDateRangeCondition', () => {
    it('Given no effective date, should return an empty object', () => {
      const result = service.getEffectiveDateRangeCondition();
      expect(result).toEqual({});
    });

    it('Given an effective date, should return the correct date range condition', () => {
      const effectiveDate = new Date();
      const result = service.getEffectiveDateRangeCondition(effectiveDate);

      expect(result).toEqual({
        OR: [
          {
            start_date: { lte: effectiveDate },
            end_date: { gte: effectiveDate },
          },
          {
            start_date: { lte: effectiveDate },
            end_date: null,
          },
          {
            start_date: null,
            end_date: { gte: effectiveDate },
          },
          {
            start_date: null,
            end_date: null,
          },
        ],
      });
    });
  });
});
