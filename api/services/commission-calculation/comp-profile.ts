import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';
import { injectable } from 'inversify';

@injectable()
export class CommissionCalculationProfileService {
  getEffectiveDateRangeCondition(effectiveDate?: Date) {
    if (!effectiveDate) return {};

    return {
      OR: [
        {
          start_date: { lte: effectiveDate },
          end_date: { gte: effectiveDate },
        },
        {
          start_date: { lte: effectiveDate },
          end_date: null,
        },
        {
          start_date: null,
          end_date: { gte: effectiveDate },
        },
        {
          start_date: null,
          end_date: null,
        },
      ],
    };
  }

  async getCompProfiles(query: { contactIds: number[]; effectiveDate?: Date }) {
    const { contactIds, effectiveDate } = query;

    if (contactIds.length === 0) {
      return { compProfileSets: [], profiles: [] };
    }
    const [profileSets, profiles] = await Promise.all([
      prismaClient.contacts_agent_commission_schedule_profiles_sets.findMany({
        where: {
          state: DataStates.ACTIVE,
          agent_commission_schedule_profiles_sets: { state: DataStates.ACTIVE },
          contact: { state: DataStates.ACTIVE, id: { in: contactIds } },
          ...(effectiveDate
            ? this.getEffectiveDateRangeCondition(effectiveDate)
            : {}),
        },
        include: {
          agent_commission_schedule_profiles_sets: {
            where: { state: DataStates.ACTIVE },
            include: {
              commission_profiles: {
                where: { state: DataStates.ACTIVE },
              },
            },
          },
        },
      }),

      prismaClient.contacts_agent_commission_schedule_profiles.findMany({
        where: {
          state: DataStates.ACTIVE,
          agent_commission_schedule_profile: { state: DataStates.ACTIVE },
          contact: { state: DataStates.ACTIVE, id: { in: contactIds } },
        },
        include: {
          agent_commission_schedule_profile: {
            where: { state: DataStates.ACTIVE },
          },
          contact: { where: { state: DataStates.ACTIVE } },
        },
      }),
    ]);

    return { compProfileSets: profileSets, profiles };
  }
}
