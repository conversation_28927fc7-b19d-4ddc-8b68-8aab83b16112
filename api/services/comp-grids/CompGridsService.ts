import { inject, injectable } from 'inversify';

import type CompGridCriteriaRecord from '@/persistence/comp-grid-criteria/CompGridCriteriaRecord';
import type ICompGridCriteriaRepo from '@/persistence/comp-grid-criteria/ICompGridCriteriaRepo';
import type ICompGridProductsRepo from '@/persistence/comp-grid-products/ICompGridProductsRepo';
import type { CompGridCriteria } from './types';
import type CompGridProductRecord from '@/persistence/comp-grid-products/CompGridProductRecord';
import { REPOSITORY_TYPES } from '@/constants';
import type {
  GetCompGridsInput,
  GetCompGridsResponse,
  ICompGridsService,
} from './ICompGridsService';
import type { ICompGridRepo } from '../../persistence/comp-grid/ICompGridRepo';
import { type comp_grids, prismaClient } from '@/lib/prisma';

export interface GetCriteriaByPkIdInput {
  account_id: string;
  criteria_pk_ids: number[];
}

export interface GetCriteriaByPkIdOutput {
  comp_grid_criteria: CompGridCriteria[];
}

export interface FindCompanyProductsForCompGridProductInput {
  account_id: string;
  // TODO(toby): Refactor this endpoint to use str_id
  comp_grid_product_pk_id: number;
}

export interface FindCompanyProductsForCompGridProductOutput {
  // TODO(toby): Refactor this endpoint to return str_ids.
  company_product_pk_ids: number[];
}

const verifyCriteriaAccess = (
  account_id: string,
  record: CompGridCriteriaRecord
): void => {
  const { access, account_id: record_account_id, str_id } = record;
  if (access !== 'global' && record_account_id !== account_id) {
    // Deny access
    throw new Error(`Unauthorized comp_grid_criteria access. (${str_id})`);
  }
};

const verifyProductAccess = (
  account_id: string,
  record: CompGridProductRecord
): void => {
  const { access, account_id: record_account_id, str_id } = record;
  if (access !== 'global' && record_account_id !== account_id) {
    // Deny access
    throw new Error(`Unauthorized comp_grid_criteria access. (${str_id})`);
  }
};

@injectable()
export default class CompGridsService implements ICompGridsService {
  @inject<ICompGridCriteriaRepo>(REPOSITORY_TYPES.CompGridCriteriaRepository)
  // @ts-expect-error
  private readonly criteriaRepo: ICompGridCriteriaRepo;
  @inject<ICompGridProductsRepo>(REPOSITORY_TYPES.CompGridProductsRepository)
  // @ts-expect-error
  private readonly productsRepo: ICompGridProductsRepo;

  constructor(
    @inject<ICompGridRepo>(REPOSITORY_TYPES.CompGridRepository)
    private compGridRepository: ICompGridRepo
  ) {}

  private mapCriteriaRecord(record: CompGridCriteriaRecord): CompGridCriteria {
    const {
      id,
      str_id,
      account_id,
      state,
      access,
      comp_grid_id,
      company_id,
      compensation_type,
      compensation_type_alternative,
      filter_date_field,
      grid_product_id,
      grid_product_option_id,
      issue_age_end,
      issue_age_start,
      premium_min,
      premium_max,
      policy_year_end,
      policy_year_start,
      transaction_type,
      payment_mode,
    } = record;

    const result: CompGridCriteria = {
      id,
      // @ts-expect-error
      str_id,
      // @ts-expect-error
      account_id,
      state,
      access,
      comp_grid_id,
      company_id,
      compensation_type,
      compensation_type_alternative,
      filter_date_field,
      grid_product_id,
      grid_product_option_id,
      issue_age_end,
      issue_age_start,
      // @ts-expect-error
      premium_min: premium_min?.toNumber(),
      // @ts-expect-error
      premium_max: premium_max?.toNumber(),
      policy_year_end,
      policy_year_start,
      transaction_type,
      payment_mode,
    };
    return result;
  }

  async getCriteriaByPkId(
    input: GetCriteriaByPkIdInput
  ): Promise<GetCriteriaByPkIdOutput> {
    const { account_id, criteria_pk_ids } = input;

    const records = await this.criteriaRepo.getByPkId(criteria_pk_ids);
    // Verify access allowed
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    records.forEach((record) => verifyCriteriaAccess(account_id, record));

    const comp_grid_criteria = records.map((record) =>
      this.mapCriteriaRecord(record)
    );

    return { comp_grid_criteria };
  }

  async findCompanyProductForCompGridProduct(
    input: FindCompanyProductsForCompGridProductInput
  ): Promise<FindCompanyProductsForCompGridProductOutput> {
    const { account_id, comp_grid_product_pk_id } = input;

    const [productRecord] = await this.productsRepo.getByPkId([
      comp_grid_product_pk_id,
    ]);
    if (!productRecord) {
      return { company_product_pk_ids: [] };
    }

    // Verify access allowed
    verifyProductAccess(account_id, productRecord);

    const {
      company_products_by_comp_grid_product:
        company_product_ids_by_comp_grid_product_ids,
    } = await this.productsRepo.findRelatedCompanyProducts([productRecord.id]);

    const company_product_pk_ids =
      company_product_ids_by_comp_grid_product_ids[productRecord.id] ?? [];

    return { company_product_pk_ids };
  }

  async getCompGridsByIds(ids: number[]): Promise<comp_grids[]> {
    if (!ids?.length) {
      return [];
    }
    return prismaClient.comp_grids.findMany({
      where: { id: { in: ids } },
    });
  }

  async getCompGrids(input: GetCompGridsInput): Promise<GetCompGridsResponse> {
    /**
     * TODO: returning find results directly for now, the service logic should be moved from the controller to the service
     *       controller: api/pages/api/comp-grids/view/[[...params]].ts
     *       ticket: https://linear.app/fintary/issue/PLT-2475
     */
    return this.compGridRepository.findManyBy(input);
  }
}
