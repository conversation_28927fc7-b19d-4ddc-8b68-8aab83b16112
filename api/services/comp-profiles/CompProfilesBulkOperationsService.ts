import { inject, injectable } from 'inversify';
import chunk from 'lodash-es/chunk';
import uniq from 'lodash-es/uniq';
import { PrismaClient } from '@prisma/client';

import type { CompProfile } from './types';

import type {
  ICompProfilesBulkOperationsService,
  UpdateCompProfilesBulkAgentAssignInput,
} from './ICompProfilesBulkOperationsService';
import { BusinessException } from '../../lib/exceptionHandler';
import type { BulkOperationResponse } from 'common/bulk-operations/bulk-entities-operation/bulk-entities-operation';

export interface FindCompProfilesByAccountInput {
  account_id: string;
}

export interface FindCompProfilesByAccountOutput {
  comp_profiles: CompProfile[];
}

@injectable()
export class CompProfilesBulkOperationsService
  implements ICompProfilesBulkOperationsService
{
  constructor(
    @inject(PrismaClient)
    private readonly prisma: PrismaClient
  ) {}

  public async updateCompProfilesBulkAgentAssign(
    input: UpdateCompProfilesBulkAgentAssignInput
  ): Promise<BulkOperationResponse> {
    const { userContext, rows } = input;

    if (!rows || rows.length === 0) {
      return { totalSuccess: 0, failures: [] };
    }

    const batches = chunk(rows, 10);

    let rowLineCount = 0;
    const failedUpdates: BulkOperationResponse['failures'] = [];

    for (const batch of batches) {
      const compProfileStrIdList = uniq(
        batch.map((item) => item.compProfileStrId)
      );
      const contactStrIdList = uniq(batch.map((item) => item.contactStrId));

      const { found, notFound } =
        await this.getCompProfilesIdAndContactsIdByStrId({
          compProfileStrIdList,
          contactStrIdList,
        });

      const updates: Array<{
        compProfileId: number;
        contactId: number;
        multiplier?: number;
        startDate?: string;
        endDate?: string;
        hierarchyProcessing?: string;
      }> = [];

      for (const item of batch) {
        rowLineCount++;

        const notFoundCompProfile = notFound.compProfileStrIdList.includes(
          item.compProfileStrId
        );
        const notFoundContact = notFound.contactStrIdList.includes(
          item.contactStrId
        );

        if (notFoundCompProfile || notFoundContact) {
          const messages = [
            notFoundCompProfile
              ? `Comp Profile not found: ${item.compProfileStrId}`
              : undefined,
            notFoundContact
              ? `Contact not found: ${item.contactStrId}`
              : undefined,
          ].filter(Boolean) as string[];

          failedUpdates.push({
            rowNumber: rowLineCount,
            errorMessages: messages,
          });
        } else {
          const compProfileId = found.compProfileList.find(
            ({ strId }) => strId === item.compProfileStrId
          )?.id;

          const contactId = found.contactList.find(
            ({ strId }) => strId === item.contactStrId
          )?.id;

          if (!compProfileId || !contactId) {
            throw new BusinessException(
              'Comp Profile id or Contact id should be defined. Investigation required'
            );
          }

          updates.push({
            ...item,
            contactId,
            compProfileId,
            hierarchyProcessing: item.applyToDownlines ? 'downlines' : 'self',
          });
        }
      }

      await this.prisma.contacts_agent_commission_schedule_profiles.createMany({
        data: updates.map((item) => ({
          agent_commission_schedule_profile_id: item.compProfileId,
          contact_id: item.contactId,
          account_id: userContext.accountId,
          start_date: item.startDate ? new Date(item.startDate) : null,
          end_date: item.endDate ? new Date(item.endDate) : null,
          hierarchy_processing: item.hierarchyProcessing,
          multiplier: item.multiplier,
          uid: userContext.userId,
          created_at: new Date(),
          created_by: userContext.userId,
          created_proxied_by: userContext.proxiedId,
        })),
      });
    }

    const totalSuccess = rows.length - failedUpdates.length;

    return { totalSuccess, failures: failedUpdates };
  }

  private async getCompProfilesIdAndContactsIdByStrId(input: {
    compProfileStrIdList: string[];
    contactStrIdList: string[];
  }): Promise<{
    found: {
      compProfileList: Array<{ strId: string; id: number }>;
      contactList: Array<{ strId: string | null; id: number }>;
    };
    notFound: {
      compProfileStrIdList: string[];
      contactStrIdList: string[];
    };
  }> {
    const existingCompProfiles =
      await this.prisma.agent_commission_schedule_profiles.findMany({
        where: { str_id: { in: input.compProfileStrIdList } },
        select: { str_id: true, id: true },
      });

    const existingContacts = await this.prisma.contacts.findMany({
      where: { str_id: { in: input.contactStrIdList } },
      select: { str_id: true, id: true },
    });

    const notFoundCompProfiles = input.compProfileStrIdList.filter(
      (id) => !existingCompProfiles.some((profile) => profile.str_id === id)
    );

    const notFoundContacts = input.contactStrIdList.filter(
      (id) => !existingContacts.some((contact) => contact.str_id === id)
    );

    return {
      found: {
        compProfileList: existingCompProfiles.map((p) => ({
          ...p,
          strId: p.str_id,
        })),
        contactList: existingContacts.map((c) => ({ ...c, strId: c.str_id })),
      },
      notFound: {
        compProfileStrIdList: notFoundCompProfiles,
        contactStrIdList: notFoundContacts,
      },
    };
  }
}
