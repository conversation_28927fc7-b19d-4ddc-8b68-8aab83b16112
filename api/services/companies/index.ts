import type { Prisma } from '@prisma/client';
import type { PrismaClient } from '@prisma/client/extension';
import { inject, injectable } from 'inversify';

import { isNill, numberOrDefault } from 'common/helpers';
import { AccountIds } from 'common/constants';
import { ImportStatuses, SortOrder, AccessTypes } from 'common/globalTypes';
import { customViewDefault } from 'common/constants/account_role_settings';
import type { Company } from 'common/types/companies';

import prisma, { prismaClient } from '@/lib/prisma';
import { REPOSITORY_TYPES } from '@/constants';
import type ICompanyRepository from '@/persistence/companies/ICompanyRepository';
import type CompanyRecord from '@/persistence/companies/CompanyRecord';
import {
  type Company as DeprecatedCompany,
  type CompanyProduct,
  type CompanyProductOption,
  isCompanyAccess,
  type CompaniesFilter,
  type CompaniesFilterResponse,
} from './types';
import type ICompanyProductsRepo from '@/persistence/company-products/ICompanyProductsRepo';
import type CompanyProductRecord from '@/persistence/company-products/CompanyProductRecord';
import type ICompanyProductOptionsRepo from '@/persistence/company-product-options/ICompanyProductOptionsRepo';
import type CompanyProductOptionRecord from '@/persistence/company-product-options/CompanyProductOptionRecord';
import { AccountAccessLevels, DataStates } from '@/types';

export interface GetCompaniesByPkIdInput {
  account_id: string;
  company_pk_ids: number[];
}

export interface GetCompaniesByPkIdOutput {
  companies: DeprecatedCompany[];
}

export interface GetProductsByPkIdInput {
  account_id: string;
  product_pk_ids: number[];
}

export interface GetProductsByPkIdOutput {
  products: CompanyProduct[];
}

export interface GetProductOptionsByPkIdInput {
  account_id: string;
  product_option_pk_ids: number[];
}

export interface GetProductOptionsByPkIdOutput {
  product_options: CompanyProductOption[];
}

export interface FindCompaniesByAccountInput {
  account_id: string;
}

export interface FindCompaniesByAccountOutput {
  companies: DeprecatedCompany[];
}

type WhereClause = Prisma.companiesWhereInput & {
  str_id?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  type?: any;
};

interface PotentialMatch {
  id: number;
  name: string;
  str_id: string;
}

@injectable()
export class CompaniesService {
  @inject(REPOSITORY_TYPES.CompanyRepository)
  // @ts-expect-error
  private readonly companiesRepo: ICompanyRepository;
  @inject(REPOSITORY_TYPES.CompanyProductsRepository)
  // @ts-expect-error
  private readonly productsRepo: ICompanyProductsRepo;
  @inject(REPOSITORY_TYPES.CompanyProductOptionsRepository)
  // @ts-expect-error
  private readonly productOptionsRepo: ICompanyProductOptionsRepo;

  private mapCompanyRecord(record: CompanyRecord): DeprecatedCompany {
    const {
      id,
      str_id,
      account_id,
      uid,
      access,
      address,
      alias_list,
      company_name,
      email,
      group_id,
      sync_id,
      notes,
      phone,
      website,
    } = record;

    // @ts-expect-error
    if (!isCompanyAccess(access)) {
      throw new Error(`Unknown access value on company record: ${access}`);
    }

    const result: DeprecatedCompany = {
      id,
      // @ts-expect-error
      str_id,
      // @ts-expect-error
      account_id,
      // @ts-expect-error
      uid,
      // @ts-expect-error
      access,
      address,
      alias_list,
      company_name,
      email,
      group_id,
      sync_id,
      notes,
      phone,
      website,
    };
    return result;
  }

  private mapProductRecord(record: CompanyProductRecord): CompanyProduct {
    // @ts-expect-error
    const result: CompanyProduct = {
      ...record,
    };
    return result;
  }

  private mapProductOptionRecord(
    record: CompanyProductOptionRecord
  ): CompanyProductOption {
    // @ts-expect-error
    const result: CompanyProductOption = {
      ...record,
    };

    return result;
  }

  async getCompaniesByPkId(
    input: GetCompaniesByPkIdInput
  ): Promise<GetCompaniesByPkIdOutput> {
    const { account_id, company_pk_ids } = input;

    const records = await this.companiesRepo.getByPkId(company_pk_ids);
    // Verify access allowed
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    records.forEach(({ str_id, account_id: record_account_id }) => {
      if (record_account_id !== account_id) {
        // Deny access
        throw new Error(`Unauthorized access to company. (${str_id})`);
      }
    });

    const companies = records.map((record) => this.mapCompanyRecord(record));

    return { companies };
  }

  // TODO: Replace this method with lookup via str_id
  async getProductsByPkId(
    input: GetProductsByPkIdInput
  ): Promise<GetProductsByPkIdOutput> {
    const { account_id, product_pk_ids } = input;

    const records = await this.productsRepo.getByPkId(product_pk_ids);
    // Verify access allowed
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    records.forEach(({ str_id, account_id: record_account_id }) => {
      if (record_account_id !== account_id) {
        // Deny access
        throw new Error(`Unauthorized access to product. (${str_id})`);
      }
    });

    const products = records.map((record) => this.mapProductRecord(record));

    return { products };
  }

  // TODO: Replace this method with lookup via str_id
  async getProductOptionsByPkId(
    input: GetProductOptionsByPkIdInput
  ): Promise<GetProductOptionsByPkIdOutput> {
    const { account_id, product_option_pk_ids } = input;

    const records = await this.productOptionsRepo.getByPkId(
      product_option_pk_ids
    );
    // Verify access allowed
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    records.forEach(({ str_id, account_id: record_account_id }) => {
      if (record_account_id !== account_id) {
        // Deny access
        throw new Error(`Unauthorized access to product option. (${str_id})`);
      }
    });

    const product_options = records.map((record) =>
      this.mapProductOptionRecord(record)
    );

    return { product_options };
  }

  async findCompaniesByAccount(
    input: FindCompaniesByAccountInput
  ): Promise<FindCompaniesByAccountOutput> {
    const { account_id } = input;

    const str_ids = await this.companiesRepo.findByAccount(account_id);
    if (str_ids.length === 0) {
      return { companies: [] };
    }

    const records = await this.companiesRepo.getByStrId(str_ids);
    const companies = records.map((record) => this.mapCompanyRecord(record));

    return { companies };
  }

  async queryAll(
    where: Prisma.companiesWhereInput,
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    return await prisma.companies.findMany({
      where: where,
    });
  }

  async queryOne(
    where: Prisma.companiesWhereInput,
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    return await prisma.companies.findFirst({
      where: where,
    });
  }

  async fetchCompaniesByFilter(
    req: CompaniesFilter
  ): Promise<CompaniesFilterResponse | Company[]> {
    const {
      accountId,
      all,
      isDynamicSelect,
      ids,
      adminMode,
      limit = undefined,
      orderBy,
      order,
      page = '0',
      q: _q = '',
      type,
      strId,
      companyNames,
      roleId,
      access,
    } = req;

    const pageNum = numberOrDefault(page, 0);
    const limitNum: number | undefined = numberOrDefault(limit, 'undefined');
    const q = _q.trim();

    let assumedAccountId = accountId;
    let baseCondition = this.buildBaseCondition(accountId);

    if (adminMode) {
      assumedAccountId = await this.getGlobalAccountId(accountId);
      baseCondition = this.buildAdminBaseCondition(assumedAccountId);
    }

    const where = this.buildWhereClause({
      baseCondition,
      q,
      type,
      strId,
      companyNames,
      access,
      ids,
    });

    const orderByClause = this.buildOrderByClause(orderBy, order);

    let data = await this.queryCompanies({
      where,
      isDynamicSelect,
      orderByClause,
      mode: adminMode,
    });
    let count = data.length;

    if (adminMode && isDynamicSelect && data.length > 0) {
      data = await this.attachAccountNames(data);
    }

    if (!isDynamicSelect && data.length > 0) {
      data = await this.attachCompaniesProcessors(data, assumedAccountId);
      data = await this.attachDocumentProfiles(data);
    } else if (isDynamicSelect) {
      // @ts-expect-error
      data = data.map((company) => ({
        ...company,
      }));
    }

    if (!all) {
      data = await this.filterByWhitelist(data, assumedAccountId, roleId);
      count = data.length;
    }

    if (data.length > 0) {
      data = await this.addPotentialMatches(data);
    }

    if (isDynamicSelect) return data;

    if (limit) {
      // @ts-expect-error
      data = this.paginateData(data, pageNum, limitNum);
    }

    return { data, count };
  }

  /**
   * Create a mapping of company names to their IDs.
   * The mapping is case-insensitive, which means that company names will compared in lowercase.
   * @param input
   * @returns Map of company name (lowercase) to ID
   * @example:
   * ```
   *  - Input:
   *    {
   *      companyNamesList: ['Company A', 'Company B', 'Company C']
   *    }
   *  - Output:
   *    {
   *      companyNameToIdMap: { 'company a': 1001, 'company b': 205 }, // Map(2)
   *      notFound: ['Company C']
   *    }
   * ```
   */
  public async getCompanyNameToIdMap(input: {
    companyNamesList: Array<string | undefined>;
    accountId: string;
  }): Promise<{
    companyNameToIdMap: Map<string, number>;
    notFound: string[];
  }> {
    const notFound: string[] = [];
    const companyNameToIdMap = new Map<string, number>();
    const isNotNill = (value?: string): value is string => !isNill(value);
    const companyNames = new Set(input.companyNamesList.filter(isNotNill));

    if (companyNames.size > 0) {
      const companies = await prismaClient.companies.findMany({
        where: {
          account_id: input.accountId,
          OR: [...companyNames].map((name) => ({
            company_name: {
              contains: name,
              mode: 'insensitive',
            },
          })),
        },
        select: { id: true, company_name: true },
      });

      if (companies?.length !== companyNames.size) {
        const foundCompanyNames = new Set(
          companies.map((c) => c.company_name?.toLowerCase())
        );
        notFound.push(
          ...[...companyNames].filter(
            (name) => !foundCompanyNames.has(name.toLowerCase())
          )
        );
      }

      for (const company of companies) {
        if (company.company_name) {
          companyNameToIdMap.set(
            company.company_name?.toLowerCase(),
            company.id
          );
        }
      }
    }

    return { companyNameToIdMap, notFound };
  }

  private buildBaseCondition(accountId: string) {
    return {
      OR: [
        { account_id: accountId, state: DataStates.ACTIVE },
        { access: AccountAccessLevels.GLOBAL, state: DataStates.ACTIVE },
      ],
    };
  }

  private async getGlobalAccountId(accountId: string): Promise<string> {
    const fintary_global_account = await prisma.accounts.findFirst({
      where: { str_id: AccountIds.FINTARY_GLOBAL },
      select: { str_id: true },
      accountInject: false,
    });
    return fintary_global_account?.str_id ?? accountId;
  }

  private buildAdminBaseCondition(assumedAccountId: string) {
    return {
      OR: [
        { account_id: assumedAccountId, state: DataStates.ACTIVE },
        { access: AccountAccessLevels.GLOBAL, state: DataStates.ACTIVE },
      ],
    };
  }

  private buildWhereClause({
    baseCondition,
    q,
    type,
    strId,
    companyNames,
    access,
    ids,
  }: {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    baseCondition: any;
    q: string;
    type?: string;
    strId?: string;
    companyNames?: string[];
    access?: string;
    ids?: number[];
  }): WhereClause {
    let whereCondition = baseCondition;

    if (access === AccessTypes.ACCOUNT) {
      whereCondition = {
        access: AccountAccessLevels.ACCOUNT,
        state: DataStates.ACTIVE,
      };
    } else if (access === AccessTypes.GLOBAL) {
      whereCondition = {
        access: AccountAccessLevels.GLOBAL,
        state: DataStates.ACTIVE,
      };
    }

    const where: WhereClause = {
      AND: [
        whereCondition,
        q
          ? {
              OR: [
                { company_name: { contains: q, mode: 'insensitive' } },
                { email: { contains: q, mode: 'insensitive' } },
                { phone: { contains: q, mode: 'insensitive' } },
                { website: { contains: q, mode: 'insensitive' } },
                { address: { contains: q, mode: 'insensitive' } },
                { notes: { contains: q, mode: 'insensitive' } },
              ],
            }
          : {},
      ],
      type: type ? { array_contains: [type] } : undefined,
      str_id: strId,
    };

    if (Array.isArray(companyNames)) {
      where.company_name = { in: companyNames };
    }

    if (Array.isArray(ids) && ids.length > 0) {
      where.id = { in: ids };
    }

    return where;
  }

  private buildOrderByClause(orderBy?: string, order?: string) {
    if (orderBy === 'processor_str_ids') {
      return { companies_processors: { _count: order ?? SortOrder.DESC } };
    }
    if (orderBy === 'profile_str_ids') {
      return {
        companies_document_profiles: { _count: order ?? SortOrder.DESC },
      };
    }

    return orderBy
      ? { [orderBy]: order ?? SortOrder.ASC }
      : { company_name: SortOrder.ASC };
  }

  private async queryCompanies({
    where,
    isDynamicSelect,
    orderByClause,
    mode,
  }: {
    where: WhereClause;
    isDynamicSelect?: boolean;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    orderByClause: any;
    mode?: boolean;
  }) {
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    return await prisma['companies'].findMany(
      isDynamicSelect
        ? {
            where,
            select: {
              id: true,
              str_id: true,
              access: true,
              company_name: true,
              sync_worker: true,
              canonical_id: true,
              account_id: true,
            },
            orderBy: { company_name: SortOrder.ASC },
            accountInject: !mode,
          }
        : {
            where,
            orderBy: orderByClause,
            include: {
              canonical_company: {
                select: {
                  id: true,
                  company_name: true,
                  str_id: true,
                },
              },
              companies_document_profiles: {
                where: {
                  state: DataStates.ACTIVE,
                },
                include: {
                  document_profile: {
                    select: {
                      str_id: true,
                      name: true,
                      status: true,
                    },
                  },
                },
              },
            },
            accountInject: !mode,
          }
    );
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private async attachAccountNames(data: any[]) {
    const accountIds = [
      ...new Set(data.map((company) => company.account_id).filter(Boolean)),
    ];

    if (accountIds.length === 0) return data;

    const accounts = await prisma.accounts.findMany({
      where: {
        str_id: { in: accountIds },
        state: DataStates.ACTIVE,
      },
      select: {
        str_id: true,
        name: true,
      },
      accountInject: false,
    });

    const accountsMap = new Map<string, { str_id: string; name: string }>(
      // @ts-expect-error
      accounts.map((acc) => [acc.str_id, acc])
    );

    return data.map((company) => ({
      ...company,
      account_name:
        (accountsMap.get(company.account_id) as { name: string } | undefined)
          ?.name || company.account_id,
    }));
  }

  private async attachCompaniesProcessors(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data: any[],
    assumedAccountId: string
  ) {
    const companiesProcessors = await prisma.companies_processors.findMany({
      where: {
        company_str_id: {
          in: data.map((company) => company.str_id),
        },
        account_id: assumedAccountId,
        state: DataStates.ACTIVE,
        processor: {
          state: DataStates.ACTIVE,
        },
      },
      select: {
        company_str_id: true,
        processor_str_id: true,
        import_status: true,
        processor: {
          select: {
            name: true,
          },
        },
      },
      accountInject: false,
    });

    return data.map((company) => {
      const companyProcessors = companiesProcessors
        // @ts-expect-error
        .filter((cp) => cp.company_str_id === company.str_id)
        // @ts-expect-error
        .map((cp) => ({
          processor_str_id: cp.processor_str_id,
          import_status: cp.import_status || ImportStatuses.NONE,
          name: cp.processor?.name || cp.processor_str_id,
        }));

      return {
        ...company,
        // @ts-expect-error
        processor_str_ids: companyProcessors.map((cp) => cp.processor_str_id),
        companies_processors: companyProcessors,
      };
    });
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private async attachDocumentProfiles(data: any[]) {
    return data.map((company) => {
      const companiesDocumentProfiles = (
        company.companies_document_profiles || []
      )
        // @ts-expect-error
        .map((cdp) => ({
          document_profile_str_id: cdp.document_profile_str_id,
          auto_mapping_id: cdp.auto_mapping_id,
          document_profile: cdp.document_profile,
        }));

      const documentProfiles = companiesDocumentProfiles
        // @ts-expect-error
        .map((cdp) => cdp.document_profile)
        .filter(Boolean);

      return {
        ...company,
        companies_document_profiles: companiesDocumentProfiles,
        document_profiles: documentProfiles,
        // @ts-expect-error
        profile_str_ids: documentProfiles.map((dp) => dp.str_id),
      };
    });
  }

  private async filterByWhitelist(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data: any[],
    assumedAccountId: string,
    roleId?: string
  ) {
    const accountSettings = await prisma.account_role_settings.findFirst({
      where: {
        account_id: assumedAccountId,
        // @ts-expect-error
        role_id: parseInt(roleId),
        custom_view_name: customViewDefault,
      },
    });
    const nameList = (accountSettings?.companies_view as string[]) ?? [];
    return data.filter((company) => {
      if (company.access === AccountAccessLevels.GLOBAL) {
        return nameList.includes(company.company_name);
      } else {
        return true;
      }
    });
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private paginateData(data: any[], pageNum: number, limitNum: number) {
    const startIndex = pageNum * limitNum;
    const endIndex = startIndex + limitNum;
    return data.length > limitNum ? data.slice(startIndex, endIndex) : data;
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  async addPotentialMatches(data: any[]): Promise<any[]> {
    if (!data?.length) return [];

    const allCompanies = await prisma.companies.findMany({
      where: { state: DataStates.ACTIVE },
      select: {
        id: true,
        str_id: true,
        access: true,
        canonical_id: true,
        company_name: true,
        account_id: true,
      },
      accountInject: false,
    });

    const globalCompanies = allCompanies.filter(
      // @ts-expect-error
      (c) => c.access === AccessTypes.GLOBAL
    );
    const unboundAccountCompanies = allCompanies.filter(
      // @ts-expect-error
      (c) => c.access !== AccessTypes.GLOBAL && !c.canonical_id
    );

    return data.map((company) => {
      let potentialMatch = null;

      if (company.access === AccessTypes.GLOBAL) {
        const matches = this.findAllMatches(company, unboundAccountCompanies);
        if (matches.length > 0) {
          potentialMatch = matches.length === 1 ? matches[0] : matches;
        }
      } else if (!company.canonical_id) {
        potentialMatch = this.findBestMatch(company, globalCompanies);
      }

      return potentialMatch
        ? { ...company, potential_match: potentialMatch }
        : company;
    });
  }

  private calculateMatchScore(target: string, candidate: string): number {
    const targetLower = target.toLowerCase();
    const candidateLower = candidate.toLowerCase();

    if (targetLower === candidateLower) return 1.0;
    if (
      targetLower.includes(candidateLower) ||
      candidateLower.includes(targetLower)
    )
      return 0.8;
    return 0;
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private createPotentialMatch(company: any): PotentialMatch {
    return {
      id: company.id,
      name: company.company_name,
      str_id: company.str_id,
    };
  }

  private findAllMatches(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    targetCompany: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    candidates: any[]
  ): PotentialMatch[] {
    if (!candidates.length || !targetCompany.company_name) return [];

    return candidates
      .filter((candidate) => {
        if (!candidate.company_name) return false;
        return (
          this.calculateMatchScore(
            targetCompany.company_name,
            candidate.company_name
          ) >= 0.8
        );
      })
      .map((candidate) => this.createPotentialMatch(candidate));
  }

  private findBestMatch(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    targetCompany: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    candidates: any[]
  ): PotentialMatch | null {
    if (!candidates.length || !targetCompany.company_name) return null;

    let bestMatch = null;
    let highestScore = 0;

    for (const candidate of candidates) {
      if (!candidate.company_name) continue;

      const score = this.calculateMatchScore(
        targetCompany.company_name,
        candidate.company_name
      );
      if (score > highestScore && score >= 0.8) {
        highestScore = score;
        bestMatch = candidate;
      }
    }

    return bestMatch ? this.createPotentialMatch(bestMatch) : null;
  }
}
