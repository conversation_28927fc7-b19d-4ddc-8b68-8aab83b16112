// TODO(toby): Extend this union type with all supported access options
import type { NextApiRequest } from 'next';
import { z } from 'zod';

import type { ExtNextApiRequest } from '@/types';

export type CompanyAccess = 'account';
// @ts-expect-error
export function isCompanyAccess(value: string): value is CompanyAccess | null {
  return value === null || ['account', 'global'].includes(value);
}

// TODO(toby): Move service-level entity types to @/types
export interface Company {
  /** @deprecated Use str_id instead */
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  access: CompanyAccess;
  address: string | null;
  alias_list: string[];
  company_name: string | null;
  email: string | null;
  group_id: string | null;
  sync_id: string | null;
  notes: string | null;
  phone: string | null;
  website: string | null;
}

export interface CompanyProduct {
  /** @deprecated Use str_id instead */
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  company_id: number;
  sync_id: string | null;
  product_name: string | null;
  product_type: string | null;
  product_sub_type: string | null;
  notes: string | null;
}

export interface CompanyProductOption {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  product_id: number;
  state: string;
  name: string | null;
  notes: string | null;
}

export const CompaniesFilterSchema = z.object({
  accountId: z.string(),
  adminMode: z.coerce.boolean().optional(),
  access: z.string().optional(),
  q: z.string().optional(),
  type: z.string().optional(),
  strId: z.string().optional(),
  companyNames: z.preprocess((val) => {
    if (Array.isArray(val)) return val;
    if (typeof val === 'string') {
      try {
        return JSON.parse(val);
      } catch {
        return [val];
      }
    }
    return undefined;
  }, z.array(z.string()).optional()),
  all: z.coerce.boolean().optional(),
  isDynamicSelect: z.coerce.boolean().optional(),
  ids: z.preprocess((val) => {
    if (Array.isArray(val)) return val;
    if (typeof val === 'string') {
      try {
        return JSON.parse(val);
      } catch {
        return [val];
      }
    }
    return undefined;
  }, z.array(z.number()).optional()),
  limit: z.coerce.number().optional(),
  orderBy: z.string().optional(),
  sort: z.string().optional(),
  order: z.string().optional(),
  page: z.coerce.number().optional(),
  roleId: z.coerce.string().optional(),
});

export type CompaniesFilter = z.infer<typeof CompaniesFilterSchema>;

export interface CompaniesFilterResponse {
  data: Company[];
  count: number;
}

const keyMap: Record<string, string> = {
  account_id: 'accountId',
  admin_mode: 'adminMode',
  str_id: 'strId',
  is_dynamic_select: 'isDynamicSelect',
  order_by: 'orderBy',
  order: 'order',
  role_id: 'roleId',
  access: 'access',
};

export function parseCompaniesFilter(
  req: NextApiRequest & ExtNextApiRequest
): CompaniesFilter {
  const input = { ...req.query };

  // Add required fields from the request object
  if (req.account_id) input.account_id = req.account_id;
  if (req.role_id) input.role_id = req.role_id;

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const normalized: Record<string, any> = {};
  for (const key in input) {
    normalized[keyMap[key] || key] = input[key];
  }

  return CompaniesFilterSchema.parse(normalized);
}
