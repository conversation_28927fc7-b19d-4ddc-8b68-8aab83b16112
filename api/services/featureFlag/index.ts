import { injectable } from 'inversify';

import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';
import { AccountConfigsTypes } from 'common/constants/accounts_configs';

@injectable()
export class FeatureFlagsService {
  async isFeatureEnabled(params: {
    feature: string;
    accountId: string;
  }): Promise<boolean> {
    const { feature, accountId } = params;
    const config = await prismaClient.account_configs.findFirst({
      where: {
        type: AccountConfigsTypes.FEATURE_FLAGS,
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
    });
    if (!config) {
      return false;
    }
    if (
      config.value &&
      typeof config.value === 'object' &&
      config.value !== null
    ) {
      // Just in case it is not a boolean value
      return Boolean((config.value as Record<string, boolean>)[feature]);
    }
    return false;
  }
}
