import { inject, injectable } from 'inversify';
import { captureException } from '@sentry/nextjs';
import * as cheerio from 'cheerio';

import { HtmlExtractValidator, type ProcessHtmlExtractType } from './validator';
import { prismaClient } from '@/lib/prisma';
import { DocumentFileService } from '@/services/documents/fileService';

type AnyNode = import('domhandler/lib/esm/node').AnyNode;
type RowData = Record<string, string>;
type ExtractedData = { tableIndex: number; data: RowData[] };

@injectable()
export class HtmlExtractService {
  constructor(
    @inject(DocumentFileService)
    private documentFileService: DocumentFileService,
    @inject(HtmlExtractValidator)
    private validator: HtmlExtractValidator
  ) {}

  async processDocument(params: ProcessHtmlExtractType) {
    try {
      const validatedData = this.validator.validateProcessParams(params);

      const document = await prismaClient.documents.findFirst({
        where: {
          id: validatedData.documentId,
          account_id: validatedData.accountId,
        },
      });

      if (!document) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error('Document not found');
        throw new Error('Document not found');
      }

      const fileBuffer =
        await this.documentFileService.getFileFromStorage(document);
      const htmlContent = fileBuffer.toString('utf-8');

      const extractedData = await this.getHtmlContent(htmlContent);

      return extractedData;
    } catch (error) {
      captureException(error);
      // @ts-expect-error
      throw new Error(`Error in processing document: ${error.message}`);
    }
  }

  private async getHtmlContent(htmlContent: string): Promise<ExtractedData[]> {
    try {
      const $ = cheerio.load(htmlContent);

      const data: ExtractedData[] = [];

      $('table').each((tableIndex, table) => {
        const headers = this.extractHeaders(table, $);
        const tableData = this.extractTableData(table, headers, $);

        data.push({ tableIndex, data: tableData });
      });

      return data;
    } catch (error) {
      captureException(error);
      throw new Error('Error processing HTML content');
    }
  }

  private extractHeaders(
    table: cheerio.BasicAcceptedElems<AnyNode>,
    $: cheerio.CheerioAPI
  ): string[] {
    const headers: string[] = [];
    $(table)
      .find('tr:first-child th')
      .each((_, th) => {
        headers.push($(th).text().trim());
      });
    return headers;
  }

  private extractTableData(
    table: cheerio.BasicAcceptedElems<AnyNode>,
    headers: string[],
    $: cheerio.CheerioAPI
  ): RowData[] {
    const tableData: RowData[] = [];
    $(table)
      .find('tr')
      .each((_rowIndex, row) => {
        const rowData: RowData = {};
        $(row)
          .find('td')
          .each((colIndex, cell) => {
            const key = headers[colIndex] || `${colIndex + 1}`;
            rowData[key] = $(cell).text().trim();
          });

        if (Object.keys(rowData).length > 0) {
          tableData.push(rowData);
        }
      });
    return tableData;
  }
}
