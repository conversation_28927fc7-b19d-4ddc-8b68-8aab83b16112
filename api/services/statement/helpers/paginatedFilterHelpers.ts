import { DEFAULT_FILTER } from 'common/constants';
import CommonFormatter from 'common/Formatter';
import { AgentCommissionsStatusesLabels } from 'common/globalTypes';

import { prismaClient } from '@/lib/prisma';
import type { ExtNextApiRequest } from '@/types';

export interface FilterOption {
  id: string | number;
  name: string;
}

export const processContactData = async (
  contactData: string[],
  req: ExtNextApiRequest
): Promise<FilterOption[]> => {
  const uniqueContactData = Array.from(new Set(contactData.flat()));
  let contacts = await prismaClient.contacts.findMany({
    where: { str_id: { in: uniqueContactData } },
    select: {
      str_id: true,
      first_name: true,
      last_name: true,
      email: true,
    },
  });

  if (
    Array.isArray(req.query?.unselected_contacts) &&
    req.query.unselected_contacts.length > 0
  ) {
    const unselectedContacts = req.query.unselected_contacts;
    contacts = contacts.filter((contact) => {
      return contact.str_id && !unselectedContacts.includes(contact.str_id);
    });
  }

  const contactsMap = new Map(
    contacts
      .filter(
        (contact): contact is typeof contact & { str_id: string } =>
          contact.str_id !== null
      )
      .map((contact) => [
        contact.str_id,
        CommonFormatter.contact(
          contact as Parameters<typeof CommonFormatter.contact>[0],
          { account_id: req.account_id }
        ),
      ])
  );

  const processedContacts = [
    { id: '-1', name: DEFAULT_FILTER.BLANK_OPTION },
    ...uniqueContactData.map((id) => ({
      id,
      name: contactsMap.get(id) || id,
    })),
  ];

  return processedContacts.sort((a, b) => a.name.localeCompare(b.name));
};

export const processFlagData = (flagData: unknown[]): FilterOption[] => {
  const uniqueFlags = new Set(
    flagData.flatMap((item) => {
      if (item === DEFAULT_FILTER.BLANK_OPTION) {
        return DEFAULT_FILTER.BLANK_OPTION;
      }
      if (typeof item === 'object' && item !== null) {
        return Object.entries(item).map(([key, value]) => `${key}: ${value}`);
      }
      return item;
    })
  );

  const processedFlags = Array.from(uniqueFlags).map((flag) => ({
    id:
      flag === DEFAULT_FILTER.BLANK_OPTION
        ? DEFAULT_FILTER.BLANK_VALUE
        : String(flag),
    name:
      flag === DEFAULT_FILTER.BLANK_OPTION
        ? DEFAULT_FILTER.BLANK_OPTION
        : String(flag),
  }));

  return [
    { id: DEFAULT_FILTER.BLANK_OPTION, name: DEFAULT_FILTER.BLANK_OPTION },
    ...processedFlags,
  ];
};

export const processDocumentData = async (
  documentIds: string[]
): Promise<FilterOption[]> => {
  const documents = await prismaClient.documents.findMany({
    where: { str_id: { in: documentIds } },
    select: {
      str_id: true,
      file_path: true,
      filename: true,
    },
  });

  const documentsMap = new Map(
    documents.map((document) => [
      document.str_id,
      document?.file_path &&
      document?.filename &&
      document.file_path.endsWith(document.filename)
        ? document.filename
        : document?.file_path?.split('/')?.pop()?.substring(22) ||
          document.str_id,
    ])
  );

  const processedDocuments = [
    { id: DEFAULT_FILTER.BLANK_OPTION, name: DEFAULT_FILTER.BLANK_OPTION },
    ...documentIds.map((id) => ({
      id,
      name: documentsMap.get(id) || id,
    })),
  ];

  return processedDocuments.sort((a, b) => a.name.localeCompare(b.name));
};

export const processGenericFieldValues = (
  fieldValues: unknown
): FilterOption[] => {
  if (!Array.isArray(fieldValues)) {
    return [];
  }
  const processedValues = fieldValues.map((value) => ({
    id:
      value === null || value === undefined
        ? DEFAULT_FILTER.BLANK_VALUE
        : String(value),
    name:
      value === null || value === undefined
        ? DEFAULT_FILTER.BLANK_OPTION
        : String(value),
  }));

  return [
    { id: DEFAULT_FILTER.BLANK_OPTION, name: DEFAULT_FILTER.BLANK_OPTION },
    ...processedValues,
  ];
};

export const processAgentCommissionsStatus2Data = (
  rawData: string[]
): FilterOption[] => {
  const processedStatuses = rawData.map((value) => {
    const statusLabel =
      AgentCommissionsStatusesLabels[
        value as keyof typeof AgentCommissionsStatusesLabels
      ];
    return {
      id: value,
      name: statusLabel || value,
    };
  });

  return [
    { id: DEFAULT_FILTER.BLANK_OPTION, name: DEFAULT_FILTER.BLANK_OPTION },
    ...processedStatuses,
  ];
};

export const processCompCalcStatusData = (
  rawData: string[]
): FilterOption[] => {
  const processedStatuses = rawData.map((value) => {
    if (value === '(Blank)') {
      return {
        id: DEFAULT_FILTER.BLANK_VALUE,
        name: DEFAULT_FILTER.BLANK_OPTION,
      };
    }
    const statusLabel =
      AgentCommissionsStatusesLabels[
        value as keyof typeof AgentCommissionsStatusesLabels
      ];
    return {
      id: value,
      name: statusLabel || value,
    };
  });

  return [
    { id: DEFAULT_FILTER.BLANK_OPTION, name: DEFAULT_FILTER.BLANK_OPTION },
    ...processedStatuses,
  ];
};

export const processReportDataIdData = (rawData: string[]): FilterOption[] => {
  return [
    { id: DEFAULT_FILTER.BLANK_OPTION, name: DEFAULT_FILTER.BLANK_OPTION },
    ...rawData.map((value) => ({
      id: value,
      name: value,
    })),
  ];
};
