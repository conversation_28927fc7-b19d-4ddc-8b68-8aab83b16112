import { describe, it, expect, vi } from 'vitest';
import { WidgetService } from './index';
import type { WidgetDefinition } from 'common/dto/widgets';
import type { AccountingTransactionsService } from '../accounting-transactions';
import type { UserService } from '../user';
import type { ContactService } from '../contact';
import type { AccountService } from '../account';
import type { ReportService } from '../report';
import type { StatementService } from '../statement';

vi.mock('@/ioc', () => ({
  container: {
    get: vi.fn(() => ({})),
  },
}));

describe('WidgetService', () => {
  it('Given a simple table definition and source, should return a valid table widget', async () => {
    const fakeLruFactory = vi.fn(() => ({}));

    const service = new WidgetService(
      {} as UserService,
      {} as ContactService,
      {} as AccountService,
      {} as ReportService,
      {} as StatementService,
      {} as AccountingTransactionsService,
      // biome-ignore lint/suspicious/noExplicitAny: This type is hard to grok
      fakeLruFactory as any
    );

    const definition: WidgetDefinition = {
      name: 'Test Table',
      access: 'global',
      dataSource: 'commissions',
      filterByDate: 'none',
      type: 'table',
      groupBys: [
        {
          id: 1,
          field: 'agent_name',
          timePeriod: null,
        },
      ],
      sortBys: [],
      filters: [],
      column: '',
      columnLimit: null,
      aggregationSelectors: [
        {
          field: 'commission_amount',
          formatter: 'currency',
          aggregation_method: 'Sum',
        },
      ],
      dataFieldsExpression: null,
      customRender: {
        code: "(dataSource) => { return [['test'], ['test']]; }",
        enabled: false,
        language: 'javascript',
      },
    };

    const source = [
      { agent_name: 'Alice', commission_amount: 100 },
      { agent_name: 'Bob', commission_amount: 200 },
      { agent_name: 'Alice', commission_amount: 50 },
    ];

    const widget = await service.createTableWidget(definition, source);

    expect(widget).toBeDefined();
    expect(widget?.widgetGroup).toBe('TABLE');
    expect(widget?.displayName).toBe('Test Table');
    expect(widget?.enabled).toBe(true);
    expect(Array.isArray(widget?.data)).toBe(true);
    expect(widget?.data.length).toBeGreaterThan(1);
    expect(widget?.data[0]).toContain('Agent name');
    expect(widget?.data[0]).toContain('Commission amount');
    // Should aggregate commission_amount by agent_name
    const aliceRow = widget?.data.find((row: string[]) => row[0] === 'Alice');
    const bobRow = widget?.data.find((row: string[]) => row[0] === 'Bob');
    expect(aliceRow[1]).toMatch(/\$/);
    expect(bobRow[1]).toMatch(/\$/);
  });
});
