import { faker } from '@faker-js/faker';
import type { accounting_transactions, contacts, Prisma } from '@prisma/client';
import * as Sentry from '@sentry/nextjs';
import BigNumber from 'bignumber.js';
import {
  DEFAULT_FILTER,
  e2eTestWidgetName,
  FieldTypes,
  WidgetGroup,
} from 'common/constants';
import { numberOrDefault } from 'common/helpers';
import { inject, injectable, type interfaces } from 'inversify';
import type { DefaultArgs } from '@prisma/client/runtime/library';

import { SERVICE_TYPES } from '@/constants';
import Formatter from '@/lib/Formatter';
import { getDateBucket, runInBatch } from '@/lib/helpers';
import { get } from '@/lib/helpers/get';
import { matchesFilter } from '@/lib/matcher';
import prisma, { prismaClient } from '@/lib/prisma';
import {
  createAgentListQuery,
  generateAccountingTransactionsWhere,
  generateContactWhere,
  generateCustomersWhere,
  generateReportWhere,
  generateReportWhereCommissionProcessingDate,
  generateReportWhereWithoutDataFilter,
  generateStatementWhere,
  generateWhereBase,
} from '@/lib/prisma/utils';
import { AccountService } from '@/services/account';
import { AccountingTransactionsService } from '@/services/accounting-transactions';
import type { LRUCacheService } from '@/services/cache/lru';
import { ContactService } from '@/services/contact';
import { ReportService } from '@/services/report';
import { StatementService } from '@/services/statement';
import { UserService } from '@/services/user';
import type {
  SaveWidgetRequest,
  ServiceCall,
  UpdateWidgetRequest,
  Widget,
} from './types';
import dayjs from '@/lib/dayjs';
import { calculateCommissionFields } from './utils';
import { generateDateClauses } from '@/lib/helpers/generateDateClauses';
import {
  type WidgetDefinition,
  type AggregationSelector,
  DEFAULT_FIELD_VALUE,
  getFieldType,
  type GroupBy,
  WidgetTypes,
  getFieldLabel,
  type WidgetTimePeriods,
} from 'common/dto/widgets';
import { AppLoggerService } from '@/services/logger/appLogger';
import { StatusType } from '@/services/api_key/validator';
import { loadPyodide } from 'pyodide';
import {
  calculateValueFromExpression,
  convertContactsKeyToName,
  executeCustomCode,
  getContactNameIdMap,
  sortingPick,
} from './shared';

type ChartDefinition = {
  tooltip?: {
    trigger: string;
    position?: [number, number];
    formatter?: string;
    axisPointer?: {
      type: string;
    };
  };
  legend?: {
    data?: unknown[];
  };
  xAxis?: {
    data?: string[];
    axisLabel?: {
      show?: boolean;
      interval?: number | 'auto';
      rotate?: number;
      margin?: number;
      formatter?: (value: string) => string;
    };
  };
  yAxis?: {
    type: string;
  };
  series?: {
    name?: string;
    type?: string;
    stack?: string;
    data?: unknown[];
    itemStyle?: {
      color?: string;
    };
    smooth?: boolean;
    connectNulls?: boolean;
  }[];
};

@injectable()
export class WidgetService {
  // @ts-expect-error
  private reportData: Record<string, unknown>[];
  // @ts-expect-error
  private reportDataWithoutDateFilter: Record<string, unknown>[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private reportDataCommissionProcessingDate: any;
  // @ts-expect-error
  private accountingTransactions: (accounting_transactions & {
    contact: contacts | null;
  })[];
  // biome-ignore lint/suspicious/noExplicitAny: Revisit in a future refactor effort.
  private customersData: any;
  // @ts-expect-error
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private statementDataByNoDate: Record<string, any>[];
  // @ts-expect-error
  private statementWhere: Prisma.statement_dataWhereInput;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private mockStatementData: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private mockReportData: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private mockReportDataCommissionProcessingDate: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private compensationTypes: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private statementTags: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private policyPolicyStatuses: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private policyProductTypes: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private commissionProductTypes: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private contactGroups: any;
  // @ts-expect-error
  private params: {
    // @ts-expect-error
    role_id;
    // @ts-expect-error
    uid;
    // @ts-expect-error
    account_id;
    // @ts-expect-error
    agent_group;
    // @ts-expect-error
    unselected_agent;
    // @ts-expect-error
    agent;
    // @ts-expect-error
    compensation_type;
    // @ts-expect-error
    tag;
    // @ts-expect-error
    policy_status;
    // @ts-expect-error
    product_type;
    // @ts-expect-error
    startDate;
    // @ts-expect-error
    endDate;
    // @ts-expect-error
    contact_str_id;
    // @ts-expect-error
    include_blanks;
    // @ts-expect-error
    timezone;
  };
  private serviceCalls: ServiceCall[] = [];
  // @ts-expect-error
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private contacts: any[];

  private logger: AppLoggerService = new AppLoggerService({
    defaultMeta: {
      service: 'WidgetService',
    },
  });

  private colorMapping = {
    Medical: '#92cc75',
    Ancillary: '#fac858',
    '401k': '#ed6666',
    Other: '#e97ccc',
    Individual: '#5570c6',
    Medicare: '#73c0de',
    null: '#3ba272',
  };

  private LEGEND_SIZE_LIMIT = 10;

  private cacheService: LRUCacheService;
  private statementSelectFields = {
    commission_amount: true,
    agent_commissions: true,
    policy_id: true,
    contacts: true,
    carrier_name: true,
    customer_name: true,
    agent_name: true,
    writing_carrier_name: true,
    processing_date: true,
    geo_state: true,
    effective_date: true,
    report: {
      select: {
        product_sub_type: true,
        product_type: true,
        customer_name: true,
        geo_state: true,
        premium_amount: true,
        writing_carrier_name: true,
      },
    },
    premium_amount: true,
    commissionable_premium_amount: true,
    compensation_type: true,
    tags: true,
    account_type: true,
    bill_mode: true,
    carrier_rate: true,
    commission_basis: true,
    commission_rate: true,
    group_name: true,
    notes: true,
    payment_mode: true,
    payment_status: true,
    premium_type: true,
    product_option_name: true,
    product_sub_type: true,
    reconciliation_method: true,
    reconciliation_status: true,
    statement_number: true,
    status: true,
    type: true,
    reconciliation_stats: true,
    standardized_customer_name: true,
    transaction_type: true,
    invoice_date: true,
    payment_date: true,
    product_name: true,
    product_type: true,
    period_date: true,
    issue_age: true,
    member_count: true,
    commission_paid_amount: true,
    commission_rate_percent: true,
    customer_paid_premium_amount: true,
    fees: true,
    new_carrier_rate: true,
    new_commission_rate: true,
    split_percentage: true,
  };

  constructor(
    @inject(UserService)
    private userService: UserService,
    @inject(ContactService)
    private contactService: ContactService,
    @inject(AccountService)
    private accountService: AccountService,
    @inject(ReportService)
    private reportService: ReportService,
    @inject(StatementService)
    private statementService: StatementService,
    @inject(AccountingTransactionsService)
    private accountingTransactionsService: AccountingTransactionsService,
    @inject(SERVICE_TYPES.LRUCacheServiceFactory)
    private lruServiceFactory: interfaces.SimpleFactory<
      LRUCacheService,
      ConstructorParameters<typeof LRUCacheService>
    >
  ) {
    this.cacheService = this.lruServiceFactory({
      max: 1000,
    });
  }

  // @ts-expect-error
  init(params) {
    this.params = {
      role_id: params.role_id,
      uid: params.uid,
      account_id: params.account_id,
      agent_group: params.agent_group,
      unselected_agent: params.unselected_agent,
      agent: params.agent,
      compensation_type: params.compensation_type,
      tag: params.tag,
      policy_status: params.policy_status,
      product_type: params.product_type,
      startDate: params.startDate,
      endDate: params.endDate,
      contact_str_id: params.contact_str_id,
      include_blanks: params.include_blanks,
      timezone: params.timezone ?? 'UTC',
    };
  }
  // @ts-expect-error
  getPieOptions(name, data, countOnly = false) {
    return {
      tooltip: {
        trigger: 'item',
        position: [0, 0],
        formatter: countOnly ? `{b0}: {c0} ({d0}%)` : `{b0}: \${c0} ({d0}%)`,
      },
      series: [
        {
          center: ['50%', '55%'],
          name,
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          emphasis: {
            label: {
              show: true,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: true,
          },
          data,
        },
      ],
    };
  }

  getTimeSeriesOptions(
    // @ts-expect-error
    data,
    opts: { pct?: boolean; type?: 'bar' | 'line'; smooth?: boolean } = {},
    nameMapToDisplay: Map<string, string>
  ) {
    const { pct, type, smooth } = {
      pct: false,
      type: 'bar',
      smooth: true,
      ...opts,
    };
    const sorted = Object.entries(data).sort(
      (a, b) => dayjs(a[0]).valueOf() - dayjs(b[0]).valueOf()
    );
    const xAxis = sorted.map(([k, _v]) => nameMapToDisplay.get(k) ?? k);
    const yAxis = sorted.map(([_k, v]) => {
      if (typeof v !== 'number') {
        return v;
      }
      return (+v)?.toFixed(2);
    });
    const yAxisTotal = sorted.reduce((acc, [_k, v]) => acc + +(v ?? 0), 0);
    // @ts-expect-error
    const yAxisPct = sorted.map(([_k, v]) => (+v / yAxisTotal) * 100);
    return {
      grid: { containLabel: true },
      tooltip: {
        trigger: 'item',
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 25,
          margin: 10,
          // @ts-expect-error
          formatter: (value) => {
            if (!value) return '';
            return value.length > 16 ? `${value.substring(0, 15)}...` : value;
          },
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: pct ? yAxisPct : yAxis,
          type: type,
          smooth: smooth,
        },
      ],
    };
  }

  // @ts-expect-error
  getSeriesOptions(data) {
    const stackSet = new Set();
    const categories = Object.keys(data);

    for (const category of categories) {
      const itemData = data[category];
      for (const key of Object.keys(itemData)) {
        if (key !== 'sum') {
          stackSet.add(key);
        }
      }
    }

    const stackedTypes = Array.from(stackSet);

    const sortedCategories = dayjs(categories[0]).isValid()
      ? categories.sort((a, b) => {
          return dayjs(a).valueOf() - dayjs(b).valueOf();
        })
      : categories;

    // @ts-expect-error
    const series = stackedTypes.map((type: string) => ({
      name: type,
      type: 'bar',
      stack: 'stack',
      data: sortedCategories.map((c) => {
        if (typeof data[c][type] === 'number') {
          return data[c][type]?.toFixed(2) || 0;
        }
        return data[c][type];
      }),
      itemStyle: {
        // @ts-expect-error
        color: this.colorMapping[type],
      },
    }));

    const limitedLegend =
      stackedTypes.length > this.LEGEND_SIZE_LIMIT ? [] : stackedTypes;

    return {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: limitedLegend,
      },
      xAxis: {
        data: categories,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 25,
          margin: 10,
          // @ts-expect-error
          formatter: (value) => {
            if (!value) return '';
            return value.length > 16 ? `${value.substring(0, 15)}...` : value;
          },
        },
      },
      yAxis: {
        type: 'value',
      },
      series: series,
    };
  }

  async storeWidgetToDB(model: SaveWidgetRequest) {
    const slug = encodeURIComponent(
      model.name.replace(/ /g, '-')?.toLowerCase()
    );
    try {
      const widget = await prisma.widgets.create({
        data: {
          name: model.name,
          account_id: model.access === 'global' ? null : model.account_id,
          slug: slug,
          spec: model.spec,
          access: model.access,
        },
      });
      return widget;
    } catch (error) {
      this.logger.error(
        `Error storing widget to DB with data: ${JSON.stringify(model)}`,
        { message: error }
      );
      throw error;
    }
  }

  async getInsightsPreviewData(accountId: string) {
    // Get all widgets for the account
    const widgets = await this.getWidgetsByAccountId(accountId, []);
    this.mockStatementData = this.createRandomStatementData(500);
    this.mockReportData = this.createRandomReportData(500);
    this.mockReportDataCommissionProcessingDate =
      this.createReportDataCommissionProcessingDate(500);
    const result = [];
    // @ts-expect-error
    for (const widget of widgets) {
      try {
        const customWidget = await this.createMockCustomWidget(
          widget.spec as WidgetDefinition
        );
        if (customWidget) {
          result.push({ ...customWidget, id: widget.id });
        }
      } catch (error) {
        this.logger.error(`Error creating widget ${widget.name}`, {
          message: error,
        });
      }
    }

    return {
      widgets: result.filter((widget) => widget !== null),
    };
  }

  async getAccountWidgetList(account_id: string) {
    try {
      const widgets = await prismaClient.widgets.findMany({
        where: {
          OR: [
            // Either shared or matching account_id
            { access: 'global' },
            { account_id: account_id },
          ],
          state: StatusType.Enum.active, // Must be active in both cases
        },
        accountInject: false,
        select: {
          id: true,
          name: true,
          access: true,
          slug: true,
        },
      });
      if (widgets == null || widgets.length === 0) {
        return null;
      }
      return widgets;
    } catch (error) {
      this.logger.error('Error getting shared widgets', { message: error });
      throw error;
    }
  }

  async updateWidgetInDB(model: UpdateWidgetRequest) {
    try {
      const widget = await prisma.widgets.update({
        where: {
          id: model.id,
        },
        data: {
          name: model.name,
          spec: model.spec,
          access: model.access,
        },
      });
      return widget;
    } catch (error) {
      this.logger.error(
        `Error storing widget to DB with data: ${JSON.stringify(model)}`,
        { message: error }
      );
      throw error;
    }
  }

  async deleteWidgetInDB(id: string) {
    try {
      const widget = await prisma.widgets.findUnique({
        where: {
          id,
        },
      });
      if (!widget) {
        throw new Error(`Widget with id ${id} not found`);
      }
      if (widget.name === e2eTestWidgetName) {
        await prisma.widgets.delete({
          where: {
            id,
          },
        });
        return;
      }
      await prisma.widgets.update({
        where: {
          id,
        },
        data: {
          state: 'deleted',
        },
      });
    } catch (error) {
      this.logger.error(`Error deleting widget in DB with id: ${id}`, {
        message: error,
      });
      throw error;
    }
  }

  async getWidgetsByAccountId(accountId: string, sharedWidgetsIds: number[]) {
    try {
      const filterCondition =
        // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        sharedWidgetsIds == null || sharedWidgetsIds.length == 0
          ? {
              access: 'global',
            }
          : {
              id: {
                in: sharedWidgetsIds,
              },
            };
      const widgets = await prismaClient.widgets.findMany({
        where: {
          OR: [
            {
              account_id: accountId,
              state: 'active',
            },
            filterCondition,
          ],
        },
        accountInject: false,
      });
      if (widgets == null || widgets.length === 0) {
        return null;
      }
      return widgets;
    } catch (error) {
      this.logger.error(`Error getting widgets by account id: ${accountId}`, {
        message: error,
      });
      throw error;
    }
  }

  async getCustomers(where: Prisma.customersWhereInput) {
    const commonWhere = {
      account_id: this.params.account_id,
      state: 'active',
    };
    return prismaClient.customers.findMany({
      where,
      include: {
        contacts: {
          where: {
            ...commonWhere,
          },
        },
        accounting_transaction_details: {
          where: {
            ...commonWhere,
          },
        },
        report_data: {
          where: {
            ...commonWhere,
          },
        },
      },
    });
  }

  async loadAllDataFromServices() {
    // Check if data is in cache
    const cacheKey = JSON.stringify(this.params);
    const cachedData = this.cacheService.get(`widgetsData-${cacheKey}`);
    if (cachedData) {
      return cachedData;
    }
    const agentGroups =
      Array.isArray(this.params.agent_group) &&
      this.params.agent_group.length > 0
        ? this.params.agent_group
        : this.params.agent_group && !Array.isArray(this.params.agent_group)
          ? [this.params.agent_group]
          : undefined;
    const userData = await this.userService.getUserByUid(this.params.uid);
    const isAdminOrDataSpecialist = this.userService.isAdminOrDataSpecialist(
      // @ts-expect-error
      userData.account_user_roles
    );

    const whereBase = generateWhereBase(this.params.account_id, userData);
    const contacts = this.params.contact_str_id
      ? [this.params.contact_str_id]
      : agentGroups
        ? await this.contactService.getContactIdsByGroupName(
            whereBase,
            agentGroups
          )
        : undefined;

    let agentList = createAgentListQuery(contacts, this.params.agent);
    // For producer, only show their own data
    if (this.params.contact_str_id) {
      agentList = [{ str_id: this.params.contact_str_id }];
    }
    const unSelectedAgentList = this.params.unselected_agent
      ?.split(',')
      // @ts-expect-error
      .map((id) => {
        return { str_id: id };
      });
    const reportWhere = generateReportWhere({
      account_id: this.params.account_id,
      userData: userData,
      startDate: this.params.startDate,
      endDate: this.params.endDate,
      policy_status: this.params.policy_status,
      product_type: this.params.product_type,
      agentList: agentList,
      compensation_type: this.params.compensation_type,
      includeBlankDate: this.params.include_blanks,
      unSelectedAgentList,
    });
    const reportWhereWithoutDateFilter = generateReportWhereWithoutDataFilter({
      account_id: this.params.account_id,
      userData: userData,
      policy_status: this.params.policy_status,
      product_type: this.params.product_type,
      compensation_type: this.params.compensation_type,
      agentList: agentList,
      unSelectedAgentList,
    });
    const reportWhereCommissionProcessingDate =
      generateReportWhereCommissionProcessingDate({
        account_id: this.params.account_id,
        userData: userData,
        startDate: this.params.startDate,
        endDate: this.params.endDate,
        product_type: this.params.product_type,
        compensation_type: this.params.compensation_type,
        agentList: agentList,
        includeBlankDate: this.params.include_blanks,
        unSelectedAgentList,
      });
    this.statementWhere = generateStatementWhere({
      account_id: this.params.account_id,
      userData: userData,
      product_type: this.params.product_type,
      compensation_type: this.params.compensation_type,
      tag: this.params.tag,
      agentList: agentList,
      unSelectedAgentList,
    });

    const accountingTransactionsWhere = generateAccountingTransactionsWhere({
      account_id: this.params.account_id,
      agentList: agentList,
      unSelectedAgentList,
    });

    const contactsWhere = generateContactWhere({
      account_id: this.params.account_id,
      agentList: agentList,
      unSelectedAgentList,
    });

    const customersWhere = generateCustomersWhere({
      account_id: this.params.account_id,
      agentList: agentList,
      unSelectedAgentList,
    });

    const reportInclude: Prisma.report_dataInclude<DefaultArgs> = {
      statement_data: {
        select: {
          commission_amount: true,
        },
      },
    };

    const result = await Promise.all([
      this.getAgentCommissions(whereBase),
      this.getAccountById(),
      this.getContactGroups(this.params.account_id),
      this.getAgentCommissionsNames(whereBase),
      this.getReportData(reportWhere, reportInclude),
      this.getReportDataComisionProcessingDate(
        reportWhereCommissionProcessingDate
      ),
      this.getStatementData(
        { ...this.statementWhere },
        'processing_date',
        this.params.startDate,
        this.params.endDate,
        this.params.include_blanks
      ),
      this.getStatementDataGroupByPolicyAgentName(whereBase),
      this.getStatementDataGroupByCompensationType(whereBase),
      this.getReportDataGroupByPolicyStatus(whereBase),
      this.getReportDataGroupByProductType(whereBase),
      this.getStatementDataGroupByProductType(whereBase),
      this.getStatementTags(whereBase),
      this.getAccountingTransactions(accountingTransactionsWhere),
      this.getContacts(contactsWhere),
      this.getReportData(reportWhereWithoutDateFilter, reportInclude),
      this.getCustomers(customersWhere),
      this.getStatementDataByNoDate({ ...this.statementWhere }),
    ]);

    this.cacheService.set(
      `widgetsData-${cacheKey}-isAdminOrDataSpecialist`,
      isAdminOrDataSpecialist
    );
    return result;
  }

  // @ts-expect-error
  setAllDataFromServices(result) {
    if (!result) {
      return;
    }
    this.contactGroups = result[2];
    this.reportData = result[4];
    this.reportDataCommissionProcessingDate = result[5];
    this.compensationTypes = result[8];
    this.policyPolicyStatuses = result[9];
    this.policyProductTypes = result[10];
    this.commissionProductTypes = result[11];
    this.statementTags = result[12];
    this.accountingTransactions = result[13];
    this.contacts = result[14];
    this.reportDataWithoutDateFilter = result[15];
    this.customersData = result[16];
    this.statementDataByNoDate = result[17];
  }

  async getWidgets(widgetsToLoad: string[], loadFilters: boolean = true) {
    const result: Widget[] = [];
    let producerOnlyData = false;
    // If this.params.contact_str_id is set, and this.params.agent is set and agent list does not include this.params.contact_str_id, return empty array
    if (this.params.contact_str_id && this.params.agent) {
      // Convert agent to array if it is not
      const agentList = Array.isArray(this.params.agent)
        ? this.params.agent
        : [this.params.agent];
      if (!agentList.includes(this.params.contact_str_id)) {
        producerOnlyData = true;
      }
    }
    const serviceData = await this.loadAllDataFromServices();
    this.setAllDataFromServices(serviceData);

    for (const widgetId of widgetsToLoad) {
      if (widgetId == null) {
        continue;
      }
      const widget = await this.getWidgetSettingsById(widgetId);
      if (!widget || widget?.state === 'deleted') {
        continue;
      }
      try {
        const customWidget = await this.createCustomWidget(widget.spec);
        if (customWidget) {
          result.push({
            ...customWidget,
            id: widget.id,
            access: widget.access,
          });
        }
      } catch (error) {
        this.logger.error(
          `Error creating custom widget for ${widget.id}: ${error}`
        );
      }
    }

    return {
      widgets: producerOnlyData ? [] : result,
      dashboard_filter_by_agent: this.cacheService.get(
        `widgetsData-${JSON.stringify(this.params)}-isAdminOrDataSpecialist`
      ),
      filters: loadFilters ? this.buildFilterValues() : undefined,
    };
  }

  buildFilterValues() {
    const compensationTypes = this.compensationTypes;
    const tags = this.statementTags;
    const policyPolicyStatuses = this.policyPolicyStatuses;
    const policyProductTypes = this.policyProductTypes;
    const commissionProductTypes = this.commissionProductTypes;

    const compensationType = [
      DEFAULT_FILTER.BLANK_OPTION,
      // @ts-expect-error
      ...compensationTypes.map((data) => data.compensation_type).sort(),
    ];

    const tag = [
      DEFAULT_FILTER.BLANK_OPTION,
      // @ts-expect-error
      ...tags.map((data) => data.tag).sort(),
    ];

    const policyStatus = [
      DEFAULT_FILTER.BLANK_OPTION,
      // @ts-expect-error
      ...new Set([...policyPolicyStatuses.map((row) => row.policy_status)]),
    ]
      .filter((val) => val)
      .sort();

    const productType = [
      DEFAULT_FILTER.BLANK_OPTION,
      ...new Set([
        // @ts-expect-error
        ...policyProductTypes.map((row) => row.product_type),
        // @ts-expect-error
        ...commissionProductTypes.map((row) => row.product_type),
      ]),
    ]
      .filter((val) => val)
      .sort();

    // @ts-expect-error
    const agentGroup = this.contactGroups.map((row) => {
      return {
        id: row.id,
        name: row.name,
      };
    });

    return {
      compensationType,
      policyStatus,
      productType,
      agentGroup,
      tag,
    };
  }

  async getWidgetById(widgetId: string) {
    const result = await this.getWidgets([widgetId], false);
    return result?.widgets[0] ?? null;
  }

  async getWidgetSettingsById(id: string) {
    const widget = await prisma.widgets.findUnique({
      where: {
        id,
      },
    });
    return widget;
  }

  async createWidget(definition: WidgetDefinition) {
    const result = await this.loadAllDataFromServices();
    this.setAllDataFromServices(result);
    return this.createCustomWidget(definition);
  }

  async createCustomWidget(definition: WidgetDefinition) {
    // biome-ignore lint/suspicious/noExplicitAny: Waiting for refactor
    let source: Record<string, any>[] | null = null;
    switch (definition?.dataSource) {
      case 'commissions':
        if (definition?.filterByDate === 'none') {
          source = this.statementDataByNoDate;
        } else {
          source = await this.getStatementData(
            { ...this.statementWhere },
            definition?.filterByDate,
            this.params.startDate,
            this.params.endDate,
            this.params.include_blanks
          );
        }
        break;
      case 'policies':
        if (definition?.filterByDate === 'commission_processing_date') {
          source = this.reportDataCommissionProcessingDate;
        } else if (definition?.filterByDate === 'none') {
          source = this.reportDataWithoutDateFilter;
        } else {
          source = this.reportData;
        }

        // @ts-expect-error
        source = source.map((row) => {
          const commissionFields = calculateCommissionFields(
            row.statement_data
          );

          const item = {
            ...row,
            ...commissionFields,
          };

          return item;
        });
        break;
      case 'agentPayouts':
        source = this.accountingTransactions.map((transaction) => {
          return {
            ...transaction,
            contact_name: transaction.contact
              ? `${Formatter.contact(transaction.contact)}`
              : null,
            date_ymd: transaction.date
              ? dayjs(transaction.date)
                  .tz(this.params.timezone)
                  .format('YYYY-MM-DD')
              : null,
          };
        });
        break;
      case 'contacts':
        // TODO: Add filterByDate logic for contacts
        // if (definition?.filterByDate === 'start_date') {
        //   source = this.contacts;
        // } else if (definition?.filterByDate === 'end_date') {
        //   source = this.contacts;
        // } else if (definition?.filterByDate === 'none') {
        //   source = this.contacts;
        // } else {
        //   source = this.contacts;
        // }
        source = this.contacts;
        source = source.map((contact) => {
          return {
            ...contact,
            name: Formatter.contact(contact),
          };
        });
        break;
      case 'customers':
        // @ts-expect-error
        source = this.customersData.map((customer) => {
          const item = {
            ...customer,
          };

          const aggregationSelectors = definition?.aggregationSelectors;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          aggregationSelectors?.forEach((selector) => {
            switch (selector.field) {
              case 'contacts_balance':
                item.contacts_balance = customer.contacts.reduce(
                  // @ts-expect-error
                  (acc, contact) => acc + +contact.balance,
                  0
                );
                break;
              case 'accounting_transaction_details_amount':
                item.accounting_transaction_details_amount =
                  customer.accounting_transaction_details.reduce(
                    // @ts-expect-error
                    (acc, transaction) => acc + +transaction.amount,
                    0
                  );
                break;
              case 'report_data_commissionable_premium_amount':
                item.report_data_commissionable_premium_amount =
                  customer.report_data.reduce(
                    // @ts-expect-error
                    (acc, policy) =>
                      acc + +policy.commissionable_premium_amount,
                    0
                  );
                break;
              case 'report_data_premium_amount':
                item.report_data_premium_amount = customer.report_data.reduce(
                  // @ts-expect-error
                  (acc, policy) => acc + +policy.premium_amount,
                  0
                );
                break;
              case 'report_data_commissions_expected':
                item.report_data_commissions_expected =
                  customer.report_data.reduce(
                    // @ts-expect-error
                    (acc, policy) => acc + +policy.commissions_expected,
                    0
                  );
                break;
              case 'report_data_customer_paid_premium_amount':
                item.report_data_customer_paid_premium_amount =
                  customer.report_data.reduce(
                    // @ts-expect-error
                    (acc, policy) => acc + +policy.customer_paid_premium_amount,
                    0
                  );
                break;
              case 'report_data_split_percentage':
                item.report_data_split_percentage = customer.report_data.reduce(
                  // @ts-expect-error
                  (acc, policy) => acc + +policy.split_percentage,
                  0
                );
                break;
              case 'report_data_issue_age':
                item.report_data_issue_age = customer.report_data.reduce(
                  // @ts-expect-error
                  (acc, policy) => acc + +policy.issue_age,
                  0
                );
                break;
              case 'report_data_policy_term_months':
                item.report_data_policy_term_months =
                  customer.report_data.reduce(
                    // @ts-expect-error
                    (acc, policy) => acc + +policy.policy_term_months,
                    0
                  );
                break;
              default:
                break;
            }
          });

          return item;
        });
        break;
      default:
        break;
    }

    if (!source) {
      return null;
    }
    let result = null;

    source = this.filterMatcher(source, definition.filters);

    try {
      switch (definition.type) {
        case WidgetTypes.BOX:
          result = await this.createBoxWidget(definition, source);
          break;
        case WidgetTypes.CHART_DONUT:
          result = await this.createDonutChartWidget(definition, source);
          break;
        case WidgetTypes.CHART_LINE:
          result = await this.createLineChartWidget(definition, source);
          break;
        case WidgetTypes.CHART_BAR:
          result = await this.createBarChartWidget(definition, source);
          break;
        case WidgetTypes.TABLE:
          result = await this.createTableWidget(definition, source);
          break;
        default:
          return null;
      }
    } catch (e) {
      this.logger.error('Error creating widget', { error: e });
      Sentry.captureException(e);
      throw e;
    }
    return { ...result, spec: definition, rowCount: source?.length ?? 0 };
  }

  // @ts-expect-error
  createRandomReportData = (number) => {
    return Array.from({ length: number }, () => ({
      policy_id: faker.string.uuid(),
      policy_status: faker.helpers.arrayElement([
        'Active',
        'Cancelled',
        'Expired',
        'Pending',
      ]),
      product_type: faker.helpers.arrayElement([
        'Medical',
        'Ancillary',
        '401k',
        'Other',
        'Individual',
        'Medicare',
      ]),
      agent_name: faker.person.fullName(),
      agent_commissions: faker.number.int(1000),
      carrier_name: faker.company.name(),
      customer_name: faker.person.fullName(),
      writing_carrier_name: faker.company.name(),
      processing_date: faker.date.past(),
      geo_state: faker.location.state(),
      effective_date: faker.date.past(),
      premium_amount: faker.number.int(1000),
      commissionable_premium_amount: faker.number.int(1000),
      compensation_type: faker.lorem.word(),
      tags: faker.lorem.word(),
    }));
  };

  // @ts-expect-error
  createRandomReconciliationData = (number) => {
    return Array.from({ length: number }, () => ({
      policy_id: faker.string.uuid(),
      policy_status: faker.helpers.arrayElement([
        'Active',
        'Cancelled',
        'Expired',
        'Pending',
      ]),
      product_type: faker.helpers.arrayElement([
        'Medical',
        'Ancillary',
        '401k',
        'Other',
        'Individual',
        'Medicare',
      ]),
      agent_name: faker.person.fullName(),
      agent_commissions: faker.number.int(1000),
      carrier_name: faker.company.name(),
      customer_name: faker.person.fullName(),
      writing_carrier_name: faker.company.name(),
      processing_date: faker.date.past(),
      geo_state: faker.location.state(),
      effective_date: faker.date.past(),
      premium_amount: faker.number.int(1000),
      commissionable_premium_amount: faker.number.int(1000),
      compensation_type: faker.lorem.word(),
      tags: faker.lorem.word(),
    }));
  };

  // @ts-expect-error
  createReportDataCommissionProcessingDate = (number) => {
    return Array.from({ length: number }, () => ({
      policy_id: faker.string.uuid(),
      policy_status: faker.helpers.arrayElement([
        'Active',
        'Cancelled',
        'Expired',
        'Pending',
      ]),
      product_type: faker.helpers.arrayElement([
        'Medical',
        'Ancillary',
        '401k',
        'Other',
        'Individual',
        'Medicare',
      ]),
      agent_name: faker.person.fullName(),
      agent_commissions: faker.number.int(1000),
      carrier_name: faker.company.name(),
      customer_name: faker.person.fullName(),
      writing_carrier_name: faker.company.name(),
      processing_date: faker.date.past(),
      geo_state: faker.location.state(),
      effective_date: faker.date.past(),
      premium_amount: faker.number.int(1000),
      commissionable_premium_amount: faker.number.int(1000),
      compensation_type: faker.lorem.word(),
      tags: faker.lorem.word(),
    }));
  };

  // @ts-expect-error
  createRandomStatementData = (number) => {
    return Array.from({ length: number }, () => ({
      agent_name: faker.person.fullName(),
      agent_commissions: faker.number.int(1000),
      commission_amount: faker.number.int(1000),
      contacts: Array.from(
        { length: faker.number.int({ min: 1, max: 10 }) },
        () => faker.lorem.word()
      ),
      carrier_name: faker.company.name(),
      customer_name: faker.person.fullName(),
      writing_carrier_name: faker.company.name(),
      processing_date: faker.date.past(),
      geo_state: faker.location.state(),
      effective_date: faker.date.past(),
      report: {
        product_sub_type: faker.lorem.word(),
      },
      premium_amount: faker.number.int(1000),
      commissionable_premium_amount: faker.number.int(1000),
      compensation_type: faker.lorem.word(),
      tags: faker.lorem.word(),
    }));
  };
  async createMockCustomWidget(definition: WidgetDefinition) {
    let source: unknown;
    switch (definition?.dataSource) {
      case 'commissions':
        source = this.mockStatementData;
        break;
      case 'policies':
        source = this.mockReportData;
        if (definition.filterByDate === 'commission_processing_date') {
          source = this.mockReportDataCommissionProcessingDate;
        }
        break;
      default:
        break;
    }
    if (!source) {
      return null;
    }
    let result = null;

    source = this.filterMatcher(source, definition.filters);

    switch (definition.type) {
      case WidgetTypes.BOX:
        result = await this.createBoxWidget(definition, source);
        break;
      case WidgetTypes.CHART_DONUT:
        result = await this.createDonutChartWidget(definition, source);
        break;
      case WidgetTypes.CHART_LINE:
        result = await this.createLineChartWidget(definition, source);
        break;
      case WidgetTypes.CHART_BAR:
        result = await this.createBarChartWidget(definition, source);
        break;
      case WidgetTypes.TABLE:
        result = await this.createTableWidget(definition, source);
        break;
      default:
        return null;
    }
    return { ...result, spec: definition };
  }

  // @ts-expect-error
  filterMatcher(source, filters) {
    if (!filters || filters.length === 0) {
      return source;
    }
    // @ts-expect-error
    source = source.filter((row) => matchesFilter(row, filters));
    return source;
  }

  // @ts-expect-error
  mergeAgentCommissions(data) {
    const agentCommissions = {};
    for (const item of data) {
      if (item.agent_commissions) {
        for (const [key, value] of Object.entries(item.agent_commissions)) {
          // @ts-expect-error
          if (!agentCommissions[key]) {
            // @ts-expect-error
            agentCommissions[key] = 0;
          }
          // @ts-expect-error
          agentCommissions[key] += numberOrDefault(value);
        }
      }
    }
    return agentCommissions;
  }

  async calculateCommissionsBasedOnAgentType(
    // @ts-expect-error
    data,
    // @ts-expect-error
    calculationType,
    // @ts-expect-error
    initialValue,
    // @ts-expect-error
    agentType
  ) {
    const agent_commissions = this.mergeAgentCommissions(data);

    const agentsInAgentCommissionsWithoutTotal = Object.keys(
      agent_commissions
    ).filter((key) => key !== 'total');
    const agentsList =
      await this.contactService.getFilteredAgentListByAgentType(
        agentsInAgentCommissionsWithoutTotal,
        agentType
      );
    if (calculationType === 'sum') {
      return agentsList.reduce((acc, cur) => {
        // @ts-expect-error
        const agentCommissions = agent_commissions[cur];
        if (agentCommissions) {
          // @ts-expect-error
          return acc + numberOrDefault(agentCommissions);
        }
        return acc;
      }, 0);
    }
    if (calculationType === 'aggregate') {
      return agentsList.reduce((acc, cur) => {
        // @ts-expect-error
        const agentCommissions = agent_commissions[cur];
        if (agentCommissions) {
          const curValue = Object.entries(agentCommissions ?? {})
            .filter(([k, _v]) => k !== 'total')
            // @ts-expect-error
            .reduce((acc, [_k, v]) => acc + +v, 0);
          return curValue + acc;
        }
        return acc;
      }, initialValue);
    }
  }

  async createBoxWidget(
    definition: WidgetDefinition,
    source: unknown
  ): Promise<Widget> {
    // biome-ignore lint/suspicious/noExplicitAny: Need to readdress how we treat the incoming dataSource.
    const data = source as any;
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let value;
    const { dataField, aggregationMethod, resultFormatter } =
      this.getPropsFromDefinition(definition);
    if (
      definition.customRender?.enabled &&
      definition?.customRender.language &&
      definition?.customRender?.code
    ) {
      const data = await executeCustomCode(
        source,
        definition.customRender?.code,
        definition.customRender?.language,
        this.logger
      );
      return {
        widgetGroup: WidgetGroup.BOX,
        displayName: definition.name,
        value: data,
        enabled: true,
      };
    } else {
      switch (aggregationMethod) {
        case 'Sum':
          if (dataField === 'agent_commissions.agents_only') {
            value = await this.calculateCommissionsBasedOnAgentType(
              data,
              'sum',
              0,
              'Agent'
            );
          } else if (dataField === 'agent_commissions.sales_reps_only') {
            value = await this.calculateCommissionsBasedOnAgentType(
              data,
              'sum',
              0,
              'Sales rep'
            );
          } else {
            // @ts-expect-error
            value = data.reduce((acc, cur) => {
              let fieldValue = numberOrDefault(get(cur, dataField));
              if (dataField === 'agent_commissions') {
                fieldValue = numberOrDefault(cur.agent_commissions?.total);
              }
              return acc + fieldValue;
            }, 0);
          }
          break;
        case 'Average':
          // @ts-expect-error
          value = data.reduce((acc, cur) => {
            return acc + numberOrDefault(get(cur, dataField));
          }, 0);
          value = value / data.length;
          break;
        case 'Count':
          value = data.length;
          break;
        case 'Aggregate':
          // @ts-expect-error
          value = data.reduce((acc, cur) => {
            const curValue = Object.entries(get(cur, dataField) ?? {})
              .filter(([k, _v]) => k !== 'total')
              .reduce((acc, [_k, v]) => acc + +v, 0);
            return curValue + acc;
          }, 0);
          break;
        default:
          break;
      }
    }
    if (resultFormatter === FieldTypes.CURRENCY) {
      value = Formatter.currency(value);
    }
    return {
      widgetGroup: WidgetGroup.BOX,
      displayName: definition.name,
      value: value,
      enabled: true,
    };
  }

  async executeCustomCode(
    dataSource: unknown,
    customCode: string,
    customCodeLanguage: 'javascript' | 'python'
  ) {
    try {
      if (customCodeLanguage === 'python') {
        const pyodide = await loadPyodide();
        await pyodide.loadPackage('pandas');
        pyodide.runPython(customCode);
        const resultFn = pyodide.globals.get('main');
        const result = resultFn(dataSource);
        if (result && typeof result.toJs === 'function') {
          return result.toJs();
        }
        return result;
      }
      // biome-ignore lint/security/noGlobalEval: Necessary for dynamic code execution - will use a vm soon.
      const resultFn = eval(`(${customCode})`);
      return resultFn(dataSource);
    } catch (error) {
      this.logger.error('Error executing custom code', { error: error });
      throw error;
    }
  }

  // @ts-expect-error
  getPropsFromDefinition(definition) {
    let dataField = Array.isArray(definition.dataField)
      ? definition.dataField[0]
      : definition.dataField;
    let aggregationMethod = definition.calculation;
    let resultFormatter = definition.resultFormatter;
    if (
      definition.aggregationSelectors?.length > 0 &&
      definition.aggregationSelectors[0]?.field !== DEFAULT_FIELD_VALUE
    ) {
      dataField = definition.aggregationSelectors[0]?.field;
      aggregationMethod =
        definition.aggregationSelectors[0]?.aggregation_method;
      resultFormatter = definition.aggregationSelectors[0]?.formatter;
    }
    if (
      definition.aggregationSelectors &&
      definition.aggregationSelectors.length > 0
    ) {
      // If dataFieldsExpression is present, we should use the first field from aggregationSelectors
      dataField ||= definition.aggregationSelectors[0]?.field;
      aggregationMethod ||=
        definition.aggregationSelectors[0]?.aggregation_method;
      resultFormatter ||= definition.aggregationSelectors[0]?.formatter;
    }

    return { dataField, aggregationMethod, resultFormatter };
  }

  // @ts-expect-error
  async createDonutChartWidget(definition, source): Promise<Widget> {
    const { dataField, aggregationMethod, resultFormatter } =
      this.getPropsFromDefinition(definition);
    const data = source;
    const isNumberFormatted = this.isNumberFormat(resultFormatter);
    const isCurrencyFormatted = this.isCurrencyFormat(resultFormatter);
    let groupedData: Record<string, Record<string, unknown>> = {};
    const groupBy = definition.groupBys?.[0];

    const nameMapToDisplay = new Map();

    if (definition.customRender?.enabled) {
      const data = await executeCustomCode(
        source,
        definition.customRender?.code,
        definition.customRender?.language,
        this.logger
      );
      return {
        widgetGroup: WidgetGroup.CHART,
        displayName: definition.name,
        value: data,
        enabled: true,
      };
    } else {
      switch (aggregationMethod) {
        case 'Sum':
          // @ts-expect-error
          groupedData = data.reduce((acc, cur) => {
            if (definition.uniqueKey) {
              nameMapToDisplay.set(
                get(cur, definition.uniqueKey)?.toString(),
                get(cur, groupBy?.field)
              );
            }
            const valueGroup = get<string>(
              cur,
              definition.uniqueKey ?? groupBy?.field
            );

            const key = valueGroup;

            if (definition.dataFieldsExpression) {
              const value = calculateValueFromExpression(
                definition.dataFieldsExpression,
                cur
              );
              acc[key] = (acc[key] ?? 0) + BigNumber(value).toNumber();
            } else {
              acc[key] =
                (acc[key] ?? 0) +
                BigNumber(get(cur, dataField) || 0).toNumber();
            }
            return acc;
          }, {});
          for (const key of Object.keys(groupedData)) {
            // @ts-expect-error
            groupedData[key] = (+groupedData[key])?.toFixed(2);
          }
          break;
        case 'Count':
          // @ts-expect-error
          groupedData = data.reduce((acc, cur) => {
            if (definition.uniqueKey) {
              nameMapToDisplay.set(
                get(cur, definition.uniqueKey)?.toString(),
                get(cur, groupBy?.field)
              );
            }
            const valueGroup = get<string>(
              cur,
              definition.uniqueKey ?? groupBy?.field
            );
            const key = valueGroup;
            if (!acc[key]) {
              acc[key] = 1;
            } else {
              acc[key] = BigNumber(acc[key] ?? 0).toNumber() + 1;
            }
            return acc;
          }, {});
          break;
        default:
          break;
      }
    }
    // @ts-expect-error
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let contactNameIdMap;
    if (groupBy.field === 'contacts') {
      contactNameIdMap = await getContactNameIdMap(groupedData);
      groupedData = Object.entries(groupedData).reduce((acc, [k, v]) => {
        // @ts-expect-error
        const realName = convertContactsKeyToName(k, contactNameIdMap);
        // @ts-expect-error
        acc[realName] = v;
        return acc;
      }, {});
    }

    const sortingField = definition.sortBys?.[0];
    if (sortingField) {
      groupedData = sortingPick(groupedData, sortingField);
    }

    const chartData = Object.entries(groupedData || {}).map(([k, v]) => {
      return {
        name: nameMapToDisplay.get(k) ?? k,
        value: isNumberFormatted
          ? +v
          : isCurrencyFormatted
            ? (+v)?.toFixed(2)
            : v,
      };
    });
    return {
      widgetGroup: WidgetGroup.CHART,
      displayName: definition.name,
      value: this.getPieOptions(
        definition.displayName,
        chartData,
        isNumberFormatted
      ),
      enabled: true,
    };
  }

  // @ts-expect-error
  async getContactNameIdMapFromNested(groupedData) {
    const topLevelKeys = Object.keys(groupedData);

    const allContactIds = topLevelKeys.flatMap((key) =>
      Object.keys(groupedData[key])
    );

    const flattenedContactIds = allContactIds.flatMap((contactId) =>
      contactId.split(',')
    );

    const result =
      await this.contactService.getBatchContactNamesByStrIds(
        flattenedContactIds
      );
    const contactNameIdMap = {};
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    result.forEach((item) => {
      const [contactId, contactName] = item.split('::');
      // @ts-expect-error
      contactNameIdMap[contactId] = contactName;
    });

    return contactNameIdMap;
  }

  // @ts-expect-error
  isNumberFormat(formatter) {
    return formatter === 'number';
  }

  // @ts-expect-error
  isCurrencyFormat(formatter) {
    return formatter === FieldTypes.CURRENCY;
  }

  // @ts-expect-error
  async buildResultMap(definition, source) {
    const data = source;
    let resultMap = {};
    const groupBy = definition.groupBys?.[0];
    const timePeriod = groupBy?.timePeriod || 'month';
    let total = 0;
    const { dataField, aggregationMethod } =
      this.getPropsFromDefinition(definition);
    const nameMapToDisplay = new Map();

    const handlers = {
      // @ts-expect-error
      SumAccumulate: (value, dateBucket) => {
        total += value;
        // @ts-expect-error
        resultMap[dateBucket] = value + total;
      },
      // @ts-expect-error
      Aggregate: (value, dateBucket) => {
        total += value;
        // @ts-expect-error
        resultMap[dateBucket] = value + total;
      },
      // @ts-expect-error
      Sum: (value, dateBucket) => {
        // @ts-expect-error
        resultMap[dateBucket] = (resultMap[dateBucket] ?? 0) + value;
      },
      // @ts-expect-error
      Count: (_value, dateBucket) => {
        // @ts-expect-error
        resultMap[dateBucket] =
          // @ts-expect-error
          BigNumber(resultMap[dateBucket] || 0).toNumber() + 1;
      },
      // @ts-expect-error
      CountAccumulate: (_value, dateBucket) => {
        // @ts-expect-error
        resultMap[dateBucket] = (resultMap[dateBucket] ?? 0) + 1 + total;
        total += 1;
      },
    };

    for (const row of data) {
      const valueGroup = get<string>(
        row,
        definition?.uniqueKey ?? groupBy?.field
      );
      if (definition?.uniqueKey) {
        nameMapToDisplay.set(
          get(row, definition.uniqueKey)?.toString(),
          get(row, groupBy?.field)
        );
      }
      const bucket = getDateBucket(valueGroup, timePeriod) || valueGroup;
      if (bucket) {
        const value = definition.dataFieldsExpression
          ? calculateValueFromExpression(definition.dataFieldsExpression, row)
          : BigNumber(get(row, dataField) || 0).toNumber();
        // @ts-expect-error
        handlers[aggregationMethod]?.(value, bucket);
      }
    }
    if (groupBy?.field === 'contacts') {
      const contactNameIdMap = await getContactNameIdMap(resultMap);
      resultMap = Object.entries(resultMap).map(([k, v]) => {
        const realName = convertContactsKeyToName(k, contactNameIdMap);
        return [realName, v];
      });
    }

    return { resultMap, nameMapToDisplay };
  }

  // @ts-expect-error
  async createLineChartWidget(definition, source): Promise<Widget> {
    if (definition.customRender?.enabled) {
      const data = await executeCustomCode(
        source,
        definition.customRender?.code,
        definition.customRender?.language,
        this.logger
      );
      return {
        widgetGroup: WidgetGroup.CHART,
        displayName: definition.name,
        value: data,
        enabled: true,
      };
    }
    const { resultMap, nameMapToDisplay } = await this.buildResultMap(
      definition,
      source
    );
    return {
      widgetGroup: WidgetGroup.CHART,
      displayName: definition.name,
      value: this.getTimeSeriesOptions(
        resultMap,
        { type: 'line' },
        nameMapToDisplay
      ),
      enabled: true,
    };
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  async createStackedBarChartWidget(definition: WidgetDefinition, source: any) {
    const data = source;
    const { dataField } = this.getPropsFromDefinition(definition);
    const groupBy: GroupBy | undefined = definition?.groupBys?.[0];
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let resultMap: any = Object.entries(
      // @ts-expect-error
      data.reduce((result, row) => {
        // @ts-expect-error
        let legendKey = get<string>(row, definition.column);
        // @ts-expect-error
        let groupByKey = get<string>(row, groupBy?.field);
        if (getFieldType(definition.dataSource, groupByKey)?.includes('date')) {
          // @ts-expect-error
          const dateBucket = getDateBucket(groupBy?.field, groupBy?.timePeriod);
          if (dateBucket) {
            groupByKey = dateBucket;
          }
        }
        const dataFieldKey = get(row, dataField);
        if (!result[groupByKey]) {
          result[groupByKey] = { sum: 0 };
        }
        if (!legendKey) {
          legendKey = DEFAULT_FILTER.BLANK_OPTION;
        }
        if (!result[groupByKey][legendKey]) {
          result[groupByKey][legendKey] = 0;
        }
        if (dataField) {
          result[groupByKey][legendKey] += numberOrDefault(dataFieldKey);
          result[groupByKey].sum += numberOrDefault(dataFieldKey);
        } else {
          result[groupByKey][legendKey] += 1;
          result[groupByKey].sum += 1;
        }

        return result;
      }, {})
    );

    // @ts-expect-error
    resultMap = resultMap.reduce((r, [groupByKey, data]) => {
      if (definition.columnLimit) {
        const { sum, ...otherKeys } = data;
        const limitedKeys = Object.entries(otherKeys)
          .sort((a, b) => Number(b[1]) - Number(a[1]))
          .slice(0, definition.columnLimit)
          .reduce((acc, [key, value]) => {
            // @ts-expect-error
            acc[key] = value;
            return acc;
          }, {});

        r[groupByKey] = { sum, ...limitedKeys };
      } else {
        r[groupByKey] = data;
      }
      return r;
    }, {});
    if (groupBy?.field === 'contacts') {
      const contactNameIdMap = await getContactNameIdMap(resultMap);
      resultMap = Object.entries(resultMap).map(([k, v]) => {
        const realName = convertContactsKeyToName(k, contactNameIdMap);
        return [realName, v];
      });
    }

    if (definition.column === 'contacts') {
      const contactNameIdMap =
        await this.getContactNameIdMapFromNested(resultMap);
      resultMap = Object.entries(resultMap).reduce((acc, [y, innerObj]) => {
        // @ts-expect-error
        acc[y] = Object.entries(innerObj).reduce((innerAcc, [xx, value]) => {
          // @ts-expect-error
          const realName = contactNameIdMap[xx] || xx;
          // @ts-expect-error
          innerAcc[realName] = value;
          return innerAcc;
        }, {});
        return acc;
      }, {});
    }
    return resultMap;
  }

  // @ts-expect-error
  async createBarChartWidget(definition, source): Promise<Widget> {
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let groupedData;
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let nameMapToDisplayGroupedData;
    const sortingField = definition.sortBys?.[0];
    if (definition.customRender?.enabled) {
      const data = await executeCustomCode(
        source,
        definition.customRender?.code,
        definition.customRender?.language,
        this.logger
      );
      return {
        widgetGroup: WidgetGroup.CHART,
        displayName: definition.name,
        value: data,
        enabled: true,
      };
    }

    if (definition.column) {
      groupedData = await this.createStackedBarChartWidget(definition, source);
    } else {
      const { resultMap, nameMapToDisplay } = await this.buildResultMap(
        definition,
        source
      );
      groupedData = resultMap;
      nameMapToDisplayGroupedData = nameMapToDisplay;
      if (sortingField) {
        groupedData = sortingPick(groupedData, sortingField);
      }
    }
    let options: ChartDefinition;
    if (this.isMultiLevelObj(groupedData)) {
      options = this.getSeriesOptions(groupedData);
    } else {
      options = this.getTimeSeriesOptions(
        groupedData,
        { type: 'bar' },
        // @ts-expect-error
        nameMapToDisplayGroupedData
      );
    }
    return {
      widgetGroup: WidgetGroup.CHART,
      displayName: definition.name,
      value: options,
      enabled: true,
    };
  }

  // @ts-expect-error
  isMultiLevelObj(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return false;
    }

    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        return true;
      }
    }

    return false;
  }

  async createTableWidget(
    definition: WidgetDefinition,
    // biome-ignore lint/suspicious/noExplicitAny: Will readdress in follow up.
    source: any
  ): Promise<Widget> {
    if (
      definition?.customRender?.enabled &&
      definition?.customRender?.code &&
      definition?.customRender?.language
    ) {
      const data = await executeCustomCode(
        source,
        definition.customRender.code,
        definition.customRender.language,
        this.logger
      );
      return {
        widgetGroup: WidgetGroup.TABLE,
        displayName: definition.name,
        data: data,
        enabled: true,
      };
    }
    // Extract dataField, for each if it contains a '.' then it is a lookup
    let dataColumns: string[];
    if (definition.aggregationSelectors?.length > 0) {
      dataColumns = definition.aggregationSelectors?.map(
        (selector: AggregationSelector) => {
          const field = selector.field;
          if (field.includes('.')) {
            const fieldParts = field.replace('?', '').split('.');
            return fieldParts[0];
          }
          return field;
        }
      );
    } else {
      dataColumns = [definition.dataFieldsExpression ?? ''];
    }

    const groupBys = definition.groupBys ?? [];

    let tableArray: string[][] = [
      [...groupBys.map((g: GroupBy) => g.field), ...dataColumns],
    ];
    if (definition.dataFieldsExpression) {
      tableArray = [
        [
          ...groupBys.map((g: GroupBy) => g.field),
          ...dataColumns,
          definition.dataFieldsExpression,
        ],
      ];
    }
    // biome-ignore lint/suspicious/noExplicitAny: Will readdress in follow up
    let resultMap: Record<string, any> = {};
    const nameMapToDisplay = new Map();

    for (const row of source) {
      const compositeKey = groupBys.map((g: GroupBy) => {
        const value = get<string>(row, g.field);
        if (g.timePeriod) {
          return getDateBucket(
            value,
            (g.timePeriod ?? 'month') as WidgetTimePeriods
          );
        }
        return value;
      });
      const key = compositeKey.join('||');

      if (definition.uniqueKey) {
        nameMapToDisplay.set(
          get(row, definition.uniqueKey)?.toString(),
          compositeKey[0]
        );
      }
      if (!resultMap[key]) {
        resultMap[key] = {};
        groupBys.forEach((g: GroupBy, idx: number) => {
          resultMap[key][g.field] = compositeKey[idx];
        });
      }

      if (definition.dataFieldsExpression) {
        const value = calculateValueFromExpression(
          definition.dataFieldsExpression,
          row
        );
        resultMap[key] = value;
      } else {
        for (const selector of definition.aggregationSelectors) {
          const field = selector.field;
          const fieldParts = field.replace('?', '').split('.');
          const subKey = fieldParts[0];
          const fieldLookupValue = get(row, field);
          const fieldLookupValueNumber = numberOrDefault(fieldLookupValue) ?? 0;

          if (
            !(resultMap[key] as Record<string, unknown>)[`${subKey}-subtotal`]
          ) {
            (resultMap[key] as Record<string, unknown>)[`${subKey}-subtotal`] =
              0;
          }
          if ((resultMap[key] as Record<string, unknown>).Count === undefined) {
            (resultMap[key] as Record<string, unknown>).Count = 0;
          }
          (resultMap[key] as Record<string, unknown>)[`${subKey}-subtotal`] =
            ((resultMap[key] as Record<string, unknown>)[
              `${subKey}-subtotal`
            ] as number) + fieldLookupValueNumber;
          (resultMap[key] as Record<string, unknown>).Count =
            ((resultMap[key] as Record<string, unknown>).Count as number) + 1;
          switch (selector.aggregation_method) {
            case 'Sum':
              resultMap[key][subKey] =
                (typeof resultMap[key][subKey] === 'number'
                  ? resultMap[key][subKey]
                  : 0) + fieldLookupValueNumber;
              break;
            case 'Count':
              resultMap[key][subKey] = resultMap[key].Count;
              break;
            case 'Average':
              resultMap[key][subKey] =
                (resultMap[key] as { [key: string]: number })[
                  `${subKey}-subtotal`
                ] / (resultMap[key] as { [key: string]: number }).Count;
              break;
            default:
              break;
          }
        }
      }
    }
    // biome-ignore lint/suspicious/noExplicitAny: Need to address types in contacts methods.
    let contactNameIdMap: Record<string, any> = {};
    if (groupBys?.[0]?.field === 'contacts') {
      contactNameIdMap = await getContactNameIdMap(resultMap);
      resultMap = Object.entries(resultMap).reduce(
        (acc: Record<string, typeof v>, [k, v]) => {
          const realName = convertContactsKeyToName(k, contactNameIdMap);
          acc[realName] = v;
          return acc;
        },
        // biome-ignore lint/suspicious/noExplicitAny: Need to address types in contacts methods.
        {} as Record<string, any>
      );
    }

    const sortingField = definition.sortBys?.[0];
    if (sortingField) {
      resultMap = sortingPick(resultMap, sortingField);
    }

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(resultMap).forEach(async ([k, v]) => {
      // Render each row using the composite key
      const compositeKey = k.split('||');
      if (definition.dataFieldsExpression) {
        let value = v;
        if (definition.resultFormatter === FieldTypes.CURRENCY) {
          value = Formatter.currency(v);
        } else if (definition.resultFormatter === FieldTypes.PERCENTAGE) {
          value = Formatter.asPercentage(v);
        }

        tableArray.push([...compositeKey, value]);
      } else {
        tableArray.push([
          ...groupBys.map((g: GroupBy) => v[g.field]),
          ...definition.aggregationSelectors.map(
            (selector: AggregationSelector) => {
              const field = selector.field;
              const fieldParts = field.replace('?', '').split('.');
              const subKey = fieldParts[0];
              const value = v[subKey];
              if (selector.formatter === FieldTypes.CURRENCY) {
                return Formatter.currency(value);
              }
              if (selector.formatter === 'percent') {
                return Formatter.asPercentage(value);
              }
              if (selector.formatter === 'number') {
                return Number(value);
              }
              return value;
            }
          ),
        ]);
      }
    });

    const headers: string[] = tableArray[0];
    if (!Array.isArray(headers)) {
      throw new Error('Invalid table headers');
    } else {
      for (let i = 0; i < headers.length; i++) {
        headers[i] =
          getFieldLabel(definition.dataSource, headers[i]) ?? headers[i];
      }
    }

    return {
      widgetGroup: WidgetGroup.TABLE,
      displayName: definition.name,
      data: tableArray,
      enabled: true,
    };
  }

  // @ts-expect-error
  async getAgentCommissions(whereBase) {
    try {
      const agentCommissions = await this.statementService.getAgentCommissions(
        whereBase,
        this.params.startDate,
        this.params.endDate,
        this.params.include_blanks
      );
      return agentCommissions;
    } catch (error) {
      this.logger.error('Error getting agent commissions', { message: error });
      throw error;
    }
  }

  async getAccountById() {
    try {
      const accountSettings = await this.accountService.getAccountById(
        this.params.account_id
      );
      return accountSettings;
    } catch (error) {
      this.logger.error('Error getting account settings', { message: error });
      throw error;
    }
  }

  // @ts-expect-error
  async getContactGroups(account_id) {
    try {
      const contactGroups =
        await this.contactService.getContactGroups(account_id);
      return contactGroups;
    } catch (error) {
      this.logger.error('Error getting contact groups', { message: error });
      throw error;
    }
  }

  // @ts-expect-error
  async getAgentCommissionsNames(whereBase) {
    try {
      const commissionAgentNames =
        await this.statementService.getStatementDataGroupByPolicyAgentName(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return commissionAgentNames;
    } catch (error) {
      this.logger.error('Error getting agent commissions names', {
        message: error,
      });
      throw error;
    }
  }

  async getReportData(
    reportWhere: Prisma.report_dataWhereInput,
    includeStatement?: Prisma.report_dataInclude<DefaultArgs>
  ) {
    try {
      const reportData = await this.reportService.getReportData(
        reportWhere,
        true,
        includeStatement
      );
      return reportData;
    } catch (error) {
      this.logger.error('Error getting report data', { message: error });
      throw error;
    }
  }

  async getReportDataComisionProcessingDate(
    reportWhereCommissionProcessingDate: Prisma.report_dataWhereInput
  ) {
    try {
      const reportDataCommissionProcessingDate =
        await this.reportService.getReportDataComisionProcessingDate(
          reportWhereCommissionProcessingDate,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return reportDataCommissionProcessingDate;
    } catch (error) {
      this.logger.error(
        'Error getting report data commission processing date',
        { message: error }
      );
      throw error;
    }
  }

  async getStatementData(
    statementWhereClause: Prisma.statement_dataWhereInput,
    dateField: string,
    startDate?: Date,
    endDate?: Date,
    includeBlankDate?: boolean
  ) {
    const finalWhereClause = { ...statementWhereClause };
    if (Array.isArray(finalWhereClause.AND)) {
      finalWhereClause.AND = [
        ...finalWhereClause.AND,
        // @ts-expect-error
        generateDateClauses(dateField, startDate, endDate, includeBlankDate),
      ];
    }

    try {
      const statementData = await this.statementService.getStatementData(
        finalWhereClause,
        // @ts-expect-error
        null,
        this.statementSelectFields
      );
      return statementData;
    } catch (error) {
      this.logger.error("Couldn't fetch commissions", { message: error });
      throw error;
    }
  }

  async getStatementDataByNoDate(
    statementWhereClause: Prisma.statement_dataWhereInput
  ) {
    try {
      const statementData = await this.statementService.getStatementData(
        statementWhereClause,
        // @ts-expect-error
        null,
        this.statementSelectFields
      );
      return statementData;
    } catch (error) {
      this.logger.error('Error getting statement data', { message: error });
      throw error;
    }
  }

  // @ts-expect-error
  async getStatementDataGroupByPolicyAgentName(whereBase) {
    try {
      const policyAgentNames =
        await this.reportService.getReportDataGroupByPolicyAgentName(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return policyAgentNames;
    } catch (error) {
      this.logger.error(
        'Error getting statement data group by policy agent name',
        { message: error }
      );
      return null;
    }
  }

  // @ts-expect-error
  async getStatementDataGroupByCompensationType(whereBase) {
    try {
      const compensationTypes =
        await this.statementService.getStatementDataGroupByCompensationType(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return compensationTypes;
    } catch {
      this.logger.error(
        'Error getting statement data group by compensation type'
      );
      return null;
    }
  }

  // @ts-expect-error
  async getStatementTags(whereBase) {
    const statementData = await this.statementService.getStatementData(
      whereBase,
      undefined,
      { tags: true }
    );
    if (!statementData) {
      return null;
    }
    const statementTags = new Set();
    statementData.map((row) => {
      if (row.tags) {
        for (const tag of row.tags) {
          statementTags.add(tag);
        }
      }
    });
    return Array.from(statementTags).map((tag) => {
      return {
        tag: tag,
      };
    });
  }

  // @ts-expect-error
  async getReportDataGroupByPolicyStatus(whereBase) {
    try {
      const policyPolicyStatuses =
        await this.reportService.getReportDataGroupByPolicyStatus(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return policyPolicyStatuses;
    } catch (error) {
      this.logger.error('Error getting report data group by policy status', {
        message: error,
      });
      return null;
    }
  }

  // @ts-expect-error
  async getReportDataGroupByProductType(whereBase) {
    try {
      const policyProductTypes =
        await this.reportService.getReportDataGroupByProductType(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return policyProductTypes;
    } catch (error) {
      this.logger.error('Error getting report data group by product type', {
        message: error,
      });
      return null;
    }
  }

  // @ts-expect-error
  async getStatementDataGroupByProductType(whereBase) {
    try {
      const commissionProductTypes =
        await this.statementService.getStatementDataGroupByProductType(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return commissionProductTypes;
    } catch (error) {
      this.logger.error('Error getting statement data group by product type', {
        message: error,
      });
      return null;
    }
  }

  async executeServiceCalls() {
    const results = await runInBatch({
      items: this.serviceCalls,
      onBatch: async (items) => {
        await Promise.all(
          // @ts-expect-error
          items.map((call) => call.service[call.method](...call.params))
        );
      },
      batchSize: 50,
    });

    // Map the results to the corresponding variables dynamically
    this.serviceCalls.forEach((call, index) => {
      // Set results[index] to the object of the call.resultKey
      // @ts-expect-error
      this[call.resultKey] = results[index];
    });
  }

  async getAccountingTransactions(
    where: Prisma.accounting_transactionsWhereInput
  ) {
    const accountingTransactions =
      await this.accountingTransactionsService.getAccountingTransactions(
        where,
        {
          contact: true,
        }
      );
    return accountingTransactions;
  }

  async getContacts(where: Prisma.contactsWhereInput) {
    const contacts = await this.contactService.getContacts({
      where,
    });
    return contacts;
  }
}
