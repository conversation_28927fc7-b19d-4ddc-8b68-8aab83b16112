import { container } from '@/ioc';
import { ContactService } from '@/services/contact';
import type { SortBy } from 'common/dto/widgets';
import { numberOrDefault } from 'common/helpers';
import dayjs from 'dayjs';
import { loadPyodide } from 'pyodide';
import type { AppLoggerService } from '../logger/appLogger';

export const calculateValueFromExpression = (
  // biome-ignore lint/suspicious/noExplicitAny: Will readdress in follow up.
  dataFieldsExpression: any,
  // biome-ignore lint/suspicious/noExplicitAny: Will readdress in follow up.
  cur: any
) => {
  const fields = dataFieldsExpression.split(/[+*/-]/);
  const operator = dataFieldsExpression.match(/[+*/-]/)[0];

  if (operator === '*' || operator === '/') {
    const initialValue = operator === '*' ? 1 : numberOrDefault(cur[fields[0]]);
    // @ts-expect-error: Will readdress in follow up.
    return fields.reduce((result, field, index) => {
      if (operator === '*' && index === 0)
        // @ts-expect-error: Will readdress in follow up.
        return result * numberOrDefault(cur[field]);
      if (operator === '/' && index === 0) return result;
      return operator === '*'
        ? // @ts-expect-error: Will readdress in follow up.
          result * numberOrDefault(cur[field])
        : // @ts-expect-error
          result / numberOrDefault(cur[field]);
    }, initialValue);
  } else {
    // @ts-expect-error: Will readdress in follow up.
    return fields.reduce((sum, field) => {
      return operator === '+'
        ? sum + numberOrDefault(cur[field])
        : // @ts-expect-error: Will readdress in follow up.
          sum - numberOrDefault(cur[field]);
    }, 0);
  }
};

export const convertContactsKeyToName = (
  key: string,
  // biome-ignore lint/suspicious/noExplicitAny: Will readdress in follow up.
  contactNameIdMap: Record<string, any>
) => {
  const contactIds = key.split(',');
  const contactNames = contactIds.map(
    (contactId) => contactNameIdMap[contactId]
  );
  return contactNames.join(',');
};

const parseNumericValue = (value: unknown): number | null => {
  if (typeof value === 'number') return value;
  if (typeof value === 'object' && value !== null) {
    if (value instanceof Date) {
      return value.getTime();
    }
    if (typeof value.valueOf === 'function') {
      const val = value.valueOf();
      if (typeof val === 'number') return val;
      if (typeof val === 'string') {
        const date = dayjs(val);
        if (date.isValid()) return date.valueOf();
      }
    }
    return 0;
  }
  if (typeof value === 'string') {
    const date = dayjs(value);
    if (date.isValid()) {
      return date.valueOf();
    }
    const numericString = value.replace(/[^0-9.-]/g, '');
    const parsed = parseFloat(numericString);
    return Number.isNaN(parsed) ? null : parsed;
  }
  return null;
};

export const sortingPick = (
  groupedData: Record<string, Record<string, unknown>>,
  sortingField: SortBy
) => {
  const { order, limit, field } = sortingField;

  const entries = Object.entries(groupedData).map(([key, value]) => {
    const sortValue = value[field];

    const numericValue = parseNumericValue(sortValue);
    return {
      key,
      value,
      sortValue,
      numericValue,
    };
  });

  entries.sort((a, b) => {
    const aIsNumber =
      typeof a.numericValue === 'number' &&
      a.numericValue !== null &&
      !Number.isNaN(a.numericValue);
    const bIsNumber =
      typeof b.numericValue === 'number' &&
      b.numericValue !== null &&
      !Number.isNaN(b.numericValue);

    if (aIsNumber && bIsNumber) {
      if (order === 'asc') {
        return (a.numericValue ?? 0) - (b.numericValue ?? 0);
      } else {
        return (b.numericValue ?? 0) - (a.numericValue ?? 0);
      }
    } else {
      if (order === 'asc') {
        return String(a.sortValue).localeCompare(String(b.sortValue));
      } else {
        return String(b.sortValue).localeCompare(String(a.sortValue));
      }
    }
  });

  const topN = entries.slice(0, limit ?? undefined);

  const result: Record<string, Record<string, unknown>> = {};
  for (const entry of topN) {
    result[entry.key] = groupedData[entry.key] as Record<string, unknown>;
  }
  return result;
};

export const getContactNameIdMap = async (
  groupedData: Record<string, unknown>
) => {
  const contactIds = Object.keys(groupedData);
  const flattenedContactIds = contactIds.flatMap((contactId) =>
    contactId.split(',')
  );
  const contactService = container.get<ContactService>(ContactService);
  const result =
    await contactService.getBatchContactNamesByStrIds(flattenedContactIds);
  const contactNameIdMap: Record<string, string> = {};
  for (const item of result) {
    const contactId = item.split('::')[0];
    const contactName = item.split('::')[1];
    contactNameIdMap[contactId] = contactName;
  }
  return contactNameIdMap;
};

export const executeCustomCode = async (
  dataSource: unknown,
  customCode: string,
  customCodeLanguage: 'javascript' | 'python',
  logger: AppLoggerService
) => {
  try {
    if (customCodeLanguage === 'python') {
      const pyodide = await loadPyodide();
      await pyodide.loadPackage('pandas');
      pyodide.runPython(customCode);
      const resultFn = pyodide.globals.get('main');
      const result = resultFn(dataSource);
      if (result && typeof result.toJs === 'function') {
        return result.toJs();
      }
      return result;
    }
    // biome-ignore lint/security/noGlobalEval: Necessary for dynamic code execution - will use a vm soon.
    const resultFn = eval(`(${customCode})`);
    return resultFn(dataSource);
  } catch (error) {
    logger.error('Error executing custom code', { error: error });
    throw error;
  }
};
