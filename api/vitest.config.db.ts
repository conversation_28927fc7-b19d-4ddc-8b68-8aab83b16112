import { defineConfig } from 'vitest/config';
import path from 'node:path';
import dotenv from 'dotenv';

dotenv.config({ path: path.resolve(__dirname, '.env.test') });

export default defineConfig({
  test: {
    globals: true,
    env: {
      NODE_ENV: 'test',
    },
    fileParallelism: false,
    setupFiles: ['test.setup.ts'],
    include: [
      'pages/api/**/*.db.test.ts',
      'pages/api/**/**/*.db.test.ts',
      'services/**/*.db.test.ts',
      'lib/**/*.db.test.ts',
      'pages/api/**/*.test.db.ts',
      'pages/api/**/**/*.test.db.ts',
      'services/**/*.test.db.ts',
      'lib/**/*.test.db.ts',
    ],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, ''),
    },
  },
});
