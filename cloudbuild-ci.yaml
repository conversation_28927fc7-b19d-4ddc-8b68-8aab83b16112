steps:
  - id: configure-git-ssh
    name: 'gcr.io/cloud-builders/git'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        gcloud secrets versions access latest --secret="dev-git-ssh" > /root/.ssh/id_rsa
        chmod 600 /root/.ssh/id_rsa
        ssh-keyscan -t rsa github.com >> /root/.ssh/known_hosts
    volumes:
      - name: 'ssh'
        path: /root/.ssh
  - id: 'doc-only-change-check'
    name: 'gcr.io/google.com/cloudsdktool/google-cloud-cli:alpine'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        ./scripts/check-doc-only-pr.sh "${PROJECT_ID}" "${_BASE_BRANCH}" "${BUILD_ID}"
    volumes:
      - name: 'ssh'
        path: /root/.ssh
    waitFor: [ 'configure-git-ssh' ]
  - id: npm-install
    name: "node:$_NODE_VERSION"
    entrypoint: "npm"
    args: ["ci"]
    dir: "."
  - id: lint and format check
    waitFor: ["npm-install"]
    name: "node:$_NODE_VERSION"
    entrypoint: "npm"
    args: [ "run", "lint:ci" ]
    dir: "."
  - id: type check
    waitFor: ["npm-install"]
    name: "node:$_NODE_VERSION"
    entrypoint: "npm"
    args: [ "run", "type:check" ]
    dir: "."
  - id: unit tests
    waitFor: ["npm-install"]
    name: "node:$_NODE_VERSION"
    entrypoint: "npm"
    args: ["run", "test:unit"]
    dir: "./api"
  - id: build web
    name: "node:$_NODE_VERSION"
    entrypoint: "npm"
    args: ["run", "build"]
    waitFor: ["lint and format check"]
    timeout: "1300s"
    dir: "./web"
  - id: run-integration-tests
    waitFor: ['-']
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args:
      - -c
      - |
        if [ "${_ENV_NAME}" != "dev" ]; then
          echo "ENV_NAME is ${_ENV_NAME}, skipping integration tests (only runs for dev)"
          exit 0
        fi

        docker compose -f docker-compose.integration.yaml up --abort-on-container-exit --exit-code-from integration-test integration-test
options:
  pool:
    name: "projects/fintary-dev/locations/us-central1/workerPools/Default"
