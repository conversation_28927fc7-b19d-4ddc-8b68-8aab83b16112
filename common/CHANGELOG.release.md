# common

## Releases on 2025-08-27

### Version 0.25.0
<details>

### Minor Changes
 - Implementation of paginated filters for commissions page

### Patch Changes
 - Updated styles on documents table view for file name cells
 - Add Product sub type and Notes filters to Commissions screen
 - Change the symbol of 'Missing commissions data' to make it more easily distinguishable from 'Missing policy data'
 - Added missing migration for updated_at schema change
 - Added new action update for bulk agent grid levels tools
 - Status update and tweaks for document statuses: 1. Add ‘Upload Failed’ status if document upload fails. 2. Add ‘Processing’ (yellow) status if auto-processing is completed but auto-import is not. 3. Add ‘Cancel’ status option for users to set for some documents. 4. Improve status chip UI. 5. Fix the edit issue on the admin document page.
 - 1. Fix the issue where updating a processor unintentionally affected the companies_processors table 2. Fix the issue where email uploads always set the document type to spreadsheet 3. Improve the processor selector logic
 - Add group_name field configuration to reconciliation fields
</details>

## Releases on 2025-08-22

### Version 0.24.38
<details>

### Patch Changes
 - Fix logic bug in the buildPrismaInclude function and add unit tests for it.
</details>

## Releases on 2025-08-21

### Version 0.24.37
<details>

### Patch Changes
 - Refactoring duplicated logic for date validation and bulk operations
</details>

## Releases on 2025-08-19

### Version 0.24.36
<details>

### Patch Changes
 - Fix comp calc method compGrid when no comp grids are used and basis is policy premium
</details>

### Version 0.24.35
<details>

### Patch Changes
 - Update E2E test locators and refine the test workflow.
</details>

### Version 0.24.34
<details>

### Patch Changes
 - Added conditional to handle case when no flags are selected
</details>

## Releases on 2025-08-18

### Version 0.24.33
<details>

### Patch Changes
 - Add support for contacts.start_date and relative comparison in comp profiles
 - Add commission rate percent range filter
</details>

## Releases on 2025-08-15

### Version 0.24.32
<details>

### Patch Changes
 - Revert Agent and Agent commissions format from newlines to comma separation
</details>

### Version 0.24.31
<details>

### Patch Changes
 - Improve filter load times in commissions page
 - Remove duplicated fields in reconciliation_data
</details>

## Releases on 2025-08-14

### Version 0.24.30
<details>

### Patch Changes
 - Fixed error when loading data processing
</details>

### Version 0.24.29
<details>

### Patch Changes
 - Reduce DEFAULT_BATCH_SIZE to fix export on Reconciliation page
 - Refactoring receivable values comunication in polices editing view
</details>

## Releases on 2025-08-13

### Version 0.24.28
<details>

### Patch Changes
 - Hotfix the document auto-import failure caused by date format issue.
 - New tool to bulk assign agents to compensation profiles, streamlining setup for large groups of data
 - Added python support for custom code execution in widgets.
 - Optimize the mapping select.
 - Add ZohoWorker integration
</details>

## Releases on 2025-08-12

### Version 0.24.27
<details>

### Patch Changes
 - Enhance grouping rules functionality by allowing selection of specific rule IDs in data processing
</details>

## Releases on 2025-08-11

### Version 0.24.26
<details>

### Patch Changes
 - Add custom report delete feature
 - Update the base url for e2e tests
 - Add e2e tests to preview and prod.
 - Speed up login / middleware by reducing/parallelizing db queries
</details>

