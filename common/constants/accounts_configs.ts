export const AccountConfigsTypes = {
  DATA_SYNC: 'dataSync',
  PER_AGENT_PAYOUT_STATUS: 'per_agent_payout_status',
  SESSION: 'session',
  RECONCILIATION: 'reconciliation',
  FEATURE_FLAGS: 'feature_flags',
} as const;

export type AccountConfigsTypes =
  (typeof AccountConfigsTypes)[keyof typeof AccountConfigsTypes];

export const AccountConfigsTypesOptions: {
  label: string;
  id: AccountConfigsTypes;
}[] = [
  { label: 'Data sync', id: AccountConfigsTypes.DATA_SYNC },
  {
    label: 'Per agent payout status',
    id: AccountConfigsTypes.PER_AGENT_PAYOUT_STATUS,
  },
  { label: 'Session', id: AccountConfigsTypes.SESSION },
  { label: 'Reconciliation', id: AccountConfigsTypes.RECONCILIATION },
  { label: 'Feature flags', id: AccountConfigsTypes.FEATURE_FLAGS },
];
