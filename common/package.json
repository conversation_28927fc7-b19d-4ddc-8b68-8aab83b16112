{"name": "common", "version": "0.25.0", "private": true, "main": "index.js", "scripts": {"test:unit": "vitest run --config vitest.config.unit.ts", "test:watch": "vitest --config vitest.config.unit.ts", "dev": "tsc -w", "release": "npm run release --prefix ../", "changelog": "npm run changelog --prefix ../", "deep-clean": "rm -rf node_modules", "lint": "biome check", "lint-fix": "biome check --write", "type:check": "tsc --noEmit --pretty"}, "dependencies": {"@prisma/client": "^6.6.0", "bignumber.js": "^9.3.1", "chrono-node": "2.8.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "currency": "^4.1.0", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "nanoid": "^5.1.5", "pretty-ms": "^9.2.0", "reflect-metadata": "^0.2.2", "typescript": "^5.9.2", "zod": "^3.25.51", "zod-openapi": "^4.2.4"}, "devDependencies": {"vitest": "^3.2.4", "vitest-mock-extended": "^3.1.0"}}