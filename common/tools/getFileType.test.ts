import { describe, it, expect } from 'vitest';
import { getFileType } from './getFileType';
import { fileTypes, fileTypeExtensions } from 'common/globalTypes';

describe('getFileType', () => {
  it('Given a PDF path, when calling getFileType, then it returns PDF', () => {
    const result = getFileType('document.pdf');
    expect(result).toBe(fileTypes.PDF);
  });

  it('Given spreadsheet extensions, when calling getFileType, then it returns SPREADSHEET', () => {
    expect(getFileType('data.csv')).toBe(fileTypes.SPREADSHEET);
    expect(getFileType('report.xlsx')).toBe(fileTypes.SPREADSHEET);
    expect(getFileType('table.xls')).toBe(fileTypes.SPREADSHEET);
  });

  it('Given HTML extension, when calling getFileType, then it returns HTML', () => {
    const result = getFileType('index.html');
    expect(result).toBe(fileTypes.HTML);
  });

  it('Given image extensions, when calling getFileType, then it returns IMAGE', () => {
    expect(getFileType('photo.jpg')).toBe(fileTypes.IMAGE);
    expect(getFileType('photo.jpeg')).toBe(fileTypes.IMAGE);
    expect(getFileType('photo.png')).toBe(fileTypes.IMAGE);
  });

  it('Given mixed case extension, when calling getFileType, then it is matched correctly', () => {
    expect(getFileType('UPPERCASE.PdF')).toBe(fileTypes.PDF);
    expect(getFileType('chart.XLSX')).toBe(fileTypes.SPREADSHEET);
    expect(getFileType('snapshot.JpEg')).toBe(fileTypes.IMAGE);
  });

  it('Given an unknown extension, when calling getFileType, then it defaults to PDF', () => {
    expect(getFileType('archive.zip')).toBe(fileTypes.PDF);
    expect(getFileType('noextension')).toBe(fileTypes.PDF);
  });

  it('Given all extensions in fileTypeExtensions, when calling getFileType, then each maps correctly', () => {
    for (const [type, extensions] of Object.entries(fileTypeExtensions)) {
      for (const ext of extensions) {
        const path = `file${ext}`;
        const result = getFileType(path);
        expect(result).toBe(type);
      }
    }
  });
});
