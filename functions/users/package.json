{"name": "users", "version": "1.0.1", "main": "build/index.js", "private": true, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "local": "rm -rf ./build && npm run build && firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy:dev": "firebase use fintary-dev && firebase deploy --only functions:users-functions", "deploy:prod": "firebase use fintary-prod && firebase deploy --only functions:users-functions", "logs": "firebase functions:log", "type:check": "tsc --noEmit --pretty"}, "dependencies": {"@google-cloud/secret-manager": "^6.0.1", "firebase-admin": "^12.7.0", "firebase-functions": "^6.4.0", "pg": "^8.12.0"}, "devDependencies": {"@types/pg": "^8.11.6", "typescript": "^5.9.2"}}