# release

## Releases on 2025-08-27

### Version 4.37.0
<details>

### Minor Changes
 - Implementation of paginated filters for commissions page

### Patch Changes
 - Fixed grid template selection in component grid tools to display the selected template correctly.
 - Updated styles on documents table view for file name cells
 - Fix policy syncing failure for TrailStone & search agents with syncId
 - Add "Select only results" option to EnhancedSelect
 - Fixed Sentry issues: 1. Error: A file name must be specified 2. <PERSON><PERSON><PERSON>: Unable to extract file content from init.txt
 - Update OneHQ to sync target premium , excess_premium & customer_paid_premium_amount
 - Fix the issue where company name is not matched because of extra white space
 - Added missing migration for updated_at schema change
 - Improved performance when loading data in compensation profiles edit mode.
 - Added new action update for bulk agent grid levels tools
 - Status update and tweaks for document statuses: 1. Add ‘Upload Failed’ status if document upload fails. 2. Add ‘Processing’ (yellow) status if auto-processing is completed but auto-import is not. 3. Add ‘Cancel’ status option for users to set for some documents. 4. Improve status chip UI. 5. Fix the edit issue on the admin document page.
 - Enable commission calc without using grouped commissions for Allied
 - 1. Fix the issue where updating a processor unintentionally affected the companies_processors table 2. Fix the issue where email uploads always set the document type to spreadsheet 3. Improve the processor selector logic
</details>

## Releases on 2025-08-22

### Version 4.36.10
<details>

### Patch Changes
 - Fixing wrong values being show on receivable values
</details>

## Releases on 2025-08-21

### Version 4.36.9
<details>

### Patch Changes
 - Fixed the issue where compensation profiles with a large set of agents could not be updated
</details>

## Releases on 2025-08-19

### Version 4.36.8
<details>

### Patch Changes
 - Fix the issue where some documents are missing in the admin documents section and some companies are missing from the companies filter.
</details>

## Releases on 2025-08-18

### Version 4.36.7
<details>

### Patch Changes
 - Fixed agent transactions search
 - Add support for contacts.start_date and relative comparison in comp profiles
 - Fix the issue that MAG worker will sync duplicated contact hiearchies for the same agent
</details>

## Releases on 2025-08-15

### Version 4.36.6
<details>

### Patch Changes
 - Improve filter load times in commissions page
</details>

## Releases on 2025-08-14

### Version 4.36.5
<details>

### Patch Changes
 - Fixed error when loading data processing
</details>

### Version 4.36.4
<details>

### Patch Changes
 - AgencyIntegratorWorker now syncs team_code and code for Broker Alliance
 - Fixed bug on compensation types with options not found
</details>

## Releases on 2025-08-13

### Version 4.36.3
<details>

### Patch Changes
 - New tool to bulk assign agents to compensation profiles, streamlining setup for large groups of data
 - Fix receivable calculation due to not correctly checking policy year
</details>

## Releases on 2025-08-12

### Version 4.36.2
<details>

### Patch Changes
 - Fix the issue where some date values are not in UTC format.
 - Use agent split of 100% for IMOs
</details>

## Releases on 2025-08-11

### Version 4.36.1
<details>

### Patch Changes
 - Fixing manual group handling on the Commissions page
</details>

### Version 4.36.0
<details>

### Minor Changes
 - Supports for advance commission schedules

### Patch Changes
 - Remove role checking for grouping rules page
 - Adds group search and alphabetical sorting for Data update tools
 - Extend data actions tool to being able to set json fields in the db
</details>

