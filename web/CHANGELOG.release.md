# web

## Releases on 2025-08-27

### Version 10.24.0
<details>

### Minor Changes
 - Implementation of paginated filters for commissions page

### Patch Changes
 - Fixed grid template selection in component grid tools to display the selected template correctly.
 - Fixed styling in Cell component to properly display the copy icon
 - Updated styles on documents table view for file name cells
 - Add "Select only results" option to EnhancedSelect
 - Fixed Sentry issues: 1. Error: A file name must be specified 2. <PERSON>rror: Unable to extract file content from init.txt
 - Change the symbol of 'Missing commissions data' to make it more easily distinguishable from 'Missing policy data'
 - Added missing migration for updated_at schema change
 - Improved performance when loading data in compensation profiles edit mode.
 - Added new action update for bulk agent grid levels tools
 - Status update and tweaks for document statuses: 1. Add ‘Upload Failed’ status if document upload fails. 2. Add ‘Processing’ (yellow) status if auto-processing is completed but auto-import is not. 3. Add ‘Cancel’ status option for users to set for some documents. 4. Improve status chip UI. 5. Fix the edit issue on the admin document page.
 - 1. Fix the issue where updating a processor unintentionally affected the companies_processors table 2. Fix the issue where email uploads always set the document type to spreadsheet 3. Improve the processor selector logic
 - Update parsing of JSONB columns in raw query to handle string values correctly
- Updated dependencies [c84ead5]
- Updated dependencies [f08df2b]
- Updated dependencies [f4eea23]
- Updated dependencies [5a11f35]
- Updated dependencies [aba2670]
- Updated dependencies [46de324]
- Updated dependencies [e8c9711]
- Updated dependencies [ad70b95]
- Updated dependencies [096309d]
  - common@0.25.0
</details>

## Releases on 2025-08-22

### Version 10.23.18
<details>

### Patch Changes
 - Fixing wrong values being show on receivable values
- Updated dependencies [1e38b54]
  - common@0.24.38
</details>

## Releases on 2025-08-21

### Version 10.23.17
<details>

### Patch Changes
 - Refactoring duplicated logic for date validation and bulk operations
 - Add CS internal metrics
- Updated dependencies [4032e96]
  - common@0.24.37
</details>

## Releases on 2025-08-19

### Version 10.23.16
<details>

### Patch Changes
- Updated dependencies [5f5f652]
  - common@0.24.36
</details>

### Version 10.23.15
<details>

### Patch Changes
 - Added a unique `key` prop to force `VariableSizeList` to re-mount when options change
- Updated dependencies [e53c7ff]
  - common@0.24.35
</details>

### Version 10.23.14
<details>

### Patch Changes
 - Fix the issue where some documents are missing in the admin documents section and some companies are missing from the companies filter.
- Updated dependencies [b1dfd46]
  - common@0.24.34
</details>

## Releases on 2025-08-18

### Version 10.23.13
<details>

### Patch Changes
 - Fixed agent transactions search
 - Add support for contacts.start_date and relative comparison in comp profiles
 - Add commission rate percent range filter
- Updated dependencies [07601b0]
- Updated dependencies [cd05597]
  - common@0.24.33
</details>

## Releases on 2025-08-15

### Version 10.23.12
<details>

### Patch Changes
- Updated dependencies [5c3b1e4]
  - common@0.24.32
</details>

### Version 10.23.11
<details>

### Patch Changes
 - Remove duplicated fields in reconciliation_data
- Updated dependencies [7140e5a]
- Updated dependencies [29bfa23]
  - common@0.24.31
</details>

## Releases on 2025-08-14

### Version 10.23.9
<details>

### Patch Changes
 - Add no results found message in BaseSelect component
</details>

### Version 10.23.8
<details>

### Patch Changes
 - Fixed bug on compensation types with options not found
 - Refactoring receivable values comunication in polices editing view
- Updated dependencies [f0353b6]
- Updated dependencies [8a10d7c]
  - common@0.24.29
</details>

### Version 10.23.10
<details>

### Patch Changes
 - Fixed error when loading data processing
- Updated dependencies [8eb41ce]
  - common@0.24.30
</details>

## Releases on 2025-08-13

### Version 10.23.7
<details>

### Patch Changes
 - New tool to bulk assign agents to compensation profiles, streamlining setup for large groups of data
 - Added python support for custom code execution in widgets.
 - Optimize the mapping select.
 - Add ZohoWorker integration
- Updated dependencies [d7b897b]
- Updated dependencies [4a521fb]
- Updated dependencies [5e3cdac]
- Updated dependencies [1e5aa79]
- Updated dependencies [47d8646]
  - common@0.24.28
</details>

## Releases on 2025-08-12

### Version 10.23.6
<details>

### Patch Changes
 - Use agent split of 100% for IMOs
 - Enhance grouping rules functionality by allowing selection of specific rule IDs in data processing
- Updated dependencies [948812e]
  - common@0.24.27
</details>

## Releases on 2025-08-11

### Version 10.23.5
<details>

### Patch Changes
 - Added legend size limit widgets and enabled time series for stacked bar charts
 - Fix bug where the page crashes immediately when navigating to the "Data actions" page.
</details>

### Version 10.23.4
<details>

### Patch Changes
 - Allow admin to create global data update
 - Adds group search and alphabetical sorting for Data update tools
 - Add commissions custom types fields to bulk update csv
 - Fixed document groups not passing date filters correctly to the documents view.
 - Add custom report delete feature
 - Documents page more date filters text overlapping fix
 - 1. Add filters to show documents with missing companies, types, statement amounts, and unverified fields. 2. Tweak the workflow to auto-verify untrusted fields when the document is processed. 3. Reduce the confidence threshold to get more classification results.
 - Speed up login / middleware by reducing/parallelizing db queries
- Updated dependencies [e9a974f]
- Updated dependencies [d8e887e]
- Updated dependencies [f4dc66e]
- Updated dependencies [c2de5f7]
  - common@0.24.26
</details>

