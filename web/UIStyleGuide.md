# UI Style Guide

Components should be custom or based on MUI components unless there's good reason to use another UI component. Avoid adding other UI frameworks since we already have MUI. In general, stay consistent with existing UI styles and conventions.

## Component directory structure

Child components, hooks, utility functions, et cetera should be stored in child directories like `components`, `hooks`, and `utils`. Do not import and export child members from `index.tsx` If a child needs to be accessed outside the module, consider a shared location like `commons`.

```
components/
└── Foo/
    ├── index.tsx
    └── hooks/
        ├── useWix.ts
    └── components/
        ├── Bar.tsx
        ├── Baz.tsx
        └── Qux.tsx
```

The actual TSX component `<Foo/>`, should be defined in `index.tsx` and exported so that it may be reused across the codebase.

### More on custom hooks and useEffect

All `useEffect` hooks must be extracted into dedicated custom hooks and placed in the `hooks` directory within the component folder. This approach improves maintainability, enforces clear separation of concerns, and prevents redundant or undocumented logic from accumulating in components. Each custom hook must be accompanied by unit tests to ensure reliability and facilitate future refactoring. Avoid placing raw `useEffect` calls directly in component files.

Use the extension `.vitest.ts` or `.vitest.tsx` for your custom hook tests. If you the regular `.test.**` extension, e2e tests will try to run it and fail.

## String capitalization

Titles and headings generally capitalize only the first word. In some cases, for top-level titles or to maintain consistency, each word may be capitalized if necessary, but the preference is to capitalize just the first word of a phrase.
Button text, field labels, and similar text should only capitalize the first word (except when justified, e.g. proper nouns/names, abbreviations/acronyms).

- [Button text]
- Page heading
- Link text

Generally avoid using ALL CAPS anywhere, except for bold warnings/issues that warrant attention.

## Dialog buttons

Positive / Primary CTA (e.g. Save, Submit, Import, Add) goes on the bottom right.  
Neutral / Secondary CTAs are still aligned to the right but go to the left of the primary.  
Negative / Cancel CTAs (e.g. Delete, Cancel, Disable, Remove) should be the left-most button. It can be aligned left or left of the remaining buttons.

### Common actions (and examples)

- `Add` - Inserts a new item into a list. Used in forms.
```tsx
<IconButton variant="text" onClick={addSortBy} data-name="add-sort-by">
    Add
</IconButton>
```
- `Remove` - Removes an item from a list. Used in forms.
```tsx
<IconButton onClick={onRemove}>
    <RemoveCircleOutline />
</IconButton>
```
- `Close` - Closes out a modal, form, window, or pane.
```tsx
<IconButton onClick={handleClose}>
    <Close className="group-hover:rotate-180 transition-all origin-center" />
</IconButton>
```
    
## Forms

Forms fields should be of size "small" throughout the app.

## Admin

When possible, add treatment to areas of the app that are only visible/available to admins, so that it's clear what the user sees vs what admins see. Typically, we use the MUI AdminPanelSettings icon (looks like a badge/shield) or a lock emoji 🔒.

## Exceptions

The above rules should be the default, but there may be situations where we deviate from the above for various reasons.
