#!/bin/bash

# copy PDF.js worker file to public directory
cp ../node_modules/pdfjs-dist/build/pdf.worker.min.mjs public/ 2>/dev/null || echo "Warning: Could not copy PDF.js worker file"

# Get the available system memory (in KB)
available_mem=$(grep MemAvailable /proc/meminfo | awk '{print $2}')

# Convert available memory to bytes
available_mem_bytes=$((available_mem * 1024))

max_mem_bytes=$((available_mem_bytes * 80 / 100))

# Convert bytes to MB
max_mem_mb=$((max_mem_bytes / 1024 / 1024))

echo "$max_mem_mb MB of memory available, running build..."
vite build
