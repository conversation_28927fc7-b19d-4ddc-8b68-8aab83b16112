import { readFileSync, writeFileSync, existsSync } from 'node:fs';
import { resolve } from 'node:path';

export default function addVersion() {
  const versionInfo = {
    buildTime: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
  };

  return {
    name: 'add-version-info',

    config() {
      console.log('Version Info:', versionInfo);
      return {
        define: {
          __BUILD_TIME__: JSON.stringify(versionInfo.buildTime),
          __APP_ENV__: JSON.stringify(versionInfo.environment),
        },
      };
    },

    // Use the `buildStart` hook to run side effects like writing files.
    // This hook runs only during the build process (`vite build`).
    buildStart() {
      // The original plugin only writes the file in production.
      // `vite build` sets NODE_ENV to 'production' by default.
      if (versionInfo.environment === 'production') {
        const manifestPath = resolve(process.cwd(), 'public', 'manifest.json');

        if (!existsSync(manifestPath)) {
          console.error(
            `[${this.name}] public/manifest.json not found. Skipping version injection.`
          );
          return;
        }

        const manifest = JSON.parse(readFileSync(manifestPath, 'utf8'));

        writeFileSync(
          manifestPath,
          `${JSON.stringify({ ...manifest, versionInfo }, null, 2)}\n`,
          'utf8'
        );

        console.log(
          `[${this.name}] ✅ Injected version info into public/manifest.json`
        );
      }
    },
  };
}
