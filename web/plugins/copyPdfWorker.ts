import { readFileSync } from 'node:fs';
import { resolve } from 'node:path';
import type { Plugin } from 'vitest/config';

export default function copyPdfWorker(): Plugin {
  return {
    name: 'copy-pdf-worker',
    generateBundle() {
      try {
        // Copy PDF.js worker file during build
        const workerPath = resolve(
          '../node_modules/pdfjs-dist/build/pdf.worker.min.mjs'
        );
        const workerContent = readFileSync(workerPath);

        this.emitFile({
          type: 'asset',
          fileName: 'pdf.worker.min.mjs',
          source: workerContent,
        });

        console.log('✅ Copied PDF.js worker file');
      } catch (error) {
        const errMsg = error instanceof Error ? error.message : String(error);
        console.warn('⚠️  Warning: Could not copy PDF.js worker file:', errMsg);
      }
    },
  };
}
