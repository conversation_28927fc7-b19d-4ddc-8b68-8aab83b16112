import type { Plugin } from 'vitest/config';

export default function externalNonceInjection(): Plugin {
  return {
    name: 'external-nonce-injection',
    transformIndexHtml(html: string) {
      // Create reference to external nonce injection script
      const nonceScriptTag =
        '<script src="/nonce-injector.js" data-nonce-injector></script>';

      // Inject the script reference as the very first element in <head>
      const updatedHtml = html.replace(
        '<head>',
        `<head>\n    ${nonceScriptTag}`
      );

      console.log('🔗 Added external nonce injector script reference');

      return updatedHtml;
    },
  };
}
