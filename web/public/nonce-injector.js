(() => {
  // Generate cryptographically secure nonce
  function generateNonce() {
    if (
      typeof crypto === 'undefined' ||
      typeof crypto.getRandomValues !== 'function'
    ) {
      // Fallback for non-secure contexts
      return `${fallback}-${Math.random().toString(36).substr(2, 22)}`;
    }
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode.apply(null, array))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
      .substr(0, 22);
  }

  // Generate CSP policy with nonce
  function generateCSPPolicy(nonce) {
    const isProduction = window.location.hostname === 'app.fintary.com';
    const isLocalhost = window.location.hostname === 'localhost';

    const policies = [
      isLocalhost
        ? "default-src 'self' *"
        : "default-src 'self' wss://*.hotjar.com https://*.hotjar.io wss://*.tawk.to https://*.fintary.com https://*.googleapis.com https://*.tawk.to https://*.sentry.io https://featureassets.org https://*.googletagmanager.com https://*.hotjar.com https://www.google-analytics.com https://*.jsdelivr.net https://prodregistryv2.org",
    ];

    // For production (app.fintary.com), use strict CSP without unsafe directives
    if (isProduction) {
      policies.push(`script-src 'self' 'nonce-${nonce}'`);
      policies.push(
        `script-src-elem 'self' 'nonce-${nonce}' https://cdn.jsdelivr.net https://*.tawk.to https://*.hotjar.com https://www.googletagmanager.com https://apis.google.com`
      );
    } else {
      // For non-production environments, keep unsafe directives for development
      policies.push(
        `script-src 'self' 'nonce-${nonce}' 'unsafe-eval' 'unsafe-inline'`
      );
      policies.push(
        `script-src-elem 'self' 'nonce-${nonce}' 'unsafe-inline' https://cdn.jsdelivr.net https://*.tawk.to https://*.hotjar.com https://www.googletagmanager.com https://apis.google.com`
      );
    }

    policies.push(
      "style-src 'self' 'unsafe-inline' https://*.tawk.to",
      "style-src-elem 'self' 'unsafe-inline' https://*.tawk.to",
      "style-src-attr 'self' 'unsafe-inline'",
      "img-src 'self' data: blob:",
      "font-src 'self' data: https://embed.tawk.to/",
      "worker-src 'self' blob:",
      "form-action 'self'"
    );

    return policies.join('; ');
  }

  // Apply nonce to existing elements
  function applyNonceToElements(nonce) {
    // Apply to existing script tags (except nonce injector)
    const scripts = document.querySelectorAll(
      'script:not([src*="nonce-injector"])'
    );
    for (const script of scripts) {
      if (!script.hasAttribute('nonce')) {
        script.setAttribute('nonce', nonce);
      }
    }

    // Apply to existing style tags
    const styles = document.querySelectorAll('style');
    for (const style of styles) {
      if (!style.hasAttribute('nonce')) {
        style.setAttribute('nonce', nonce);
      }
    }
  }

  // Enhanced function to handle all types of style-related elements
  function applyNonceToStyleElements(elements, nonce) {
    for (const element of elements) {
      if (
        element.tagName === 'SCRIPT' &&
        !element.hasAttribute('nonce') &&
        !element.src.includes('nonce-injector')
      ) {
        element.setAttribute('nonce', nonce);
      }

      if (element.tagName === 'STYLE' && !element.hasAttribute('nonce')) {
        element.setAttribute('nonce', nonce);
      }

      if (
        element.tagName === 'LINK' &&
        element.rel === 'stylesheet' &&
        !element.hasAttribute('nonce')
      ) {
        element.setAttribute('nonce', nonce);
      }
    }
  }

  // Observe DOM changes and apply nonce to new elements
  function observeNewElements(nonce) {
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        for (const node of mutation.addedNodes) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            applyNonceToStyleElements([node], nonce);

            const childScripts = node?.querySelectorAll(
              'script:not([nonce]):not([src*="nonce-injector"])'
            );
            const childStyles = node?.querySelectorAll('style:not([nonce])');
            const childLinks = node?.querySelectorAll(
              'link[rel="stylesheet"]:not([nonce])'
            );

            if (childScripts) {
              applyNonceToStyleElements(Array.from(childScripts), nonce);
            }
            if (childStyles) {
              applyNonceToStyleElements(Array.from(childStyles), nonce);
            }
            if (childLinks) {
              applyNonceToStyleElements(Array.from(childLinks), nonce);
            }
          }
        }
      }
    });

    observer.observe(document.documentElement, {
      childList: true,
      subtree: true,
      attributes: false,
    });

    return observer;
  }

  // Enhanced CSP error handler
  function setupCSPErrorHandler() {
    const reportedViolations = new Set();

    document.addEventListener('securitypolicyviolation', (e) => {
      const violationKey =
        e.violatedDirective +
        '|' +
        e.effectiveDirective +
        '|' +
        (e.sourceFile || 'unknown');

      if (!reportedViolations.has(violationKey)) {
        reportedViolations.add(violationKey);

        if (e.violatedDirective.startsWith('style-src')) {
          console.warn('⚠️ Unexpected style violation (should be allowed):', {
            directive: e.violatedDirective,
            source: e.sourceFile,
            line: e.lineNumber,
          });
        } else {
          console.warn('🛡️ CSP Violation:', {
            blockedURI: e.blockedURI,
            violatedDirective: e.violatedDirective,
            effectiveDirective: e.effectiveDirective,
            sourceFile: e.sourceFile,
            lineNumber: e.lineNumber,
          });
        }
      }
    });
  }

  // Main execution
  try {
    // Generate nonce
    const nonce = generateNonce();

    // Store nonce globally
    window.__CSP_NONCE__ = nonce;

    // Apply nonce to existing elements
    applyNonceToElements(nonce);

    // Set up observer for new elements
    observeNewElements(nonce);

    // Set up CSP error handler
    setupCSPErrorHandler();

    // Update or create CSP meta tag
    const cspPolicy = generateCSPPolicy(nonce);
    let cspMeta = document.querySelector(
      'meta[http-equiv="Content-Security-Policy"]'
    );

    if (!cspMeta) {
      cspMeta = document.createElement('meta');
      cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
      document.head.insertBefore(cspMeta, document.head.firstChild);
    }

    cspMeta.setAttribute('content', cspPolicy);

    // Log success
    const isProduction = window.location.hostname === 'app.fintary.com';
    console.log('🔑 External nonce injection completed:', nonce);
    console.log(
      `🛡️ CSP policy: ${isProduction ? 'Production mode - strict CSP' : 'Development mode - permissive CSP'}`
    );
    console.log('✅ Using external script to avoid inline CSP issues');
  } catch (error) {
    console.error('❌ External nonce injection failed:', error);

    // Fallback
    try {
      const fallbackPolicy =
        "default-src 'self' 'unsafe-inline' 'unsafe-eval' *; img-src 'self' data: blob: *; font-src 'self' data: *;";
      let cspMeta = document.querySelector(
        'meta[http-equiv="Content-Security-Policy"]'
      );

      if (!cspMeta) {
        cspMeta = document.createElement('meta');
        cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
        document.head.insertBefore(cspMeta, document.head.firstChild);
      }

      cspMeta.setAttribute('content', fallbackPolicy);
      console.warn('🔄 Applied fallback CSP policy');

      window.__CSP_NONCE__ = `${fallback}-${Math.random().toString(36).substr(2, 9)}`;
    } catch (fallbackError) {
      console.error('❌ Even fallback CSP failed:', fallbackError);
    }
  }
})();
