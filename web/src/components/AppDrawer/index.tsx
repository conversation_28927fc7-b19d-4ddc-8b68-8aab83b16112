import {
  AdminPanelSettingsOutlined,
  ArrowForwardIosSharp,
  CloudUploadOutlined,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Tooltip,
  useMediaQuery,
} from '@mui/material';
import MuiAccordion, { type AccordionProps } from '@mui/material/Accordion';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import MuiAccordionSummary, {
  type AccordionSummaryProps,
} from '@mui/material/AccordionSummary';
import { styled, useTheme } from '@mui/material/styles';
import { SystemRoles } from 'common/globalTypes';
import { useContext, useEffect, useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import { getRouteConfig } from '@/components/AppDrawer/routeConfig';
import { Pages } from '@/components/SettingsView/ViewsFieldsSettings';
import BaseModal from '@/components/UploadModal';
import { UIStateContext } from '@/contexts/UIStateProvider';
import useSnackbar from '@/contexts/useSnackbar';
import { useUserInfo } from '@/hooks/useUserInfo';
import API from '@/services/API';
import { hasAccess } from '@/services/helpers';
import { useAccountStore, useMenuStore, useRoleStore } from '@/store';
import { Roles } from '@/types';
import { LoginAsUser } from './LoginAsUser';

const FintaryAdminOnlyIcon = ({ access }) => {
  return Array.isArray(access) &&
    access?.length === 1 &&
    access?.includes(Roles.FINTARY_ADMIN) ? (
    <span
      style={{
        color: 'transparent',
        textShadow: '0 0 0 #e8e8e8',
      }}
    >
      🔒
    </span>
  ) : null;
};

const AppDrawer = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showUploadModal, setShowUploadModal] = useState(false);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [menuItems, setMenuItems] = useState<any[]>([[]]);
  const { menuOpen, setMenuOpen } = useMenuStore();

  const isDesktop = useMediaQuery('(min-width:600px)');

  const drawerWidth = 200;
  const theme = useTheme();
  const pageViewId = Pages.map((page) => page.id);
  const adminRoles = [Roles.ACCOUNT_ADMIN, Roles.ACCOUNT_ADMIN_RO];

  const { userRole } = useRoleStore();
  const { selectedAccount } = useAccountStore();
  const { showSnackbar } = useSnackbar();
  const mode = selectedAccount?.accountMode;

  const { data: accountSettings, isFetched: accountSettingsReady } =
    API.getBasicQuery(`accounts/settings`);
  const classificationPoster = API.getMutation(
    'documents/document-classification',
    'POST'
  );

  const { data: { fintaryAdmin } = {} } = useUserInfo();

  const {
    role: [role, setRole],
    appDrawerExpanded: [expanded, setExpanded],
  } = useContext(UIStateContext);

  const documentViewSettings = accountSettings?.pages_settings?.add_documents;
  const showAddDocument =
    documentViewSettings?.show_page ??
    (userRole &&
      [Roles.ACCOUNT_ADMIN, Roles.DATA_SPECIALIST].includes(userRole));

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const configuredAccountSettings = {
      ...accountSettings,
      pages_settings: { ...accountSettings?.pages_settings },
    };
    if (userRole && !adminRoles.includes(userRole)) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      pageViewId.forEach((key) => {
        if (!accountSettings?.pages_settings?.[key]) {
          configuredAccountSettings.pages_settings[key] = {
            show_page: false,
          };
        }
      });
    }
    const routeConfig = getRouteConfig(
      mode,
      selectedAccount,
      configuredAccountSettings
    );
    setMenuItems(routeConfig);
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Nov/2024
     * MISSED REFs:  'adminRoles' and 'pageViewId'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mode, role, userRole, accountSettings, selectedAccount]);

  useEffect(() => {
    setMenuOpen(isDesktop);
  }, [isDesktop, setMenuOpen]);

  useEffect(() => {
    if (['<EMAIL>'].includes(fintaryAdmin?.email || ''))
      setRole('demo');
    else if (fintaryAdmin) setRole(SystemRoles.ADMIN);
  }, [fintaryAdmin, setRole]);

  const handleClose = (result) => {
    setShowUploadModal(false);
    if (result) {
      showSnackbar(result);
    }
  };

  const Accordion = styled((props: AccordionProps) => (
    <MuiAccordion disableGutters elevation={0} square {...props} />
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ))(({ theme: t }) => ({
    border: 0,
    '&:not(:last-child)': {
      borderBottom: 0,
    },
    '&:before': {
      display: 'none',
    },
  }));

  const AccordionSummary = styled((props: AccordionSummaryProps) => (
    <MuiAccordionSummary
      expandIcon={
        isDesktop ? <ArrowForwardIosSharp sx={{ fontSize: '0.9rem' }} /> : null
      }
      sx={{
        pl: 0.5,
        borderRadius: '0 24px 24px 0',
        '&:hover': { backgroundColor: 'whitesmoke' },
      }}
      {...props}
    />
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ))(({ theme: t }) => ({
    '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
      transform: 'rotate(90deg)',
    },
    '& .MuiAccordionSummary-content': {
      marginTop: 0,
      marginBottom: 0,
    },
  }));

  const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
    padding: theme.spacing(0),
    borderTop: 0,
  }));

  const filteredMenuItems = menuItems
    .filter((section) =>
      hasAccess(section.access, userRole, role === SystemRoles.ADMIN)
    )
    .map((section) =>
      section
        .filter((item) =>
          hasAccess(item?.access, userRole, role === SystemRoles.ADMIN)
        )
        .map((item) => ({
          ...item,
          target: Array.isArray(item.target)
            ? item.target.filter((t) =>
                hasAccess(t?.access, userRole, role === SystemRoles.ADMIN)
              )
            : item.target,
        }))
    )
    .filter((section) => section.length > 0);

  const initializeCompanyClassifier = async () => {
    try {
      await classificationPoster.mutateAsync({
        file: {
          name: 'init.txt',
          type: 'txt',
          content: btoa('warmup'),
        },
      });
    } catch (error) {
      console.error('Classifier initialized failed', error);
    }
  };

  const handleUploadClick = async () => {
    setShowUploadModal(true);
    initializeCompanyClassifier();
  };

  return (
    <>
      <Drawer
        variant={isDesktop ? 'persistent' : 'temporary'}
        open={menuOpen}
        sx={{ width: menuOpen ? drawerWidth : 0 }}
        onClose={isDesktop ? undefined : () => setMenuOpen(false)}
      >
        <Toolbar />
        <Box
          sx={{
            flexGrow: 1,
            width: drawerWidth,
            overflow: 'auto',
            overflowX: 'hidden',
          }}
        >
          {accountSettingsReady &&
            filteredMenuItems.map((section, i) => (
              <div key={`${section.label ?? i}`}>
                <Divider />
                <List>
                  {section.map(
                    ({
                      label,
                      icon,
                      target,
                      access,
                      endIcon,
                      disabled,
                      tooltip,
                    }) =>
                      Array.isArray(target) ? (
                        <Accordion expanded={expanded[label]} key={label}>
                          <AccordionSummary
                            onClick={() => {
                              if (!expanded[label]) {
                                navigate(target[0].target);
                              }
                              setExpanded({
                                ...expanded,
                                [label]: !expanded[label],
                              });
                            }}
                            sx={{ pr: 1, minHeight: 40 }}
                          >
                            <ListItem
                              component="div"
                              sx={{
                                pl: 1,
                                pr: 0,
                                py: 0.5,
                                '&:hover': { backgroundColor: 'unset' },
                                ...(disabled && {
                                  opacity: 0.5,
                                  pointerEvents: 'none',
                                  cursor: 'default',
                                }),
                              }}
                            >
                              <ListItemIcon
                                style={{
                                  minWidth: 40,
                                  color: 'black',
                                }}
                              >
                                {icon}
                              </ListItemIcon>
                              <ListItemText primary={label} />
                              <FintaryAdminOnlyIcon access={access} />
                            </ListItem>
                          </AccordionSummary>
                          <AccordionDetails>
                            {target.map((t) => (
                              <Tooltip
                                title={t.tooltip ?? ''}
                                placement="right"
                                key={t.label}
                              >
                                <ListItem
                                  key={t.label}
                                  onClick={() => {
                                    if (!isDesktop) setMenuOpen(false);
                                  }}
                                  component={t.disabled ? 'div' : Link}
                                  {...(!t.disabled && { to: t.target })}
                                  sx={{
                                    ...{ pl: 2.5, py: 0.25, opacity: 0.8 },
                                    ...(location.pathname ===
                                    t.target.split('?')[0]
                                      ? {
                                          backgroundColor: '#2096f322',
                                          borderRadius: '0 24px 24px 0',
                                        }
                                      : {}),
                                    color: 'unset',
                                    ...(t.disabled && {
                                      opacity: 0.5,
                                      pointerEvents: 'none',
                                      cursor: 'default',
                                    }),
                                  }}
                                >
                                  <ListItemIcon
                                    style={{
                                      color:
                                        location.pathname ===
                                        t.target.split('?')[0]
                                          ? theme.palette.primary.main
                                          : 'unset',
                                      minWidth: 40,
                                    }}
                                  >
                                    {t.icon}
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={t.label}
                                    primaryTypographyProps={
                                      location.pathname === t.target
                                        ? {
                                            color: theme.palette.primary.main,
                                            fontWeight: 600,
                                            fontSize: 15,
                                          }
                                        : { fontSize: 15 }
                                    }
                                  />

                                  <FintaryAdminOnlyIcon access={t.access} />
                                </ListItem>
                              </Tooltip>
                            ))}
                          </AccordionDetails>
                        </Accordion>
                      ) : (
                        <Tooltip
                          title={tooltip ?? ''}
                          placement="right"
                          enterDelay={1000}
                          key={target}
                        >
                          <ListItem
                            key={target}
                            onClick={() => {
                              if (!isDesktop) setMenuOpen(false);
                            }}
                            component={disabled ? 'div' : Link}
                            {...(!disabled && { to: target })}
                            sx={{
                              ...(location.pathname === target
                                ? {
                                    color: 'unset',
                                    backgroundColor: '#2096f322',
                                    borderRadius: '0 24px 24px 0',
                                    px: 1.5,
                                    py: 0.5,
                                  }
                                : {
                                    color: 'unset',
                                    px: 1.5,
                                    py: 0.5,
                                    '&:hover': {
                                      borderRadius: '0 24px 24px 0',
                                    },
                                  }),
                              ...(disabled && {
                                opacity: 0.5,
                                pointerEvents: 'none',
                                cursor: 'default',
                              }),
                            }}
                            secondaryAction={
                              access === SystemRoles.ADMIN && (
                                <IconButton
                                  edge="end"
                                  disabled
                                  sx={{ opacity: 0.5, mr: -2 }}
                                >
                                  {endIcon ?? <AdminPanelSettingsOutlined />}
                                </IconButton>
                              )
                            }
                          >
                            <ListItemIcon
                              style={{
                                color:
                                  location.pathname === target
                                    ? theme.palette.primary.main
                                    : 'unset',
                                minWidth: 40,
                              }}
                            >
                              {icon}
                            </ListItemIcon>
                            <ListItemText
                              primary={label}
                              primaryTypographyProps={
                                location.pathname === target
                                  ? {
                                      color: theme.palette.primary.main,
                                      fontWeight: 600,
                                    }
                                  : {}
                              }
                              sx={
                                location.pathname === target
                                  ? {
                                      color: 'white',
                                      fontWeight: 600,
                                      '&:hover': {
                                        color: 'black',
                                      },
                                    }
                                  : {}
                              }
                            />
                            <FintaryAdminOnlyIcon access={access} />
                          </ListItem>
                        </Tooltip>
                      )
                  )}
                </List>
              </div>
            ))}
        </Box>
        <Box
          sx={{
            width: drawerWidth,
            zIndex: 1,
          }}
        >
          <LoginAsUser showAddDocument={showAddDocument} />
          {showAddDocument && (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Tooltip
                title={`You can also email statements to documents${selectedAccount?.accountShortName ? `+${selectedAccount?.accountShortName}` : ''}@fintary.com for processing`}
              >
                <Button
                  variant="contained"
                  startIcon={<CloudUploadOutlined />}
                  onClick={handleUploadClick}
                  sx={{
                    width: 180,
                    mt: 1,
                    mb: 1.5,
                    boxShadow: 0,
                    backgroundColor: '#e5f1fd',
                    color: '#2196f3',
                    fontWeight: 'bold',
                    '&:hover': {
                      backgroundColor: '#e5f1fd',
                      color: '#2196f3',
                      boxShadow: 0,
                    },
                  }}
                >
                  Upload data
                </Button>
              </Tooltip>
            </Box>
          )}
        </Box>
      </Drawer>
      <BaseModal open={showUploadModal} handleClose={handleClose} />
    </>
  );
};

export default AppDrawer;
