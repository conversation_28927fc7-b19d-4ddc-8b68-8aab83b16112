import { getEnvVariable } from '@/env';
import {
  AddLink,
  CalculateOutlined,
  CallSplitOutlined,
  MergeOutlined,
} from '@mui/icons-material';
import { Button, Tooltip, useMediaQuery } from '@mui/material';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { AccountIds, WorkerNames } from 'common/constants';
import type { SyncParamsDTO } from 'common/dto/data_processing/sync';
import { SystemRoles } from 'common/globalTypes';
import { useContext, useEffect, useMemo, useState } from 'react';
import { Navigate, useSearchParams } from 'react-router-dom';

import GroupingEdit from '@/components/CommissionsDataView/Grouping';
import ReconcileEdit from '@/components/CommissionsDataView/ReconcileEdit';
import UngroupingEdit from '@/components/CommissionsDataView/Ungrouping';
import EnhancedDataView from '@/components/organisms/EnhancedDataView';
import { LoadingContext } from '@/contexts/LoadingContext';
import { ReconciliationConfirmProvider } from '@/contexts/ReconciliationConfirmProvider';
import { UIStateContext } from '@/contexts/UIStateProvider';
import useSnackbar from '@/contexts/useSnackbar';
import { useSyncedFieldsNew } from '@/contexts/useSyncedFields';
import { useExportOptions } from '@/hooks/useExportOptions';
import API from '@/services/API';
import Statements from '@/services/Statements';
import { useAccountStore, useRoleStore } from '@/store';
import { Roles } from '@/types';
import { useCreatePolicy } from './hooks/useCreatePolicy';
import { useAdditionalActions } from './hooks/useAdditionalActions';

const CommissionsDataView = ({ reportId = null }) => {
  const {
    role: [role],
  } = useContext(UIStateContext);
  const { selectedAccount } = useAccountStore();
  const { userRole } = useRoleStore();
  const { data: accountSettings, isFetched: isFetchedAccountSettings } =
    API.getBasicQuery(`accounts/settings`);
  const mode = selectedAccount?.accountMode;
  const [searchParams, _] = useSearchParams();

  const statements = new Statements(mode, role, userRole, {
    account_id: selectedAccount?.accountId,
    statement_id: searchParams.get('id'),
  });

  const [showReconcile, setShowReconcile] = useState(false);
  const exportOptions = useExportOptions('commissions-report');
  const [showGrouping, setShowGrouping] = useState(false);
  const [showUngrouping, setShowUngrouping] = useState(false);
  const { setLoadingConfig } = useContext(LoadingContext);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [selectedStatment, setSelectedStatment] = useState<any | null>(null);
  const { workerSyncedFields } = useSyncedFieldsNew();
  const syncService = API.getMutation('data_processing/sync', 'POST');
  const { getAdditionalActions } = useAdditionalActions();
  const { showSnackbar } = useSnackbar();
  const [selectedData, setSelectedData] = useState<
    { id: number; policy_id: string }[]
  >([]);
  const { data: accountInfo } = API.getBasicQuery('accounts');
  const [filterCacheKey, setFilterCacheKey] = useState<readonly unknown[]>([]);
  const queryClient = useQueryClient();
  const isMobile = useMediaQuery('(max-width:600px)');
  const { handleCreatePolicy } = useCreatePolicy();

  // Cache key management effect
  useEffect(() => {
    // Cancel previous query when searchParams or selectedAccount changes, avoid previous query from returning stale data and causing UI mismatch
    setFilterCacheKey((prev) => {
      const cacheKey = [
        selectedAccount?.accountId,
        // Filter out pagination & order params from searchParams
        new URLSearchParams(
          Array.from(searchParams.entries()).filter(
            ([key]) => !['limit', 'page', 'sort', 'orderBy'].includes(key)
          )
        ).toString(),
      ];
      if (prev?.length > 0 && prev.join() !== cacheKey.join()) {
        queryClient.cancelQueries({ queryKey: prev });
      }
      return cacheKey;
    });
  }, [queryClient, searchParams, selectedAccount?.accountId]);

  const { data: filters } = useQuery({
    queryKey: filterCacheKey,
    queryFn: async () => {
      const url = `${getEnvVariable('API')}/api/statement_data/filters_paginated`;
      const res = await fetch(url, {
        method: 'GET',
        headers: await API.getHeaders(),
      });
      return res.json();
    },
  });

  // Derived state and computed values
  const hasAgencyIntegratorWorker =
    !!workerSyncedFields?.[WorkerNames.AgencyIntegratorWorker];

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const onBulkSync = async (params: any) => {
    try {
      let result = null;
      if (hasAgencyIntegratorWorker) {
        const lookupData = new Map(selectedData.map((item) => [item.id, item]));
        const policyNumbers = params.ids.map(
          (id) => lookupData.get(id)?.policy_id
        );
        if (!policyNumbers?.length) {
          return;
        }
        result = await syncService.mutateAsync({
          entities: ['policies'],
          policyNumbers: policyNumbers,
          sync: true,
          worker: WorkerNames.AgencyIntegratorWorker,
        } as SyncParamsDTO);
      }
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if (!(result as any)?.success) {
        showSnackbar(
          `Sync failed: ${
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            (result as any)?.message
          }`,
          'error'
        );
      } else {
        showSnackbar('Sync successfully', 'success');
      }
    } catch (error) {
      console.error(error);
      showSnackbar('Sync failed', 'error');
    }
  };

  statements.actions = useMemo(
    () => [
      ...(statements.actions || []),
      ...getAdditionalActions({
        setSelectedStatment,
        setShowReconcile,
      }),
    ],
    [getAdditionalActions, statements.actions]
  );

  if (accountInfo?.comp_grids_enabled) {
    statements.actions?.push({
      id: 'view_comp_grid_rates',
      label: 'View comp grid rates',
      onClick: (row) => {
        const queryParams = new URLSearchParams();
        if (row?.product_name || row?.report?.product_name) {
          queryParams.append(
            'q',
            row?.product_name ?? row?.report?.product_name
          );
        }

        if (row?.effective_date || row?.report?.effective_date) {
          queryParams.append(
            'effective_date',
            row.effective_date ?? row?.report?.effective_date
          );
        }

        const url = `${window.location.origin}/schedules/comp-grids/viewer?${queryParams.toString()}`;
        window.open(url, '_blank');
      },
    });
  }

  const pageSettingFields = isMobile
    ? accountSettings?.pages_settings?.commissions?.outstandingMobileFields
    : accountSettings?.pages_settings?.commissions?.fields;

  const newFields = (pageSettingFields ?? []).reduce((acc, cur) => {
    acc[cur] = {
      ...statements.fields[cur],
    };
    if (
      role === SystemRoles.ADMIN &&
      selectedAccount?.accountId === AccountIds.TRANSGLOBAL
    ) {
      if (cur === 'agent_commissions_log') {
        acc.expected_result = statements.fields.expected_result;
      }
    }
    return acc;
  }, {});

  if (Object.keys(newFields)?.length > 0) statements.fields = newFields;

  statements.dateFilters = statements.dateFilters.filter((filter) => {
    return pageSettingFields?.includes(filter.filterFieldId);
  });

  if (accountSettings?.pages_settings?.commissions?.page_label) {
    statements.label = accountSettings?.pages_settings?.commissions?.page_label;
  }

  if (
    isFetchedAccountSettings &&
    accountSettings?.pages_settings?.commissions?.show_page === false
  ) {
    return (
      // TODO: Remove navigate after figuring out how to handle this in router
      <Navigate to="/policies" />
    );
  }

  const BulkReconcile = ({ key, isWorkingToolbar }) => {
    return (
      <Button
        loading={isWorkingToolbar}
        key={key}
        onClick={() => {
          setShowReconcile(true);
        }}
        color="primary"
        sx={{ minWidth: 'unset', ml: 0.5 }}
      >
        <Tooltip title="Bulk reconciliations">
          <AddLink />
        </Tooltip>
      </Button>
    );
  };

  const BulkGroupDedupe = ({ key, isWorkingToolbar }) => {
    return (
      <Button
        loading={isWorkingToolbar}
        key={key}
        onClick={() => setShowGrouping(true)}
        sx={{ minWidth: 'unset', ml: 0.5 }}
      >
        <Tooltip title="Bulk group records">
          <MergeOutlined />
        </Tooltip>
      </Button>
    );
  };

  const BulkUngroup = ({ key, isWorkingToolbar }) => {
    return (
      <Button
        loading={isWorkingToolbar}
        key={key}
        onClick={() => setShowUngrouping(true)}
        color="primary"
        sx={{ minWidth: 'unset', ml: 0.5 }}
      >
        <Tooltip title="Ungroup records">
          <CallSplitOutlined />
        </Tooltip>
      </Button>
    );
  };

  const BulkCompCalc = ({ key }) => {
    return (
      <Button
        onClick={async () => {
          try {
            const commissionIds = selectedStatment
              ? [selectedStatment.id]
              : selectedData.map((r) => r.id);
            setLoadingConfig({
              loading: true,
              message: 'Calculating comp...',
            });

            const response = await axios.post(
              `${getEnvVariable('API')}/api/data_processing/commissions/agents`,
              {
                isSync: true,
                useGroupedCommissions: true,
                ids: commissionIds,
              }
            );

            const data = response.data;
            if (response.status === 200 && data.statusText) {
              showSnackbar('Comp calc successfully', 'success');
              queryClient.invalidateQueries({ queryKey: filterCacheKey });
            } else {
              showSnackbar(
                `Comp calc failed: ${
                  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  (data as any)?.message
                }`,
                'error'
              );
            }
          } catch (error) {
            console.error(error);
            showSnackbar('Comp calc failed', 'error');
          } finally {
            setLoadingConfig({
              loading: false,
              message: '',
            });
          }
        }}
        color="primary"
        key={key}
      >
        <Tooltip title="Calculate comp">
          <CalculateOutlined />
        </Tooltip>
      </Button>
    );
  };

  const selectedItems = selectedData.filter(Boolean);
  return selectedAccount && isFetchedAccountSettings ? (
    <ReconciliationConfirmProvider mode={mode}>
      {selectedStatment || selectedItems?.length ? (
        <>
          <ReconcileEdit
            open={showReconcile}
            onClose={() => {
              setShowReconcile(false);
              setSelectedStatment(null);
            }}
            statementIds={
              selectedStatment
                ? [selectedStatment.id]
                : selectedItems.map((r) => r?.id)
            }
          />
          <GroupingEdit
            open={showGrouping}
            onClose={() => setShowGrouping(false)}
            statements={selectedItems}
            refetch={() =>
              queryClient.invalidateQueries({ queryKey: filterCacheKey })
            }
          />
          <UngroupingEdit
            open={showUngrouping}
            onClose={() => setShowUngrouping(false)}
            statements={selectedItems}
            refetch={() =>
              queryClient.invalidateQueries({ queryKey: filterCacheKey })
            }
          />
        </>
      ) : null}
      <EnhancedDataView
        enableRowCreateCopyAction
        enableRowDeleteAction
        reportId={reportId}
        dataSpec={statements}
        bulkAdd
        enableBulkEditCsv
        // TODO: Refactor type after EnhancedDataView is refactored
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        actions={statements.actions as any}
        actionsEnabled={() => true}
        filters={filters}
        // TODO: Refactor type after EnhancedDataView is refactored
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        exportOptions={exportOptions as any}
        outstandingMobileFields={
          accountSettings?.pages_settings?.commissions?.outstandingMobileFields
        }
        setSelectedData={setSelectedData}
        onBulkSync={
          (hasAgencyIntegratorWorker
            ? (ids) => onBulkSync({ ids })
            : // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              undefined) as any
        }
        bulkActions={
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          [BulkReconcile, BulkCompCalc, BulkGroupDedupe, BulkUngroup] as any
        }
        enableSaves
        showTotals
        // TODO (frank.santillan): Move to settings after we migrate reconciliation / commissions / policies to pages_settings.
        readOnly={userRole === Roles.PRODUCER}
        // TODO: Refactor type after EnhancedDataView is refactored
        extraFormActions={
          [
            {
              label: 'Create policy',
              onClick: handleCreatePolicy,
              variant: 'text',
              showInEdit: true, // Only show in edit mode
            },
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          ] as any
        }
      />
    </ReconciliationConfirmProvider>
  ) : null;
};

export default CommissionsDataView;
