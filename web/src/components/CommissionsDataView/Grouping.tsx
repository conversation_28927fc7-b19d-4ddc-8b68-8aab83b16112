import { getEnvVariable } from '@/env';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogTitle,
  <PERSON>alog<PERSON>ctions,
  Button,
  Autocomplete,
  TextField,
  CircularProgress,
  Typography,
  FormControlLabel,
  Alert,
  Checkbox,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment,
} from '@mui/material';
import { useEffect, useState } from 'react';
import { GroupingDTOSchema } from 'common/dto/data_processing/grouping';
import {
  GroupingCalculationMethod,
  GroupingCalculationMethodDescription,
} from 'common/dto/data_processing/interfaces';

import API from '@/services/API';
import useSnackbar from '@/contexts/useSnackbar';
import StatementCard from './StatementCard';

interface GroupingEditProps {
  open: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  statements: any[];
  onClose: () => void;
  refetch?: () => void;
}

interface StatementProps {
  id: number;
  children_data?: { id: number; policy_id: string }[];
  policy_id: string;
  customer_name: string;
  agent_name: string;
  effective_date: string;
  processing_date: string;
  invoice_date: string;
  payment_date: string;
  state: string;
  compensation_type: string;
}
export default function GroupingEdit({
  open,
  statements,
  onClose,
  refetch,
}: GroupingEditProps) {
  const { showSnackbar } = useSnackbar();
  const [useVirtualOption, setUseVirtualOption] = useState(true);
  const [calculationMethod, setCalculationMethod] =
    useState<GroupingCalculationMethod>(
      GroupingCalculationMethod.GROUP_BY_TOTAL_PREMIUM
    );
  const [advancedRatePercent, setAdvancedRatePercent] = useState<number>(100);
  const [selected, setSelected] = useState<
    | ({
        label: string;
      } & StatementProps)
    | null
  >(null);
  const [options, setOptions] = useState<
    ({ label: string } & StatementProps)[]
  >([]);
  const [loading, setLoading] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);

  const handleGroup = async () => {
    setConfirmOpen(false);
    try {
      setLoading(true);
      const requestData = GroupingDTOSchema.parse({
        isSync: true,
        isManual: true,
        statementIds: statements.map((s) => s.id),
        masterId: selected?.id,
        actions: ['group'],
        useVirtualRecords: useVirtualOption,
        calculationMethod,
        advancedRatePercent: useVirtualOption ? advancedRatePercent : undefined,
      });
      const res = await fetch(
        `${getEnvVariable('API')}/api/data_processing/grouping`,
        {
          method: 'POST',
          headers: await API.getHeaders(),
          body: JSON.stringify(requestData),
        }
      );
      await res.json();
      if (res.status === 200) {
        showSnackbar('Grouping successful', 'success');
      }
      if (refetch) {
        refetch();
      }
      handleClose();
    } catch (_error) {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if ((_error as any).name === 'ZodError') {
        showSnackbar(
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          Object.entries((_error as any).flatten().fieldErrors)
            .map(([_key, value]) => `${value}`)
            .join('\n'),
          'error'
        );
      } else {
        showSnackbar('Error grouping records', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    setSelected(null);
    setConfirmOpen(false);
    setAdvancedRatePercent(100);
  };

  useEffect(() => {
    setOptions(
      statements?.map((r) => ({
        id: r.id,
        label: `PolicyNumber: ${r.policy_id} - EffDate: ${r.effective_date}`,
        str_id: r.str_id,
        ...r,
      })) ?? []
    );
  }, [statements]);

  const recordsWithChildren = statements.filter(
    (r) => r.id !== selected?.id && r.children_data?.length > 0
  );

  return (
    <>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Group records</DialogTitle>
        <div />
        <DialogContent
          sx={{
            width: '400px',
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            alignItems: 'stretch',
          }}
        >
          {!useVirtualOption && (
            <Autocomplete
              value={selected}
              onChange={(_e, v) => setSelected(v)}
              options={options}
              renderInput={(params) => (
                <TextField {...params} label="Select the primary record" />
              )}
              renderOption={(props, option) => {
                const { key: _key, ...optionProps } = props;
                return (
                  <StatementCard
                    key={option.id}
                    statement={option}
                    {...optionProps}
                  />
                );
              }}
            />
          )}
          <FormControlLabel
            control={
              <Checkbox
                checked={useVirtualOption}
                onChange={() => setUseVirtualOption(!useVirtualOption)}
              />
            }
            label="Use virtual records"
          />
          {useVirtualOption && (
            <>
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel id="calculation-method-label">
                  Calculation method
                </InputLabel>
                <Select
                  labelId="calculation-method-label"
                  value={calculationMethod}
                  label="Calculation method"
                  onChange={(e) =>
                    setCalculationMethod(
                      e.target.value as GroupingCalculationMethod
                    )
                  }
                >
                  {Object.values(GroupingCalculationMethod).map((method) => (
                    <MenuItem key={method} value={method}>
                      {GroupingCalculationMethodDescription.get(method)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              <TextField
                fullWidth
                sx={{ mt: 2 }}
                type="number"
                label="Advanced rate"
                value={advancedRatePercent}
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  if (!isNaN(value) && value >= 0 && value <= 100) {
                    setAdvancedRatePercent(value);
                  }
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">%</InputAdornment>
                  ),
                }}
                inputProps={{
                  min: 0,
                  max: 100,
                  step: 0.1,
                }}
                helperText="Default: 100%. For advance payments, enter the advance rate (e.g., 75 for 75% advance)"
              />
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={() => setConfirmOpen(true)}
            disabled={loading || (!useVirtualOption && !selected)}
            variant="contained"
          >
            {loading ? <CircularProgress size={24} /> : 'Group'}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
        <DialogTitle>Confirm grouping</DialogTitle>
        <DialogContent>
          <Typography>
            {useVirtualOption
              ? 'Are you sure you want to group the selected statements using a virtual record?'
              : `Are you sure you want to group the selected statements with master statement (Policy: ${selected?.policy_id})?`}
          </Typography>
          <div style={{ height: 10 }} />
          {selected && !useVirtualOption && (
            <StatementCard statement={selected} />
          )}
          <div style={{ height: 10 }} />
          {recordsWithChildren.length > 0 &&
            recordsWithChildren.map((r) => (
              // biome-ignore lint/correctness/useJsxKeyInIterable: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography>
                  Policy {r.policy_id} is a primary record for an existing group
                  of commissions. Grouping it as a linked record
                  {!useVirtualOption && ` of Policy ${selected?.policy_id}`}{' '}
                  will unset any existing linked commissions for policy{' '}
                  {r.policy_id}.
                </Typography>
              </Alert>
            ))}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmOpen(false)}>Cancel</Button>
          <Button onClick={handleGroup} color="primary" variant="contained">
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
