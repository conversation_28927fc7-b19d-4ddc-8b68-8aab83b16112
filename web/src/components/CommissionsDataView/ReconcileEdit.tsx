import { getEnvVariable } from '@/env';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  DialogActions,
  Button,
  Autocomplete,
  TextField,
  type AutocompleteInputChangeReason,
  CircularProgress,
  Typography,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { useEffect, useState } from 'react';

import API from '@/services/API';
import useSnackbar from '@/contexts/useSnackbar';
import PolicyCard from './PolicyCard';

export default function ReconcileEdit({
  open,
  statementIds,
  onClose,
}: {
  open: boolean;
  statementIds?: number[];
  onClose: () => void;
}) {
  const { showSnackbar } = useSnackbar();
  const [selected, setSelected] = useState<{
    label: string;
    id: number;
    policy_id: number;
    customer_name: string;
    agent_name: string;
    effective_date: string;
    product_type: string;
    state: string;
    writing_carrier_name: string;
    assign_one_agent?: boolean;
  } | null>(null);
  const [options, setOptions] = useState<
    {
      label: string;
      id: number;
      policy_id: number;
      product_type: string;
      customer_name: string;
      agent_name: string;
      state: string;
      writing_carrier_name: string;
      effective_date: string;
    }[]
  >([]);
  const [searchQuery, setSearchQuery] = useState('');
  const { data, abort } = API.getBasicQuery(
    `report_data`,
    `q=${searchQuery}&incl_linked=true&limit=100`
  );

  const [loading, setLoading] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);

  const reconcile = async () => {
    setConfirmOpen(false);
    try {
      setLoading(true);
      const res = await fetch(
        `${getEnvVariable('API')}/api/reconciliation/reconcile`,
        {
          method: 'POST',
          headers: await API.getHeaders(),
          body: JSON.stringify({
            policyId: selected?.id,
            isSync: true,
            isManual: true,
            statementIds: statementIds,
            assignOneAgent: selected?.assign_one_agent || false,
          }),
        }
      );
      const data = await res.json();
      if ((res.status === 200 && data.statusText) || res.status >= 400) {
        showSnackbar(data.message || data.statusText, 'error');
      }

      handleClose();

      console.log('processReconciliation: ', data);
    } catch {
      showSnackbar('Error reconciling', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    setSelected(null);
    setSearchQuery('');
    setConfirmOpen(false);
  };

  useEffect(() => {
    setOptions(
      data?.data?.map((r) => ({
        id: r.id,
        label: `PolicyNumber: ${r.policy_id} - EffDate: ${r.effective_date}`,
        str_id: r.str_id,
        ...r,
      })) ?? []
    );
  }, [data]);

  const handleInputChange = (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    _e: any,
    v: string,
    _reason: AutocompleteInputChangeReason
  ) => {
    setSearchQuery((prev) => {
      if (prev !== v) {
        abort();
      }
      return v;
    });
  };

  return (
    <>
      <Dialog open={open} onClose={onClose}>
        <DialogTitle>Manual reconcile</DialogTitle>
        <div />
        <DialogContent
          sx={{
            width: '400px',
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            alignItems: 'stretch',
          }}
        >
          <Autocomplete
            onChange={(_e, v) => setSelected(v)}
            options={options}
            onInputChange={handleInputChange}
            renderInput={(params) => (
              <TextField {...params} label="Search for policy" />
            )}
            renderOption={(props, option) => {
              const { key: _key, ...optionProps } = props;
              return (
                <PolicyCard key={option.id} policy={option} {...optionProps} />
              );
            }}
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={selected?.assign_one_agent || false}
                onChange={(e) => {
                  if (selected) {
                    setSelected({
                      ...selected,
                      assign_one_agent: e.target.checked,
                    });
                  }
                }}
              />
            }
            label="Only assign one agent (best match)"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={() => setConfirmOpen(true)}
            disabled={loading || !selected}
            variant="contained"
          >
            {loading ? <CircularProgress size={24} /> : 'Reconcile'}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
        <DialogTitle>Confirm reconciliation</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to manually reconcile this statement with
            policy number: {selected?.policy_id}?
          </Typography>
          {selected && <PolicyCard policy={selected} />}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmOpen(false)}>Cancel</Button>
          <Button onClick={reconcile} color="primary" variant="contained">
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
