import { getEnvVariable } from '@/env';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  DialogActions,
  Button,
  CircularProgress,
  Typography,
  Alert,
} from '@mui/material';
import { useState } from 'react';
import { UngroupingDTOSchema } from 'common/dto/data_processing/grouping';

import API from '@/services/API';
import useSnackbar from '@/contexts/useSnackbar';
import StatementCard from './StatementCard';

interface UngroupingEditProps {
  open: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  statements: any[];
  onClose: () => void;
  refetch?: () => void;
}

export default function UngroupingEdit({
  open,
  statements,
  onClose,
  refetch,
}: UngroupingEditProps) {
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);

  const handleUngroup = async () => {
    setConfirmOpen(false);
    try {
      setLoading(true);
      const requestData = UngroupingDTOSchema.parse({
        statementIds: statements.map((s) => s.id),
      });
      const res = await fetch(
        `${getEnvVariable('API')}/api/data_processing/grouping/ungroup`,
        {
          method: 'POST',
          headers: await API.getHeaders(),
          body: JSON.stringify(requestData),
        }
      );
      const data = await res.json();
      if (res.status === 200 && data.success) {
        showSnackbar('Records ungrouped successfully', 'success');
        if (refetch) {
          refetch();
        }
      } else {
        showSnackbar(
          `Ungrouping failed: ${data.message || 'Unknown error'}`,
          'error'
        );
      }
      handleClose();
    } catch (_error) {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if ((_error as any).name === 'ZodError') {
        showSnackbar(
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          Object.entries((_error as any).flatten().fieldErrors)
            .map(([_key, value]) => `${value}`)
            .join('\n'),
          'error'
        );
      } else {
        showSnackbar('Error ungrouping records', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    setConfirmOpen(false);
  };

  const parentRecords = statements.filter((r) => r.children_data?.length > 0);

  const groupedRecords = statements.filter((r) => !r.children_data?.length);

  return (
    <>
      <Dialog open={open} onClose={onClose}>
        <DialogTitle>Ungroup records</DialogTitle>
        <DialogContent
          sx={{
            width: '400px',
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
            alignItems: 'stretch',
          }}
        >
          <Typography>
            Are you sure you want to ungroup the selected statements?
          </Typography>
          {statements.length > 0 && (
            <Typography variant="subtitle2">
              Selected statements: {statements.length}
            </Typography>
          )}
          {parentRecords.length > 0 && (
            <Alert severity="info">
              <Typography>
                {parentRecords.length} statement(s) have child records that will
                be ungrouped.
              </Typography>
            </Alert>
          )}
          {groupedRecords.length > 0 && (
            <Alert severity="info">
              <Typography>
                {groupedRecords.length} statement(s) are child records that will
                be ungrouped from their parent.
              </Typography>
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={() => setConfirmOpen(true)}
            disabled={loading || statements.length === 0}
            variant="contained"
          >
            {loading ? <CircularProgress size={24} /> : 'Ungroup'}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
        <DialogTitle>Confirm ungrouping</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to ungroup the selected statements?
          </Typography>
          <div style={{ height: 10 }} />
          {statements.length > 0 && (
            <Typography variant="subtitle2">
              Selected statements: {statements.length}
            </Typography>
          )}
          {parentRecords.length > 0 && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography>
                {parentRecords.length} statement(s) have child records that will
                be ungrouped. This will remove the parent-child relationship
                between these statements.
              </Typography>
            </Alert>
          )}
          {groupedRecords.length > 0 && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography>
                {groupedRecords.length} statement(s) are child records that will
                be ungrouped from their parent. This will remove the
                parent-child relationship between these statements.
              </Typography>
            </Alert>
          )}
          {statements.map((statement) => (
            // biome-ignore lint/correctness/useJsxKeyInIterable: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            <div>
              <StatementCard key={statement.id} statement={statement} />
              <div style={{ height: 10 }} />
            </div>
          ))}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmOpen(false)}>Cancel</Button>
          <Button onClick={handleUngroup} color="primary" variant="contained">
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
