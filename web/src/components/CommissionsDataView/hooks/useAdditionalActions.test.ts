import { renderHook, act } from '@testing-library/react';
import { vi, type Mock } from 'vitest';

import { useAdditionalActions } from './useAdditionalActions';
import * as useSnackbar from '@/contexts/useSnackbar';
import * as API from '@/services/API';
import { useCreatePolicy } from './useCreatePolicy';

const showSnackbarMock = vi.fn();
const mutateAsyncMock = vi.fn();
const handleCreatePolicyMock = vi.fn();

vi.mock('@/contexts/useSnackbar', () => ({
  __esModule: true,
  default: vi.fn(),
}));

vi.mock('@/services/API', () => ({
  __esModule: true,
  default: {
    getMutation: vi.fn(() => ({
      mutateAsync: vi.fn(),
    })),
  },
}));

vi.mock('./useCreatePolicy', () => ({
  useCreatePolicy: vi.fn(),
}));

describe('useAdditionalActions', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (useSnackbar.default as any as Mock).mockReturnValue({
      showSnackbar: showSnackbarMock,
    });

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (API.default.getMutation as any as Mock).mockReturnValue({
      mutateAsync: mutateAsyncMock,
    });

    (useCreatePolicy as Mock).mockReturnValue({
      handleCreatePolicy: handleCreatePolicyMock,
    });
  });

  it('Given useAdditionalActions is called, should return getAdditionalActions function', () => {
    const { result } = renderHook(() => useAdditionalActions());
    expect(typeof result.current.getAdditionalActions).toBe('function');
  });

  it('Given getAdditionalActions is called, should return actions with correct ids', () => {
    const { result } = renderHook(() => useAdditionalActions());
    const actions = result.current.getAdditionalActions({
      setSelectedStatment: vi.fn(),
      setShowReconcile: vi.fn(),
    });
    expect(actions.some((a) => a.id === 'reconcile')).toBe(true);
    expect(actions.some((a) => a.id === 'update_policy_payout_rates')).toBe(
      true
    );
  });

  it('Given statuses are checked, should enable "reconcile" action only for correct statuses', () => {
    const { result } = renderHook(() => useAdditionalActions());
    const actions = result.current.getAdditionalActions({
      setSelectedStatment: vi.fn(),
      setShowReconcile: vi.fn(),
    });
    const reconcile = actions.find((a) => a.id === 'reconcile');
    expect(reconcile.enabled({ agent_commissions_status: 'Pending' })).toBe(
      true
    );
    expect(reconcile.enabled({ agent_commissions_status: 'Paid' })).toBe(false);
    expect(reconcile.enabled({ agent_commissions_status: 'Manual' })).toBe(
      false
    );
  });

  it('Given "reconcile" action is clicked, should call setSelectedStatment and setShowReconcile', () => {
    const setSelectedStatment = vi.fn();
    const setShowReconcile = vi.fn();
    const { result } = renderHook(() => useAdditionalActions());
    const actions = result.current.getAdditionalActions({
      setSelectedStatment,
      setShowReconcile,
    });
    const reconcile = actions.find((a) => a.id === 'reconcile');
    const row = { id: 1 };
    act(() => {
      reconcile.onClick(row);
    });
    expect(setSelectedStatment).toHaveBeenCalledWith(row);
    expect(setShowReconcile).toHaveBeenCalledWith(true);
  });

  it('Given "update_policy_payout_rates" action is clicked, should call mutateAsync and showSnackbar on success', async () => {
    mutateAsyncMock.mockResolvedValueOnce(true);
    const { result } = renderHook(() => useAdditionalActions());
    const actions = result.current.getAdditionalActions({
      setSelectedStatment: vi.fn(),
      setShowReconcile: vi.fn(),
    });
    const update = actions.find((a) => a.id === 'update_policy_payout_rates');
    const row = { id: 123 };
    await act(async () => {
      await update.onClick(row);
    });
    expect(mutateAsyncMock).toHaveBeenCalledWith({ statement_id: 123 });
    expect(showSnackbarMock).toHaveBeenCalledWith(
      'Policy payout rates updated successfully',
      'success'
    );
  });

  it('Given mutateAsync fails, should show error snackbar', async () => {
    mutateAsyncMock.mockRejectedValueOnce(new Error('Failed!'));
    const { result } = renderHook(() => useAdditionalActions());
    const actions = result.current.getAdditionalActions({
      setSelectedStatment: vi.fn(),
      setShowReconcile: vi.fn(),
    });
    const update = actions.find((a) => a.id === 'update_policy_payout_rates');
    const row = { id: 123 };
    await act(async () => {
      await update.onClick(row);
    });
    expect(showSnackbarMock).toHaveBeenCalledWith('Failed!', 'error');
  });

  it('Given mutateAsync fails with non-Error instance, should show error snackbar with stringified error', async () => {
    mutateAsyncMock.mockRejectedValueOnce({ foo: 'bar' });
    const { result } = renderHook(() => useAdditionalActions());
    const actions = result.current.getAdditionalActions({
      setSelectedStatment: vi.fn(),
      setShowReconcile: vi.fn(),
    });
    const update = actions.find((a) => a.id === 'update_policy_payout_rates');
    const row = { id: 123 };
    await act(async () => {
      await update.onClick(row);
    });
    expect(showSnackbarMock).toHaveBeenCalledWith(
      JSON.stringify({ foo: 'bar' }),
      'error'
    );
  });
});
