import { renderHook, act } from '@testing-library/react';
import { useNavigate } from 'react-router-dom';
import { vi, type Mock } from 'vitest';

import { useCreatePolicy } from './useCreatePolicy';
import type { StatementData } from '../types';

const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

describe('useCreatePolicy', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
    (useNavigate as Mock).mockReturnValue(mockNavigate);
  });

  it('Given handleCreatePolicy is called, should navigate with correct state', () => {
    const commissionData: StatementData = {
      agent_name: '<PERSON>',
      customer_name: '<PERSON>',
      contacts: ['123456789'],
      policy_id: 'POL123',
      effective_date: '2024-06-01',
      geo_state: 'CA',
      issue_age: 35,
      notes: 'Some notes',
      payment_mode: 'Annual',
      product_name: 'Life Insurance',
      product_type: 'Term',
      premium_amount: 1000,
      transaction_type: 'New',
      writing_carrier_name: 'CarrierX',
      commissionable_premium_amount: 950,
      agent_commissions_status: 'Pending',
    };

    const { result } = renderHook(() => useCreatePolicy());

    act(() => {
      result.current.handleCreatePolicy(commissionData);
    });

    expect(mockNavigate).toHaveBeenCalledWith('/policies?m=edit', {
      state: {
        mode: 'edit',
        prefillData: {
          agent_name: 'Agent Smith',
          customer_name: 'John Doe',
          contacts: ['123456789'],
          policy_id: 'POL123',
          effective_date: '2024-06-01',
          geo_state: 'CA',
          issue_age: 35,
          notes: 'Some notes',
          payment_mode: 'Annual',
          product_name: 'Life Insurance',
          product_type: 'Term',
          premium_amount: 1000,
          transaction_type: 'New',
          writing_carrier_name: 'CarrierX',
          commissionable_premium_amount: 950,
        },
      },
    });
  });

  it('Given commissionData has missing optional fields, should not throw', () => {
    const commissionData = {
      agent_name: 'Agent Smith',
      customer_name: 'John Doe',
    } as StatementData;

    const { result } = renderHook(() => useCreatePolicy());

    expect(() => {
      act(() => {
        result.current.handleCreatePolicy(commissionData);
      });
    }).not.toThrow();

    expect(mockNavigate).toHaveBeenCalled();
  });
});
