import { Add, Chevron<PERSON>eft, ChevronRight, MoreV<PERSON> } from '@mui/icons-material';
import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Drawer,
  FormControl,
  IconButton,
  InputLabel,
  Menu,
  MenuItem,
  Select,
  Tab,
  Tabs,
  Tooltip,
  Typography,
} from '@mui/material';
import { WidgetGroup } from 'common/constants';
import { dayjs } from 'common/helpers/datetime';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import isNull from 'lodash-es/isNull';
import { useContext, useEffect, useMemo, useState } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
// biome-ignore lint/correctness/noUndeclaredDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import 'react-resizable/css/styles.css';
import { Navigate, useNavigate, useSearchParams } from 'react-router-dom';
import { WidgetAccessTypes } from 'common/dto/widgets';

import LoadingCircle from '@/components/atoms/LoadingCircle';
import './styles.css';
import BasicDatePicker from '@/components/molecules/BasicDatePicker';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { LoadingContext } from '@/contexts/LoadingContext';
import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import UILabels from '@/services/UILabels';
import { useAccountStore, useRoleStore } from '@/store';
import { Roles } from '@/types';
import { isAdminOrDataSpecialist } from '@/utils/isAdminOrDataSpecialist';
import useDashboard from './dashboardHook';
import SubPageCreator from './SubPageCreator';
import WidgetCreator from './WidgetCreator';
import WidgetSelector from './WidgetSelector';
import WidgetWrapper from './WidgetWrapper';
import ViewAsSelectors from '@/components/DashboardView/ViewAsSelectors';
import useHorizontalScrollArrows from '../../hooks/useHorizontalScrollArrows';

dayjs.extend(quarterOfYear);
dayjs.extend(utc);
dayjs.extend(timezone);

const dateRanges = {
  thisWeek: () => ({
    start_date: dayjs().startOf('week').toDate(),
    end_date: dayjs().endOf('week').toDate(),
  }),
  lastWeek: () => ({
    start_date: dayjs().subtract(1, 'week').startOf('week').toDate(),
    end_date: dayjs().subtract(1, 'week').endOf('week').toDate(),
  }),
  thisMonth: () => ({
    start_date: dayjs().startOf('month').add(1, 'day').toDate(),
    end_date: dayjs().endOf('month').toDate(),
  }),
  lastMonth: () => ({
    start_date: dayjs().subtract(1, 'month').startOf('month').toDate(),
    end_date: dayjs().subtract(1, 'month').endOf('month').toDate(),
  }),
  thisQuarter: () => ({
    start_date: dayjs().startOf('quarter').toDate(),
    end_date: dayjs().endOf('quarter').toDate(),
  }),
  lastQuarter: () => ({
    start_date: dayjs().subtract(1, 'quarter').startOf('quarter').toDate(),
    end_date: dayjs().subtract(1, 'quarter').endOf('quarter').toDate(),
  }),
  thisYear: () => ({
    start_date: dayjs().startOf('year').toDate(),
    end_date: dayjs().endOf('year').toDate(),
  }),
  lastYear: () => ({
    start_date: dayjs().subtract(1, 'year').startOf('year').toDate(),
    end_date: dayjs().subtract(1, 'year').endOf('year').toDate(),
  }),
  last7days: () => ({
    start_date: dayjs().subtract(7, 'days').toDate(),
    end_date: dayjs().toDate(),
  }),
  last30days: () => ({
    start_date: dayjs().subtract(30, 'days').toDate(),
    end_date: dayjs().toDate(),
  }),
  last60days: () => ({
    start_date: dayjs().subtract(60, 'days').toDate(),
    end_date: dayjs().toDate(),
  }),
  last90days: () => ({
    start_date: dayjs().subtract(90, 'days').toDate(),
    end_date: dayjs().toDate(),
  }),
  null: () => ({
    start_date: null,
    end_date: null,
  }),
};

const DrawerMode = {
  CREATE_WIDGET: 'CREATE_WIDGET',
  ADD_SHARED_WIDGET: 'ADD_SHARED_WIDGET',
  ADD_NEW_PAGE: 'ADD_NEW_PAGE',
  EDIT_WIDGET: 'EDIT_WIDGET',
};

const filters = {
  agent_group: {
    label: 'Agent group',
    type: 'multi-select',
    field: 'agent_group',
    optionsKey: 'agentGroup',
    filterFunc: (val, filterVal) =>
      filterVal === 'All' ? true : val === filterVal,
  },
  agent: {
    label: 'Agent',
    type: 'multi-select',
    field: 'agent',
    optionsKey: 'agent',
    filterFunc: (val, filterVal) =>
      filterVal === 'All' ? true : val === filterVal,
  },
  policy_status: {
    label: 'Policy status',
    type: 'multi-select',
    optionsKey: 'policyStatus',
    field: 'policy_status',
    filterFunc: (val, filterVal) =>
      filterVal === 'All' ? true : val === filterVal,
  },
  product_type: {
    label: 'Product type',
    type: 'multi-select',
    optionsKey: 'productType',
    field: 'product_type',
    filterFunc: (val, filterVal) =>
      filterVal === 'All' ? true : val === filterVal,
  },
  compensation_type: {
    label: 'Compensation type',
    type: 'multi-select',
    optionsKey: 'compensationType',
    field: 'compensation_type',
    filterFunc: (val, filterVal) =>
      filterVal === 'All' ? true : val === filterVal,
  },
  tag: {
    label: 'Tag',
    type: 'multi-select',
    optionsKey: 'tag',
    field: 'tag',
    filterFunc: (val, filterVal) =>
      filterVal === 'All' ? true : val === filterVal,
  },
  date_range: {
    label: 'Date range',
    type: 'select',
    options: [
      { label: 'This week', value: 'thisWeek' },
      { label: 'Last week', value: 'lastWeek' },
      { label: 'This month', value: 'thisMonth' },
      { label: 'Last month', value: 'lastMonth' },
      { label: 'This quarter', value: 'thisQuarter' },
      { label: 'Last quarter', value: 'lastQuarter' },
      { label: 'This year', value: 'thisYear' },
      { label: 'Last year', value: 'lastYear' },
      { label: 'Last 7 days', value: 'last7days' },
      { label: 'Last 30 days', value: 'last30days' },
      { label: 'Last 60 days', value: 'last60days' },
      { label: 'Last 90 days', value: 'last90days' },
      { label: 'Custom', value: 'custom' },
    ],
    initialValue: 'last60days',
    sx: { width: 140 },
  },
  start_date: {
    label: 'Start date',
    type: 'date',
    field: 'effectiveDate',
    initialValue: dayjs().subtract(60, 'days').toDate(),
    filterFunc: (val, filterVal) => val >= filterVal,
  },
  end_date: {
    label: 'End date',
    type: 'date',
    field: 'effectiveDate',
    initialValue: dayjs().toDate(),
    filterFunc: (val, filterVal) => val <= filterVal,
  },
  include_blanks: {
    label: 'Include blanks',
    type: 'checkbox',
    field: 'include_blanks',
    initialValue: false,
  },
};
const ResponsiveGridLayout = WidthProvider(Responsive);

/**
 * Temporary fix. Its not the recommended approach to fix this lints,
 * we should get more time to type all these functions in the near future.
 *
 * @typedef {[any | null, Function]} UseState
 */

const Dashboard = ({ dashboardLabel, dashboardName }) => {
  const { data: accountSettings, isFetched: isFetchedAccountSettings } =
    API.getBasicQuery(`accounts/settings`);
  const navigate = useNavigate();
  const [sideDrawerMode, setSideDrawerMode] = useState(
    DrawerMode.CREATE_WIDGET
  );
  const [layouts, setLayouts] = useState(null);
  const [showSaveLayoutButton, setShowSaveLayoutButton] = useState(false);
  const { setLoadingConfig } = useContext(LoadingContext);

  const cols = { lg: 8, md: 8, sm: 4, xs: 2, xxs: 2 };
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 };
  const margin = {
    lg: [10, 10],
    md: [10, 10],
    sm: [10, 10],
    xs: [10, 10],
    xxs: [10, 10],
  };
  const sidebarWidth = 500;
  const { userRole } = useRoleStore();
  const isAccountAdmin = userRole === Roles.ACCOUNT_ADMIN;
  const [isEditingMode, setIsEditingMode] = useState(false);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [widgetOnEdit, setWidgetOnEdit] = useState<any | null>(null);
  const [activeView, setActiveView] = useState(userRole);
  const [searchParams, setSearchParams] = useSearchParams();
  let query = searchParams.toString();

  const viewSettings = accountSettings?.pages_settings?.insights;
  const createWidgetPoster = API.getMutation('insights/preview', 'POST');
  const saveWidgetSettingPoster = API.getMutation(
    'insights/accountWidgetsSettings',
    'PUT'
  );
  const saveWidgetLayoutPoster = API.getMutation('insights/layout', 'POST');
  const saveWidgetPoster = API.getMutation('insights', 'POST');
  const deleter = API.getMutation('insights', 'DELETE');
  const dashboardDeleter = API.getMutation(
    'insights/delete_dashboard',
    'DELETE'
  );
  const updateWidgetPoster = API.getMutation('insights', 'PUT');
  const [anchorEl, setAnchorEl] = useState(null);

  const [
    widgetModel,
    setWidgetModel,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ] = useState<any | null>(null);
  const [deleteConfirmDialogOpen, setDeleteConfirmDialogOpen] = useState(false);
  const { showSnackbar } = useSnackbar();
  const { setPredefinedDashboardName, setPredefinedWidgetSettings } =
    useDashboard();
  const [widgetIdTobeDeleted, setWidgetIdTobeDeleted] = useState(null);

  const [filteredValues, setFilteredValues] = useState({
    agent_group: [],
    agent: [],
    compensation_type: [],
    tag: [],
    date_range: 'last60days',
    end_date: null,
    policy_status: [],
    product_type: [],
    start_date: null,
    include_blanks: false,
  });

  // Load the filtered values from the url query string
  useEffect(() => {
    const filteredValues = {};
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.keys(filters).forEach((key) => {
      if (searchParams.has(key)) {
        if (filters[key].type === 'multi-select') {
          filteredValues[key] = searchParams.getAll(key);
        } else if (filters[key].type === 'checkbox') {
          const value = searchParams.get(key);
          filteredValues[key] = value === 'true';
        } else {
          filteredValues[key] = searchParams.get(key);
        }
      }
    });
    setFilteredValues((prev) => ({ ...prev, ...filteredValues }));
  }, [searchParams]);

  const { selectedAccount } = useAccountStore();

  const [
    insightsData,
    setInsightsData,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ] = useState<any>({});
  const [
    widgetArray,
    setWidgetArray,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ] = useState<any[]>([]);

  const [boxes, setBoxes] = useState([]);
  const [charts, setCharts] = useState([]);
  const [tables, setTables] = useState([]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (!widgetModel || widgetOnEdit) {
      return;
    }
    let layoutPlaceHolder = widgetArray.filter(
      (/** @type {{i: string}} */ item) => item.i === 'new'
    )?.[0];
    if (!layoutPlaceHolder) {
      return;
    }
    const /** @type any[] */ layout = widgetArray.filter(
        (item: { i: string }) => item.i !== 'new'
      );
    layoutPlaceHolder = {
      ...layoutPlaceHolder,
      ...widgetModel,
      i: 'preview',
    };

    layout.push(layoutPlaceHolder);
    setWidgetArray(layout);
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  December/2024
     * MISSED REFs:  'widgetArray' and 'widgetOnEdit'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [widgetModel]);

  const {
    isLoading: isLoadingInsights,
    data: result,
    refetch,
    isFetched: isFetchedInsights,
  } = API.getBasicQuery('insights', query);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (isFetchedInsights) {
      setInsightsData(result);
      const layout = result?.dashboardSettings?.layout
        ? result.dashboardSettings?.layout[activeView || '']
        : null;
      buildWidgetArray(
        [
          ...(result?.boxes ? result.boxes : []),
          ...(result?.charts ? result.charts : []),
          ...(result?.tables ? result.tables : []),
        ],
        layout
      );
    }

    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Jun/2024
     * MISSED REFs:  'activeView', 'buildWidgetArray', and 'isFetchedInsights'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [result]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (isFetchedInsights) {
      if (dashboardName) {
        setBoxes(insightsData?.boxes);
        setCharts(insightsData?.charts);
        setTables(insightsData?.tables);
      } else {
        setBoxes(insightsData?.boxes ?? []);
        setCharts(insightsData?.charts ?? []);
        setTables(insightsData?.tables ?? []);
      }
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Jun/2024
     * MISSED REFs: dashboardName
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFetchedInsights, insightsData]);

  const initAccessRoles = useMemo(() => {
    return (
      insightsData?.accessRoles?.map((item) => ({
        id: +item,
        value: +item,
      })) ?? []
    );
  }, [insightsData]);

  const [sideDrawerOpen, setsideDrawerOpen] = useState(false);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (activeView !== userRole || containsSearchParamInUrl('view_as')) {
      clearAllParamsForUrlAndQuery();
      updateUrlAndQuery('view_as', activeView, true);
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Feb/2024
     * MISSED REFs: 'clearAllParamsForUrlAndQuery', 'containsSearchParamInUrl', 'updateUrlAndQuery', and 'userRole'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeView]);

  const selectedProducer = searchParams.get('agent');

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  let pageLabel = dashboardLabel ?? 'Insights';
  if (viewSettings?.page_label) {
    pageLabel = viewSettings?.page_label;
  }

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (shouldResetDateUrl()) {
      setFilteredValues({
        ...filteredValues,
        ...dateRanges[filteredValues.date_range](),
      });
      const start_date = dateRanges[filteredValues.date_range]().start_date;
      const end_date = dateRanges[filteredValues.date_range]().end_date;
      if (start_date && end_date) {
        updateUrlAndQuery(
          'start_date',
          start_date.toISOString().substring(0, 10),
          true
        );
        updateUrlAndQuery(
          'end_date',
          end_date.toISOString().substring(0, 10),
          true
        );
      }
    }
    /*
     *
     *
     * https://linear.app/fintary/issue/PLT-2289
     *
     * MISSED REFs:  Check the debounce function passed here
     * WARN SINCE:  March/2025 ****
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filteredValues.date_range]);

  if (isFetchedAccountSettings && viewSettings?.show_page === false) {
    return <Navigate to="/settings" />;
  }

  const handleFilterValueChange = (key, optionsKey, values) => {
    clearUrlAndQuery(key);

    if (values.length === insightsData.filterValues?.[optionsKey].length) {
      setFilteredValues({
        ...filteredValues,
        [key]: [],
      });
      return;
    }

    setFilteredValues({
      ...filteredValues,
      [key]: values,
    });

    const deselectedValues = insightsData?.filterValues?.[optionsKey]?.filter(
      (value) => {
        return !values.some(
          (selectedValue) =>
            selectedValue.id === value.id || selectedValue === value
        );
      }
    );

    if (key === 'agent' && values.length > 50) {
      clearUrlAndQuery(key);
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      deselectedValues.forEach((value) => {
        let valueToUpdate = value;
        if (typeof value === 'object') {
          valueToUpdate = value.id;
        }
        updateUrlAndQuery('unselected_agent', valueToUpdate);
      });
    } else {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      values.forEach((value) => {
        let valueToUpdate = value;
        if (typeof value === 'object') {
          valueToUpdate = value.id;
        }
        updateUrlAndQuery(key, valueToUpdate);
      });
    }
  };

  const handleSelectChange = (key, value) => {
    setFilteredValues({
      ...filteredValues,
      [key]: value,
    });
    updateUrlAndQuery(key, value, true);
  };

  const updateUrlAndQuery = (key, value, replace = false) => {
    updateUrl(key, value, replace);
    updateQuery(key, value, replace);
  };

  const containsSearchParamInUrl = (key) => {
    return searchParams.has(key);
  };

  const clearAllParamsForUrlAndQuery = () => {
    setSearchParams((prev) => {
      // Clear all the search params
      prev.forEach((_value, key) => {
        prev.delete(key);
      });
      return prev;
    });
    query = '';
  };

  const clearUrlAndQuery = (key) => {
    setSearchParams((prev) => {
      prev.delete(key);
      return prev;
    });
    query = query.replace(new RegExp(`${key}=[^&]*&?`), '');
  };
  const updateUrl = (key, value, replace = false) => {
    const values = searchParams.getAll(key);
    if (replace) {
      if (searchParams.has(key) && searchParams.get(key) !== value) {
        setSearchParams((prev) => {
          prev.set(key, value);
          return prev;
        });
        return;
      }
    }
    if (values?.some((val) => val === value)) {
      return;
    }
    setSearchParams((prev) => {
      prev.append(key, value);
      return prev;
    });
  };

  const updateQuery = (key, value, replace = false) => {
    if (replace) {
      query = query.replace(new RegExp(`${key}=[^&]*`), `${key}=${value}`);
      return;
    }
    if (query.includes(`${key}=${value}`)) {
      return;
    }
    query = query.concat(`&${key}=${value}`);
  };

  const ScrollableFilters = () => {
    const {
      refContainer,
      refLeftIcon,
      refRightIcon,
      isScrollable,
      scrollFilter,
    } = useHorizontalScrollArrows();

    return (
      <Box sx={{ position: 'relative', overflow: 'hidden' }}>
        <IconButton
          ref={refLeftIcon}
          onClick={() => scrollFilter('left')}
          disabled={!isScrollable}
          sx={{
            display: isScrollable ? 'flex' : 'none',
            position: 'absolute',
            left: 0,
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 2,
            background: '#fff',
            boxShadow: 1,
          }}
        >
          <ChevronLeft />
        </IconButton>
        <Box
          ref={refContainer}
          sx={{
            display: 'flex',
            overflowX: 'hidden',
            alignItems: 'center',
            padding: '10px 40px',
            width: '100%',
          }}
        >
          {/** biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX-- */}
          {Object.entries(filteredFilters)?.map(([k, v]: [string, any]) => {
            if (v.type === 'date') {
              return (
                <div style={{ width: '100%' }}>
                  <BasicDatePicker
                    label={v.label}
                    key={v.label}
                    value={filteredValues[k]}
                    setValue={(e) => {
                      setFilteredValues({
                        ...filteredValues,
                        [k]: e,
                      });
                      updateUrlAndQuery(k, e, true);
                    }}
                    sx={{ ml: 1, width: 200 }}
                  />
                </div>
              );
            } else if (v.type === 'multi-select') {
              if (!isAdminOrDataSpecialist(userRole)) return null;
              if (
                !insightsData?.filterValues?.[v.optionsKey] ||
                insightsData?.filterValues?.[v.optionsKey].length === 0
              ) {
                return null;
              }
              if (
                (v.optionsKey === 'agentGroup' || v.optionsKey === 'agent') &&
                activeView === Roles.PRODUCER
              ) {
                return null;
              }
              return (
                <EnhancedSelect
                  label={v.label}
                  multiple
                  enableSearch
                  options={insightsData?.filterValues?.[v.optionsKey] ?? []}
                  value={
                    filteredValues[k]?.length
                      ? filteredValues[k]
                      : insightsData.filterValues?.[v.optionsKey]
                  }
                  onChange={(values) =>
                    handleFilterValueChange(k, v.optionsKey, values)
                  }
                  sx={{ width: 135, ml: 1 }}
                  key={v.label}
                />
              );
            } else if (v.type === 'select') {
              return (
                <div key={v.label}>
                  <FormControl
                    sx={{ ml: 1, width: 135, ...v.sx }}
                    key={v.label}
                  >
                    <InputLabel>{v.label}</InputLabel>
                    <Select
                      value={filteredValues[k]}
                      label={v.label}
                      onChange={(e) => handleSelectChange(k, e.target.value)}
                      sx={{
                        '.MuiSelect-select': {
                          py: 0.75,
                          px: 1.5,
                        },
                      }}
                    >
                      {(v.options ?? []).map((option) => (
                        <MenuItem value={option.value} key={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              );
            } else if (v.type === 'checkbox') {
              return (
                <Box
                  className="ml-4 flex flex-col items-center mt-[-16px]"
                  key={v.label}
                >
                  <Box className="text-[12px] whitespace-nowrap text-[#00000099] font-[Roboto]">
                    {v.label}
                  </Box>
                  <Checkbox
                    className="p-0"
                    checked={filteredValues[k]}
                    onChange={(e) => {
                      setFilteredValues({
                        ...filteredValues,
                        [k]: e.target.checked,
                      });
                      updateUrlAndQuery(k, e.target.checked, true);
                    }}
                  />
                </Box>
              );
            }
            return null;
          })}
          <Dialog
            style={{ zIndex: 9999 }}
            open={deleteConfirmDialogOpen}
            onClose={() => setDeleteConfirmDialogOpen(false)}
          >
            <DialogTitle>{'Do you want to delete this widget?'}</DialogTitle>
            <DialogContent>
              <DialogContentText>
                {'This action cannot be undone.'}
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDeleteConfirmDialogOpen(false)}>
                Disagree
              </Button>
              <Button onClick={deleteWidget} autoFocus>
                Agree
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
        <IconButton
          ref={refRightIcon}
          onClick={() => scrollFilter('right')}
          disabled={!isScrollable}
          sx={{
            display: isScrollable ? 'flex' : 'none',
            position: 'absolute',
            right: 0,
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 2,
            background: '#fff',
            boxShadow: 1,
          }}
        >
          <ChevronRight />
        </IconButton>
      </Box>
    );
  };

  const { data: settingsData } = API.getBasicQuery('accounts');

  const mode = selectedAccount?.accountMode;
  const labels = new UILabels(mode);

  if (dashboardName) {
    query = query.concat(`&dashboard=${dashboardName}`);
  }

  if (dashboardName === 'preview') {
    // Add is_preview=true to query string
    query = query.concat('&is_preview=true');
  }

  const createWidget = async (data) => {
    const agent_group = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    filteredValues.agent_group.forEach((val) => {
      agent_group.push(val);
    });
    const compensation_type = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    filteredValues.compensation_type.forEach((val) => {
      compensation_type.push(val);
    });
    const product_type = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    filteredValues.product_type.forEach((val) => {
      product_type.push(val);
    });
    const policy_status = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    filteredValues.policy_status.forEach((val) => {
      policy_status.push(val);
    });

    const isValidDate = (date) => {
      return date && dayjs(date).isValid();
    };

    // biome-ignore lint/suspicious/noImplicitAnyLet: Initial?
    let startDate, endDate;

    if (filteredValues.date_range === 'custom') {
      const validStartDate = isValidDate(filteredValues.start_date);
      const validEndDate = isValidDate(filteredValues.end_date);

      if (!validStartDate || !validEndDate) {
        const invalidDates: string[] = [];
        if (!validStartDate) invalidDates.push('start date');
        if (!validEndDate) invalidDates.push('end date');

        throw new Error(
          `Invalid ${invalidDates.join(' and ')}. Please select valid dates for custom date range`
        );
      }

      startDate = dayjs(filteredValues.start_date).toISOString();
      endDate = dayjs(filteredValues.end_date).toISOString();
    } else {
      const dateRangeKey = filteredValues.date_range || 'last60days';
      const dateRangeFunc = dateRanges[dateRangeKey];

      if (!dateRangeFunc || typeof dateRangeFunc !== 'function') {
        throw new Error(`Invalid date range: ${dateRangeKey}`);
      }

      const { start_date: defaultStartDate, end_date: defaultEndDate } =
        dateRangeFunc();
      startDate = defaultStartDate.toISOString();
      endDate = defaultEndDate.toISOString();
    }
    const response = await createWidgetPoster.mutateAsync({
      agent: filteredValues.agent,
      agent_group: agent_group?.length ? agent_group : undefined,
      compensation_type: compensation_type.length
        ? compensation_type
        : undefined,
      end_date: endDate.substring(0, 10),
      policy_status: policy_status.length ? policy_status : undefined,
      product_type: product_type.length ? product_type : undefined,
      start_date: startDate.substring(0, 10),
      widgetDefinition: {
        ...data,
      },
    });
    return response;
  };

  const saveWidget = async () => {
    if (isNull(widgetModel)) {
      return;
    }
    setLoadingConfig({
      loading: true,
      message: 'Saving widget...',
    });
    // biome-ignore lint/suspicious/noImplicitAnyLet: Initial?
    let response;
    try {
      console.log('widgetModel', widgetModel);
      if (isEditingMode) {
        response = await updateWidgetPoster.mutateAsync({
          id: widgetOnEdit?.id,
          name: widgetModel?.name,
          spec: widgetModel?.spec,
          access: widgetModel?.access,
          accessRoles: widgetModel?.accessRoles,
        });
      } else {
        response = await saveWidgetPoster.mutateAsync({
          name: widgetModel?.name,
          spec: widgetModel?.spec,
          access: widgetModel?.access,
          accessRoles: widgetModel?.accessRoles,
        });
        // Widget settings for the current role
        if (response.id) {
          const dashboardWidgetsAdmin =
            insightsData?.dashboardSettings[Roles.ACCOUNT_ADMIN] ?? [];
          const dashboardWidgetsProducer =
            insightsData?.dashboardSettings[Roles.PRODUCER] ?? [];
          const dashboardWidgetsDataSpecialist =
            insightsData?.dashboardSettings[Roles.DATA_SPECIALIST] ?? [];
          const data = {
            adminWidgetsSettings: dashboardWidgetsAdmin.concat(response.id),
            producerWidgetsSettings: dashboardWidgetsProducer.concat(
              response.id
            ),
            dataSpecialistWidgetsSettings:
              dashboardWidgetsDataSpecialist.concat(response.id),
            dashboardName,
          };
          try {
            await saveWidgetSettingPoster.mutateAsync(data);
          } catch (error) {
            showSnackbar(`Error save widget setting :${error}`, 'error');
          }
        }
      }
      refetch();
    } catch (e) {
      showSnackbar(`Failed to save widget: ${(e as Error).message}`, 'error');
      setLoadingConfig({
        loading: false,
      });
      return;
    }
    setLoadingConfig({
      loading: false,
    });
  };

  const saveLayout = async () => {
    setLoadingConfig({
      loading: true,
      message: 'Saving layout...',
    });
    try {
      await saveWidgetLayoutPoster.mutateAsync({
        layout: layouts,
        dashboardId: insightsData?.dashboardId,
        roleId: activeView ?? userRole,
      });
      setShowSaveLayoutButton(false);
    } catch (/** @type any */ e) {
      showSnackbar(`Failed to save layout: $${(e as Error).message}`, 'error');
    }

    setLoadingConfig({
      loading: false,
    });
  };

  const buildWidgetArray = (data, layout) => {
    const currentBreakpoint = Object.keys(breakpoints).find((breakpoint) => {
      return window.innerWidth > breakpoints[breakpoint];
    });

    const totalCols = currentBreakpoint && cols[currentBreakpoint];
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const tempArray: any[] = [];
    let boxIndex = 0;
    let chartTableIndex = 0;
    data.map((item) => {
      const arrayItem = {
        ...item,
        i: `${item.id}`,
      };
      if (item.widgetGroup === WidgetGroup.BOX) {
        arrayItem.w = 2;
        arrayItem.h = 1;
        arrayItem.x = (boxIndex * 2) % totalCols;
        arrayItem.y = Math.floor((boxIndex * 2) / totalCols);
        boxIndex++;
      } else {
        arrayItem.w = 4;
        arrayItem.h = 2;
        arrayItem.x = (chartTableIndex * 4) % totalCols;
        arrayItem.y = Math.floor(
          (boxIndex * 4 + chartTableIndex * 4) / totalCols
        );
        chartTableIndex++;
      }

      tempArray.push(arrayItem);
    });
    if (layout) {
      layout?.map((position) => {
        tempArray.map((data) => {
          if (data.i === position.i) {
            data.x = position.x;
            data.y = position.y;
            data.w = position.w;
            data.h = position.h;
          }
        });
      });
    }
    const lastX = tempArray.sort((a, b) => b.x - a.x)[0];
    const lastY = tempArray.sort((a, b) => b.y - a.y)[0];
    tempArray.push({
      i: 'new',
      x: (lastX ? lastX.x + lastX.w : 0) % totalCols,
      y: (lastY ? lastY.y : 0) + 1,
      w: 2,
      h: 1,
    });
    setWidgetArray(tempArray);
  };
  const handleModify = (currentLayout) => {
    setLayouts(currentLayout);
  };

  const onShowSaveLayoutButton = () => {
    setShowSaveLayoutButton(true);
  };

  const shouldResetDateUrl = () => {
    if (filteredValues.date_range === 'custom') {
      return false;
    }
    if (
      filteredValues.date_range === 'last60days' &&
      !containsSearchParamInUrl('start_date') &&
      !containsSearchParamInUrl('end_date')
    ) {
      return false;
    }
    return true;
  };

  const isLoading = isLoadingInsights;

  const filteredFilters = Object.fromEntries(
    Object.entries(filters).filter(
      ([k, _v]) =>
        !['start_date', 'end_date'].includes(k) ||
        filteredValues.date_range === 'custom'
    )
  );

  const openAddWidgetDialog = (mode) => {
    setSideDrawerMode(mode);
    setIsEditingMode(false);
    setWidgetOnEdit(null);
    setsideDrawerOpen(true);
  };

  const copyExistingWidget = (widget) => {
    setWidgetOnEdit({ ...widget, shared: widget.access === 'global' });
    setSideDrawerMode(DrawerMode.CREATE_WIDGET);
    setIsEditingMode(false);
    setsideDrawerOpen(true);
  };

  const editWidget = (id) => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const widget: any = [...boxes, ...charts, ...tables].find(
      (e: { id: string }) => e?.id === id
    );
    setWidgetOnEdit({
      ...widget?.spec,
      id: widget?.id,
      spec: widget?.spec,
      shared: widget?.access === 'global',
    });
    setIsEditingMode(true);
    setSideDrawerMode(DrawerMode.EDIT_WIDGET);
    setsideDrawerOpen(true);
  };

  const renderDrawerContent = (mode) => {
    switch (mode) {
      case DrawerMode.CREATE_WIDGET:
      case DrawerMode.EDIT_WIDGET:
        return (
          <WidgetCreator
            widgetOnEdit={widgetOnEdit}
            closeAddWidgetDialog={closeAddWidgetDialog}
            saveChange={saveWidget}
            createWidget={createWidget}
            setWidgetModel={setWidgetModel}
            isEditingMode={isEditingMode}
          />
        );
      case DrawerMode.ADD_SHARED_WIDGET:
        return (
          <WidgetSelector
            sharedWidgets={insightsData?.sharedWidgets}
            refetch={refetch}
            dashboardSettings={insightsData?.dashboardSettings}
            closeAddWidgetDialog={closeAddWidgetDialog}
            isEditPage
            initDashboardLabel={insightsData?.dashboardLabel}
            initAccessRoles={initAccessRoles}
            dashboardId={insightsData?.dashboardId}
          />
        );
      case DrawerMode.ADD_NEW_PAGE:
        return (
          <SubPageCreator
            closeAddWidgetDialog={closeAddWidgetDialog}
            sharedWidgets={insightsData?.sharedWidgets}
          />
        );
      default:
        return null;
    }
  };

  const cloneDashboard = () => {
    setPredefinedDashboardName(`${dashboardName} (copy)`);
    setPredefinedWidgetSettings(insightsData?.dashboardSettings);
    setsideDrawerOpen(true);
    setSideDrawerMode(DrawerMode.ADD_NEW_PAGE);
    handleMenuClose();
  };

  const deleteDashboard = async () => {
    setLoadingConfig({
      loading: true,
      message: 'Deleting dashboard...',
    });
    try {
      const res = await dashboardDeleter.mutateAsync({
        dashboardName: dashboardName,
        dashboardId: insightsData?.dashboardId,
      });
      if (res.error) {
        showSnackbar('Failed to delete dashboard', 'error');
        return;
      }
      navigate('/insights');
    } catch (e) {
      showSnackbar(
        `Failed to delete dashboard: ${(e as Error).message}`,
        'error'
      );
    } finally {
      setLoadingConfig({
        loading: false,
      });
    }
  };

  const deleteWidget = async () => {
    setDeleteConfirmDialogOpen(false);
    setLoadingConfig({
      loading: true,
      message: 'Deleting widget...',
    });
    try {
      const res = await deleter.mutateAsync({ id: widgetIdTobeDeleted });
      if (res.error) {
        showSnackbar('Failed to delete widget', 'error');
        return;
      }
    } catch (e) {
      showSnackbar(`Failed to delete widget: ${(e as Error).message}`, 'error');
    } finally {
      setLoadingConfig({
        loading: false,
      });
    }

    setBoxes((prev) =>
      prev.filter((e: { id?: string }) => e?.id !== widgetIdTobeDeleted)
    );
    setCharts((prev) =>
      prev.filter((e: { id?: string }) => e?.id !== widgetIdTobeDeleted)
    );
    setTables((prev) =>
      prev.filter((e: { id?: string }) => e?.id !== widgetIdTobeDeleted)
    );
    setDeleteConfirmDialogOpen(false);
    refetch();
  };

  const closeAddWidgetDialog = () => {
    setsideDrawerOpen(false);
  };

  return (
    <Box p={2} sx={{ width: '100%', overflowY: 'scroll' }}>
      <Drawer
        anchor="right"
        variant="persistent"
        open={sideDrawerOpen}
        onClose={closeAddWidgetDialog}
        sx={{
          '& .MuiDrawer-paper': {
            marginTop: '64px',
            maxWidth: sidebarWidth,
          },
        }}
      >
        {/* Action bar to close the drawer and save the widget */}

        {isEditingMode ? (
          <Tabs
            value={sideDrawerMode}
            onChange={(_e, value) => {
              setSideDrawerMode(value);
            }}
          >
            <Tab label="Edit widget" value={DrawerMode.EDIT_WIDGET} />
          </Tabs>
        ) : (
          <Tabs
            value={sideDrawerMode}
            onChange={(_e, value) => {
              setSideDrawerMode(value);
            }}
          >
            <Tab label="Create widget" value={DrawerMode.CREATE_WIDGET} />
            <Tab label="Edit page" value={DrawerMode.ADD_SHARED_WIDGET} />
            <Tab label="Add new page" value={DrawerMode.ADD_NEW_PAGE} />
          </Tabs>
        )}
        {renderDrawerContent(sideDrawerMode)}
      </Drawer>
      <Box
        sx={{
          width: sideDrawerOpen ? `calc(100% - ${sidebarWidth}px)` : '100%',
        }}
      >
        <Box display="flex">
          <Typography variant="h5">
            {settingsData?.company
              ? `${settingsData?.company} ${(insightsData?.dashboardLabel ?? pageLabel)?.toLowerCase()}`
              : (insightsData?.dashboardLabel ?? pageLabel)}
          </Typography>
          <ScrollableFilters />
        </Box>
        <Box>
          {isAccountAdmin && (
            <Box
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                justifyContent: 'space-between',
                gap: 1,
                mt: 1,
                mb: 1,
              }}
            >
              <ViewAsSelectors
                selectedRole={activeView}
                onClickRole={setActiveView}
                producerList={insightsData?.filterValues?.agent ?? []}
                selectedProducer={selectedProducer}
                onSelectProducer={(value) => {
                  updateUrlAndQuery('agent', value.id, true);
                }}
              />
              <Box>
                {showSaveLayoutButton && (
                  <Button variant="contained" onClick={saveLayout}>
                    {' '}
                    Save layout{' '}
                  </Button>
                )}
                <Button
                  variant="contained"
                  id="edit-widget"
                  sx={{ marginLeft: 1, maxHeight: 4 }}
                  onClick={() => openAddWidgetDialog(DrawerMode.CREATE_WIDGET)}
                >
                  Edit
                </Button>
                <IconButton
                  sx={{ alignSelf: 'flex-start', ml: 0.5 }}
                  onClick={handleMenuClick}
                >
                  <MoreVert />
                </IconButton>
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                >
                  <MenuItem key="Delete" onClick={deleteDashboard}>
                    Delete dashboard
                  </MenuItem>
                  <MenuItem key="copy" onClick={cloneDashboard}>
                    Create copy
                  </MenuItem>
                </Menu>
              </Box>
            </Box>
          )}
        </Box>
        <Box>
          {isLoading ? (
            <LoadingCircle />
          ) : (
            <ResponsiveGridLayout
              onLayoutChange={handleModify}
              onDragStop={onShowSaveLayoutButton}
              onResizeStop={onShowSaveLayoutButton}
              verticalCompact={true}
              cols={cols}
              breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
              preventCollision={false}
              draggableHandle=".dragHandle"
              autoSize={true}
              margin={margin}
              rowHeight={170}
            >
              {widgetArray?.map((widget) => {
                return (
                  <div
                    className="reactGridItem"
                    key={`${widget.i}`}
                    data-grid={{
                      x: widget?.x,
                      y: widget?.y,
                      w: widget?.w,
                      h: widget?.h,
                      i: widget?.i,
                      id: widget?.id,
                      minW: 2,
                      maxW: Infinity,
                      maxH: Infinity,
                      isDraggable: true,
                      isResizable: true,
                    }}
                  >
                    {widget.i === 'new' ? (
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          border: '1px dashed #ccc',
                          borderRadius: '5px',
                          height: '100%',
                          '&:hover': {
                            cursor: 'pointer',
                            border: '1px dashed #000',
                          },
                        }}
                        onClick={() =>
                          openAddWidgetDialog(DrawerMode.CREATE_WIDGET)
                        }
                      >
                        <Tooltip title="Add new widget">
                          <Add
                            sx={{
                              '&:hover': {
                                color: 'primary.main',
                                cursor: 'pointer',
                              },
                            }}
                          />
                        </Tooltip>
                      </Box>
                    ) : (
                      <WidgetWrapper
                        id={widget.id}
                        displayName={
                          labels.getLabel('dashboard', widget.labelId) ??
                          widget.displayName
                        }
                        spec={widget.spec}
                        data={widget.data ?? widget.value}
                        formatters={widget.formatters}
                        onEdit={() => editWidget(widget.id)}
                        onDelete={() => {
                          setDeleteConfirmDialogOpen(true);
                          setWidgetIdTobeDeleted(widget.id);
                        }}
                        onCopy={copyExistingWidget}
                        sharedWidget={
                          widget.access === WidgetAccessTypes.GLOBAL
                        }
                      />
                    )}
                  </div>
                );
              })}
            </ResponsiveGridLayout>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Dashboard;
