import { Dialog, DialogContent, IconButton, Box } from '@mui/material';
import { Close } from '@mui/icons-material';

import BasicTable from '@/components/molecules/BasicTable';
import { NoData } from './NoData';

export interface TableWidgetProps {
  data: (string | number)[][];
  formatters?: unknown;
  closeModal: () => void;
  isModalOpen: boolean;
}

const TableWidget = ({
  data,
  formatters,
  closeModal,
  isModalOpen,
}: TableWidgetProps) => {
  const handleClose = () => {
    closeModal();
  };

  const hasData = Array.isArray(data) && data.length > 1;

  return (
    <>
      <Box
        sx={{
          height: '100%',
          p: 2,
        }}
      >
        {hasData ? (
          <BasicTable
            headers={data[0] as string[]}
            rows={data.slice(1)}
            sorted={true}
            formatters={formatters}
            pageSize={6}
          />
        ) : (
          <NoData />
        )}
      </Box>
      <Dialog
        open={isModalOpen}
        fullScreen
        onClose={handleClose}
        sx={{ background: 'transparent', p: 1 }}
      >
        <IconButton
          className="group absolute p-4 right-0 top-0 cursor-pointer hover:text-blue-600"
          onClick={handleClose}
          sx={{ position: 'absolute', right: 8, top: 8, zIndex: 10 }}
        >
          <Close className="group-hover:rotate-180 transition-all origin-center" />
        </IconButton>
        <DialogContent
          sx={{
            p: 0,
            backgroundColor: '#fff',
            borderRadius: '4px',
            height: '100%',
          }}
        >
          <Box sx={{ p: 4, height: '100%' }}>
            {hasData ? (
              <BasicTable
                headers={data ? (data[0] as string[]) : []}
                rows={data?.slice(1)}
                sorted={true}
                formatters={formatters}
                pageSize={50}
              />
            ) : (
              <NoData />
            )}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TableWidget;
