import {
  Bar<PERSON><PERSON>Outlined,
  DonutLargeOutlined,
  Inventory2Outlined,
  ListAlt as ListAltIcon,
  PeopleOutlined,
  PersonOutline,
  ReceiptOutlined,
  RequestQuoteOutlined,
  ShowChartOutlined,
  SupportAgentOutlined,
  TableChartOutlined,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  FormControl,
  FormHelperText,
  FormLabel,
  Skeleton,
  Switch,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { GridLegacy as Grid } from '@mui/material';
import { javascript } from '@codemirror/lang-javascript';
import { python } from '@codemirror/lang-python';
import {
  WidgetTypes,
  CommissionsFilterByDateFieldLabels,
  CommissionsFilterByDateFields,
  DEFAULT_FIELD_VALUE,
  filterFieldsByDataSource,
  type GroupBy,
  PoliciesFilterByDateFieldLabels,
  PoliciesFilterByDateFields,
  WidgetAccessTypes,
  WidgetDataSourceLabels,
  WidgetDataSources,
  WidgetTypeLabels,
} from 'common/dto/widgets';
import { SystemRoles } from 'common/globalTypes';
import {
  isZodError,
  parseZodError,
  type ZodResponse,
} from 'common/helpers/zod';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import FieldAggregationManager from '@/components/molecules/FieldAggregation/FieldAggregationManager';
import FieldMatcher from '@/components/molecules/FieldMatcher';
import FieldsCalculatorExpressionBuilder from '@/components/molecules/FieldsCalculatorExpressionBuilder';
import { UIStateContext } from '@/contexts/UIStateProvider';
import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import { AccountAccessLevels } from '@/types';
import WidgetWrapper from './WidgetWrapper';
import CustomCodeEditor from '../molecules/CustomCodeEditor';
import ColumnLimitFields from '../molecules/ColumnLimitFields';
import GroupByManager from '../molecules/GroupBy/GroupByManager';
import type { AggregationSelector, SortBy } from 'common/dto/widgets';
import SortByManager from '../molecules/SortBy/SortByManager';

export interface CustomRender {
  code?: string;
  language: 'javascript' | 'python';
  enabled?: boolean;
}

const getDefaultCodeForWidgetType = (
  widgetType: string,
  language: 'javascript' | 'python'
) => {
  const codeTemplates = {
    javascript: {
      [WidgetTypes.BOX]: `(dataSource) => {
  return 42;
}`,
      [WidgetTypes.CHART_BAR]: `(dataSource) => {
  return {
    xAxis: {
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: 'Sample Data',
      type: 'bar',
      data: [20, 40, 30, 80, 50]
    }]
  };
}`,
      [WidgetTypes.CHART_LINE]: `(dataSource) => {
  return {
    xAxis: {
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: 'Sample Data',
      type: 'line',
      data: [20, 40, 30, 80, 50]
    }]
  };
}`,
      [WidgetTypes.CHART_DONUT]: `(dataSource) => {
  return {
    series: [{
      name: 'Sample Data',
      type: 'pie',
      data: [
        { name: 'Category A', value: 335 },
        { name: 'Category B', value: 310 },
        { name: 'Category C', value: 234 },
        { name: 'Category D', value: 135 }
      ]
    }]
  };
}`,
      [WidgetTypes.TABLE]: `(dataSource) => {
  return [['Header 1', 'Header 2'], ['Value 1', 'Value 2'], ['Value 3', 'Value 4']];
}`,
      default: `(dataSource) => {
  return [['Header 1', 'Header 2'], ['Value 1', 'Value 2'], ['Value 3', 'Value 4']];
}`,
    },
    python: {
      [WidgetTypes.BOX]: `import pandas as pd

def main(dataSource):
    return 42`,
      [WidgetTypes.CHART_BAR]: `import pandas as pd

def main(dataSource):
    return {
        "xAxis": {
            "data": ["Jan", "Feb", "Mar", "Apr", "May"]
        },
        "yAxis": {
            "type": "value"
        },
        "series": [{
            "name": "Sample Data",
            "type": "bar",
            "data": [20, 40, 30, 80, 50]
        }]
    }`,
      [WidgetTypes.CHART_LINE]: `import pandas as pd

def main(dataSource):
    return {
        "xAxis": {
            "data": ["Jan", "Feb", "Mar", "Apr", "May"]
        },
        "yAxis": {
            "type": "value"
        },
        "series": [{
            "name": "Sample Data",
            "type": "line",
            "data": [20, 40, 30, 80, 50]
        }]
    }`,
      [WidgetTypes.CHART_DONUT]: `import pandas as pd

def main(dataSource):
    return {
        "series": [{
            "name": "Sample Data",
            "type": "pie",
            "data": [
                {"name": "Category A", "value": 335},
                {"name": "Category B", "value": 310},
                {"name": "Category C", "value": 234},
                {"name": "Category D", "value": 135}
            ]
        }]
    }`,
      [WidgetTypes.TABLE]: `import pandas as pd

def main(dataSource):
    return [["Header 1", "Header 2"], ["Value 1", "Value 2"], ["Value 3", "Value 4"]]`,
      default: `import pandas as pd

def main(dataSource):
    return [["Header 1", "Header 2"], ["Value 1", "Value 2"], ["Value 3", "Value 4"]]`,
    },
  };

  return codeTemplates[language][widgetType] || codeTemplates[language].default;
};

const getLanguageExtension = (language: 'javascript' | 'python') => {
  switch (language) {
    case 'javascript':
      return javascript({ jsx: true, typescript: true });
    case 'python':
      return python();
    default:
      return javascript({ jsx: true, typescript: true });
  }
};

const dataSourceIcon: Record<WidgetDataSources, React.ReactNode> = {
  commissions: React.createElement(RequestQuoteOutlined),
  policies: React.createElement(ReceiptOutlined),
  agentPayouts: React.createElement(SupportAgentOutlined),
  contacts: React.createElement(PersonOutline),
  customers: React.createElement(PeopleOutlined),
};
const toggleStyle = {
  width: 200,
  display: 'flex',
  justifyContent: 'space-between',
};

const widgetGroupsIcon = {
  box: <Inventory2Outlined />,
  'chart-donut': <DonutLargeOutlined />,
  'chart-line': <ShowChartOutlined />,
  'chart-bar': <BarChartOutlined />,
  table: <TableChartOutlined />,
};

interface WidgetFormErrors {
  widgetName?: string;
  dataSource?: string;
  widgetType?: string;
  dataConfiguration?: string;
  dateFilter?: string;
  customCode?: string;
}

const WidgetCreator = ({
  widgetOnEdit,
  createWidget,
  closeAddWidgetDialog,
  saveChange,
  setWidgetModel,
  isEditingMode,
}) => {
  const { data: _fieldsData } = API.getBasicQuery(`fields`);
  const [errors, setErrors] = useState<WidgetFormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  if (_fieldsData) {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    _fieldsData.forEach((field) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.keys(filterFieldsByDataSource).forEach((dataSourceKey) => {
        const dataSource = filterFieldsByDataSource[dataSourceKey];

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        dataSource.groupByFields.forEach((groupField) => {
          if (groupField.name === field.key) {
            groupField.fieldMatcherType = field.type;
          }
        });

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        dataSource.dataFields.forEach((dataField) => {
          if (dataField.name === field.key) {
            dataField.fieldMatcherType = field.type;
          }
        });
      });
    });
  }

  const addSelector = () => {
    setDataFieldsAggregators([
      ...dataFieldsAggregators,
      {
        id: dataFieldsAggregators.length + 1,
        field: '',
        aggregation_method: '',
      },
    ]);
  };

  useEffect(() => {
    if (widgetOnEdit) {
      setSelectedDataSource(widgetOnEdit.dataSource || '');
      setCustomRender(
        widgetOnEdit.customRender || {
          enabled: false,
          language: 'javascript',
          code: getDefaultCodeForWidgetType(
            widgetOnEdit.type || WidgetTypes.TABLE,
            'javascript'
          ),
        }
      );
      setSelectedColumnValue(widgetOnEdit.column || '');
      setColumnLimit(widgetOnEdit.columnLimit || null);
      setSelectedWidgetType(widgetOnEdit.type || '');
      setSelectedTimePeriodValue(widgetOnEdit.timePeriod || 'month');
      setDataFieldsAggregators(
        widgetOnEdit.aggregationSelectors || [
          {
            field: DEFAULT_FIELD_VALUE,
            aggregation_method: 'Count',
            formatter: 'number',
          },
        ]
      );
      setGroupByValues(widgetOnEdit.groupBys || []);
      setSelectedSortByValues(widgetOnEdit.sortBys || []);
      setFilterByDate(widgetOnEdit.filterByDate || '');
      setDataFieldsExpression(widgetOnEdit.dataFieldsExpression || null);
      setFilters(widgetOnEdit.filters || []);
      setWidgetName(widgetOnEdit.name || '');
    } else {
      setSelectedDataSource('');
      setCustomRender({
        enabled: false,
        language: 'javascript',
        code: getDefaultCodeForWidgetType(WidgetTypes.TABLE, 'javascript'),
      });
      setSelectedColumnValue('');
      setColumnLimit(null);
      setSelectedWidgetType('');
      setSelectedTimePeriodValue('month');
      setFilterByDate('');
      setDataFieldsAggregators([
        {
          field: DEFAULT_FIELD_VALUE,
          aggregation_method: 'Count',
          formatter: 'number',
        },
      ]);
      setDataFieldsExpression(null);
      setFilters([]);
      setWidgetName('');
    }
  }, [widgetOnEdit]);

  const removeSelector = (id) => {
    setDataFieldsAggregators(
      dataFieldsAggregators.filter((selector) => selector.id !== id)
    );
  };

  const updateSelector = (id, updatedField) => {
    setDataFieldsAggregators(
      dataFieldsAggregators.map((selector) =>
        selector.id === id ? { ...selector, ...updatedField } : selector
      )
    );
  };

  const [selectedAccessLevel, setSelectedAccessLevel] = useState<boolean>(
    widgetOnEdit?.accessLevel !== undefined
      ? widgetOnEdit?.accessLevel !== 'global'
      : false
  );
  const [selectedDataSource, setSelectedDataSource] = useState<
    WidgetDataSources | ''
  >(widgetOnEdit?.dataSource);

  const [customRender, setCustomRender] = useState<CustomRender>(
    widgetOnEdit?.customRender || {
      enabled: false,
      language: 'javascript',
      code: getDefaultCodeForWidgetType('table', 'javascript'),
    }
  );

  const [selectedWidgetType, setSelectedWidgetType] = useState(
    widgetOnEdit?.type
  );

  const resetLanguageEnvironment = useCallback(
    (language: 'javascript' | 'python') => {
      setCustomRender((prev) => ({
        ...prev,
        code: getDefaultCodeForWidgetType(selectedWidgetType, language),
        language: language,
      }));
    },
    [selectedWidgetType]
  );

  const updateWidgetTypeAndCodeTemplate = useCallback((widgetType: string) => {
    setSelectedWidgetType(widgetType);
    setCustomRender((prev) => ({
      ...prev,
      code: getDefaultCodeForWidgetType(widgetType, prev.language),
    }));
  }, []);

  const [groupByValues, setGroupByValues] = useState<GroupBy[]>(
    widgetOnEdit?.groupBys || [
      {
        id: 1,
        field: '',
        timePeriod: null,
      },
    ]
  );

  const addGroupBy = () => {
    setGroupByValues([
      ...groupByValues,
      {
        id: groupByValues.length + 1,
        field: '',
        timePeriod: null,
      },
    ]);
  };

  const removeGroupBy = (id: number) => {
    setGroupByValues(groupByValues.filter((groupBy) => groupBy.id !== id));
  };

  const updateGroupBy = (id: number, updatedField: Partial<GroupBy>) => {
    setGroupByValues(
      groupByValues.map((groupBy) =>
        groupBy.id === id ? { ...groupBy, ...updatedField } : groupBy
      )
    );
  };

  const [selectedSortByValues, setSelectedSortByValues] = useState<SortBy[]>(
    widgetOnEdit?.sortBys || [
      {
        id: 1,
        field: '',
        order: null,
        limit: null,
      },
    ]
  );

  const addSortBy = () => {
    // TODO: Support multiple sortBys for tables
    if (selectedSortByValues.length > 0) {
      return;
    }
    setSelectedSortByValues([
      ...selectedSortByValues,
      {
        id: selectedSortByValues.length + 1,
        field: '',
        order: null,
        limit: null,
      },
    ]);
  };

  const removeSortBy = (id: number) => {
    setSelectedSortByValues(
      selectedSortByValues.filter((sortBy) => sortBy.id !== id)
    );
  };

  const updateSortBy = (id: number, updatedField: Partial<SortBy>) => {
    setSelectedSortByValues(
      selectedSortByValues.map((sortBy) =>
        sortBy.id === id ? { ...sortBy, ...updatedField } : sortBy
      )
    );
  };

  const [selectedTimePeriodValue, setSelectedTimePeriodValue] =
    useState<string>(widgetOnEdit?.timePeriod || 'month');
  const [filterByDate, setFilterByDate] = useState<string>(
    widgetOnEdit?.filterByDate
  );
  const [selectedColumnValue, setSelectedColumnValue] = useState(
    widgetOnEdit?.column
  );
  const [columnLimit, setColumnLimit] = useState(widgetOnEdit?.columnLimit);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [dataFieldsAggregators, setDataFieldsAggregators] = useState<any>(
    widgetOnEdit?.aggregationSelectors || [
      {
        field: DEFAULT_FIELD_VALUE,
        aggregation_method: 'Count',
        formatter: 'number',
      },
    ]
  );
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [dataFieldsExpression, setDataFieldsExpression] = useState<any>(
    widgetOnEdit?.dataFieldsExpression || null
  );

  const combinedSortFields = useMemo(() => {
    const aggregatorFields = dataFieldsAggregators.map(
      (selector: AggregationSelector) => {
        const fieldDef = filterFieldsByDataSource[
          selectedDataSource
        ]?.dataFields.find((f) => f.name === selector.field);
        return {
          name: selector.field,
          displayName: fieldDef?.displayName || selector.field,
        };
      }
    );

    const groupByFields = groupByValues.map((groupBy) => {
      const groupDef = filterFieldsByDataSource[
        selectedDataSource
      ]?.groupByFields.find((f) => f.name === groupBy.field);
      return {
        name: groupBy.field,
        displayName: groupDef?.displayName || groupBy.field,
      };
    });

    return [...aggregatorFields, ...groupByFields];
  }, [dataFieldsAggregators, groupByValues, selectedDataSource]);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [filters, setFilters] = useState<any>([
    ...(widgetOnEdit?.filters ?? []),
  ]);
  const { showSnackbar } = useSnackbar();

  const [widgetName, setWidgetName] = useState(widgetOnEdit?.name || '');
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [previewData, setPreviewData] = useState<any>(null);
  const {
    role: [role],
  } = useContext(UIStateContext);
  const isFintaryAdmin = role === SystemRoles.ADMIN;

  // Function to reset states when data source changes
  const resetStatesOnDataSourceChange = useCallback(() => {
    setSelectedTimePeriodValue('month');
    setSelectedColumnValue('');
    setColumnLimit(null);
    setDataFieldsAggregators([
      {
        field: DEFAULT_FIELD_VALUE,
        aggregation_method: 'Count',
        formatter: 'number',
      },
    ]);
    setDataFieldsExpression(null);
    setFilters([]);
  }, []);

  const handleDataSourceChange = useCallback(
    (newDataSource: WidgetDataSources | '') => {
      setSelectedDataSource(newDataSource);
      if (
        ![
          WidgetDataSources.POLICIES.toString(),
          WidgetDataSources.COMMISSIONS.toString(),
        ].includes(newDataSource)
      ) {
        setFilterByDate('none');
      } else {
        setFilterByDate('');
      }
    },
    []
  );

  const validateForm = () => {
    const newErrors: WidgetFormErrors = {};

    if (!widgetName) {
      newErrors.widgetName = 'Widget name is required';
    }

    if (!selectedDataSource) {
      newErrors.dataSource = 'Data source is required';
    }

    if (!selectedWidgetType && !customRender.enabled) {
      newErrors.widgetType = 'Widget type is required';
    }

    if (
      !filterByDate &&
      ['policies', 'commissions'].includes(selectedDataSource)
    ) {
      newErrors.dateFilter = 'Date filter is required';
    }

    if (
      dataFieldsAggregators.length === 0 &&
      !customRender.enabled &&
      !filters.length
    ) {
      newErrors.dataConfiguration =
        'At least one data configuration is required';
    }

    if (customRender.enabled && !customRender.language) {
      newErrors.customCode = 'A language must be selected for custom code';
    }

    setErrors(newErrors);
    console.error(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateWidget = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const uniqueKey = filterFieldsByDataSource[
        selectedDataSource
      ]?.groupByFields.find(
        (f) => f.name === groupByValues[0]?.field
      )?.uniqueKey;
      const result = await createWidget({
        name: widgetName,
        access: selectedAccessLevel
          ? AccountAccessLevels.GLOBAL
          : AccountAccessLevels.ACCOUNT,
        dataSource: selectedDataSource,
        filterByDate: filterByDate,
        groupBys: groupByValues,
        sortBys: selectedSortByValues,
        // Add uniqueKey to avoid duplicate names for companies or contacts.
        uniqueKey,
        type: selectedWidgetType,
        filters: filters,
        timePeriod: selectedTimePeriodValue,
        column: selectedColumnValue,
        columnLimit: columnLimit,
        aggregationSelectors: dataFieldsAggregators,
        dataFieldsExpression: dataFieldsExpression,
        customRender: customRender,
      });
      setPreviewData(result);
      setWidgetModel({
        id: result.id,
        access: selectedAccessLevel
          ? AccountAccessLevels.GLOBAL
          : AccountAccessLevels.ACCOUNT,
        name: widgetName,
        displayName: result.displayName,
        widgetGroup: selectedWidgetType?.toUpperCase(),
        value: result.value,
        spec: {
          dataSource: selectedDataSource,
          filterByDate: filterByDate,
          name: widgetName,
          type: selectedWidgetType,
          groupBys: groupByValues,
          timePeriod: selectedTimePeriodValue,
          uniqueKey,
          column: selectedColumnValue,
          sortBys: selectedSortByValues,
          columnLimit: columnLimit,
          filters: filters,
          customRender: customRender,
          aggregationSelectors: dataFieldsAggregators,
          dataFieldsExpression: dataFieldsExpression,
        },
      });
    } catch (error) {
      if (isZodError((error as { error?: unknown }).error)) {
        showSnackbar(
          `Issue with widget definition: ${parseZodError(error as ZodResponse)}`,
          'error'
        );
      } else {
        const errorMessage =
          (error as Error)?.message || 'An unexpected error occurred.';
        showSnackbar(`Error creating widget: ${errorMessage}`, 'error');
      }
      setPreviewData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const commonInputStyle = {
    width: '100%',
    '& .MuiInputBase-root': {
      height: '40px !important',
    },
  };

  const renderWidget = (data) => (
    <WidgetWrapper
      id={data.id}
      displayName={data.displayName}
      spec={data.spec}
      data={data.data ?? data.value}
      formatters={data.formatters}
      sharedWidget={data.access === WidgetAccessTypes.GLOBAL}
    />
  );

  const renderLoadingSkeleton = () => {
    return (
      <Box
        id="shimmer-widget-prevew-loading"
        sx={{ width: '100%', height: '100%', p: 2 }}
      >
        <Box sx={{ mb: 2 }}>
          <Skeleton variant="text" width="60%" height={24} />
        </Box>
        <Box
          sx={{
            display: 'flex',
            height: 'calc(100% - 40px)',
            alignItems: 'flex-end',
            gap: 2,
          }}
        >
          {[...Array(5)].map((_, i) => (
            <Skeleton
              // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              key={i}
              variant="rectangular"
              width="15%"
              height={`${Math.random() * 70 + 20}%`}
              sx={{ borderRadius: '4px 4px 0 0' }}
            />
          ))}
        </Box>
      </Box>
    );
  };

  const getFilterByDateOptions = () => {
    const noneOption = { id: 'none', label: 'None' };

    if (selectedDataSource === WidgetDataSources.COMMISSIONS) {
      return [
        ...Object.values(CommissionsFilterByDateFields).map((value) => ({
          id: value,
          label: CommissionsFilterByDateFieldLabels[value],
        })),
      ];
    }

    if (selectedDataSource === WidgetDataSources.POLICIES) {
      return [
        ...Object.values(PoliciesFilterByDateFields).map((value) => ({
          id: value,
          label: PoliciesFilterByDateFieldLabels[value],
        })),
      ];
    }

    return [noneOption];
  };

  const filterByDateOptions = getFilterByDateOptions();

  return (
    <Box sx={{ padding: 2 }}>
      <Typography variant="h6">
        {isEditingMode ? 'Edit widget' : 'Create a new widget'}
      </Typography>
      <FormControl fullWidth margin="normal">
        <TextField
          id="widget-name"
          sx={{ flex: 1 }}
          value={widgetName}
          label="Widget name"
          error={!!errors.widgetName}
          helperText={errors.widgetName}
          onChange={(e) => {
            setWidgetName(e.target.value);
            if (errors.widgetName) {
              setErrors({ ...errors, widgetName: undefined });
            }
          }}
        />
      </FormControl>
      <FormControl fullWidth margin="normal" id="data-source-select">
        <EnhancedSelect
          label="Data source"
          sx={commonInputStyle}
          options={Object.keys(filterFieldsByDataSource).map((source) => ({
            id: source,
            label: (
              <div className="flex items-center gap-2">
                {dataSourceIcon[source]}
                <span>{WidgetDataSourceLabels[source]}</span>
              </div>
            ),
          }))}
          value={{
            id: selectedDataSource,
            label: WidgetDataSourceLabels[selectedDataSource] || '',
          }}
          onChange={(value) => {
            const prevDataSource = selectedDataSource;
            handleDataSourceChange(value.id as WidgetDataSources);

            // Reset states if data source changes
            if (prevDataSource !== value.id) {
              resetStatesOnDataSourceChange();
            }

            if (errors.dataSource) {
              setErrors({ ...errors, dataSource: undefined });
            }
          }}
        />
        {errors.dataSource && (
          <FormHelperText error>{errors.dataSource}</FormHelperText>
        )}
      </FormControl>
      {['policies', 'commissions'].includes(selectedDataSource) && (
        <FormControl fullWidth margin="normal">
          <EnhancedSelect
            label="Filter by date"
            sx={commonInputStyle}
            options={filterByDateOptions}
            value={filterByDateOptions.find((item) => item.id === filterByDate)}
            onChange={(value) => {
              setFilterByDate(value.id);

              if (errors.dateFilter) {
                setErrors({ ...errors, dateFilter: undefined });
              }
            }}
          />
          {errors.dateFilter && (
            <FormHelperText error>{errors.dateFilter}</FormHelperText>
          )}
        </FormControl>
      )}
      <FormControl fullWidth margin="normal" id="widget-type-select">
        <EnhancedSelect
          label="Widget type"
          sx={commonInputStyle}
          options={Object.values(WidgetTypes).map((label) => ({
            id: label,
            label: (
              <div className="flex items-center gap-2">
                {widgetGroupsIcon[label]}
                <span>{WidgetTypeLabels[label]}</span>
              </div>
            ),
          }))}
          value={{
            id: selectedWidgetType,
            label: WidgetTypeLabels[selectedWidgetType],
          }}
          onChange={(value) => {
            updateWidgetTypeAndCodeTemplate(value.id);
            if (errors.widgetType) {
              setErrors({ ...errors, widgetType: undefined });
            }
          }}
        />
        {errors.widgetType && (
          <FormHelperText error>{errors.widgetType}</FormHelperText>
        )}
      </FormControl>
      {errors.dataConfiguration && (
        <Typography color="error" sx={{ mt: 1, mb: 1 }}>
          {errors.dataConfiguration}
        </Typography>
      )}
      {isFintaryAdmin && customRender?.enabled && (
        <CustomCodeEditor
          code={customRender?.code ?? ''}
          language={customRender?.language}
          onCodeChange={(code) => setCustomRender({ ...customRender, code })}
          onLanguageChange={resetLanguageEnvironment}
          getLanguageExtension={getLanguageExtension}
        />
      )}
      {!customRender.enabled &&
        ['chart-bar', 'table', 'chart-donut', 'chart-line'].includes(
          selectedWidgetType
        ) && (
          <FormControl fullWidth margin="normal" id="group-by-select">
            <FormLabel htmlFor="group-by-select-manager">Group by</FormLabel>
            <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}>
              <GroupByManager
                fields={
                  filterFieldsByDataSource[selectedDataSource]?.groupByFields ??
                  []
                }
                groupBys={groupByValues}
                addGroupBy={addGroupBy}
                removeGroupBy={removeGroupBy}
                updateGroupBy={updateGroupBy}
              />
            </Box>
          </FormControl>
        )}
      {!customRender?.enabled &&
        [WidgetTypes.CHART_BAR].includes(selectedWidgetType) && (
          <ColumnLimitFields
            selectedDataSource={selectedDataSource}
            selectedColumnValue={selectedColumnValue}
            onColumnChange={setSelectedColumnValue}
            columnLimit={columnLimit}
            onColumnLimitChange={setColumnLimit}
          />
        )}
      {!customRender?.enabled &&
        filterFieldsByDataSource[selectedDataSource]?.dataFields && (
          <FormControl fullWidth margin="normal" id="data-fields-select">
            <FormLabel htmlFor="data-fields-aggregation-manager">
              Data fields
            </FormLabel>
            <FieldAggregationManager
              fields={filterFieldsByDataSource[selectedDataSource]?.dataFields}
              selectors={dataFieldsAggregators}
              addSelector={addSelector}
              removeSelector={removeSelector}
              updateSelector={updateSelector}
            />
          </FormControl>
        )}
      {!customRender?.enabled &&
        filterFieldsByDataSource[selectedDataSource]?.groupByFields && (
          <Box>
            <FormLabel htmlFor="filters-field-matcher">Filters</FormLabel>
            <FieldMatcher
              fields={[
                ...(filterFieldsByDataSource[selectedDataSource]
                  ?.groupByFields ?? []),
              ].map((field) => {
                return {
                  id: field.name,
                  label: field.displayName,
                  fieldMatcherType: field.fieldMatcherType,
                };
              })}
              hideUsePolicyData={true}
              value={filters}
              setValue={setFilters}
            />
          </Box>
        )}
      {!customRender?.enabled &&
        [
          WidgetTypes.CHART_BAR,
          WidgetTypes.TABLE,
          WidgetTypes.CHART_DONUT,
          WidgetTypes.CHART_LINE,
        ].includes(selectedWidgetType) && (
          <FormControl fullWidth margin="normal" id="sort-bys-select">
            <FormLabel htmlFor="data-fields-aggregation-manager">
              Sort by
            </FormLabel>
            <SortByManager
              fields={combinedSortFields}
              sortBys={selectedSortByValues}
              addSortBy={addSortBy}
              removeSortBy={removeSortBy}
              updateSortBy={updateSortBy}
            />
          </FormControl>
        )}
      {!customRender?.enabled && dataFieldsAggregators.length > 1 && (
        <FormControl fullWidth margin="normal">
          <FormLabel htmlFor="fields-calculation-input">
            Fields calculation
          </FormLabel>
          <FieldsCalculatorExpressionBuilder
            fields={dataFieldsAggregators.map((selector) => selector.field)}
            value={dataFieldsExpression}
            setValue={setDataFieldsExpression}
          />
        </FormControl>
      )}
      <FormControl fullWidth margin="normal">
        {isFintaryAdmin && (
          <>
            <Grid container spacing={2} sx={toggleStyle}>
              <Grid item>
                <Typography>
                  Global{' '}
                  <span
                    style={{
                      color: 'transparent',
                      textShadow: '0 0 0 #e8e8e8',
                    }}
                  >
                    🔒
                  </span>
                </Typography>
              </Grid>
              <Grid item>
                <Switch
                  checked={selectedAccessLevel}
                  onChange={() => setSelectedAccessLevel(!selectedAccessLevel)}
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} sx={toggleStyle}>
              <Grid item>
                <Typography>
                  Custom code{' '}
                  <span
                    style={{
                      color: 'transparent',
                      textShadow: '0 0 0 #e8e8e8',
                    }}
                  >
                    🔒
                  </span>
                </Typography>
              </Grid>
              <Grid item>
                <Switch
                  checked={customRender?.enabled}
                  onChange={() =>
                    setCustomRender((prev) => ({
                      ...prev,
                      enabled: !prev.enabled,
                    }))
                  }
                />
              </Grid>
            </Grid>
          </>
        )}
      </FormControl>
      <hr />
      <Box
        mt={2}
        display={'flex'}
        flexDirection={'row'}
        alignItems={'center'}
        gap={4}
      >
        <Button
          variant="contained"
          color="primary"
          onClick={handleCreateWidget}
        >
          Preview
        </Button>
        {previewData && (
          <Chip
            icon={<ListAltIcon />}
            label={`${previewData?.rowCount || 0} ${previewData?.rowCount === 1 ? 'record' : 'records'} found`}
            color="primary"
            variant="outlined"
            sx={{ mr: 1, fontWeight: 500, fontSize: 16 }}
          />
        )}
      </Box>
      <Box mt={4} mb={4} p={2} borderColor="grey.400" id="preview-widget">
        {isLoading
          ? renderLoadingSkeleton()
          : previewData && renderWidget(previewData)}
      </Box>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row-reverse',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingBlock: '10px',
          borderBottom: '1px solid #e0e0e0',
          height: '100px',
          // position: 'fixed',
          // bottom: '0',
          // right: '2rem',
          maxWidth: '500px',
          // zIndex: 9999,
          marginBottom: '50px',
          background: 'transparent',
        }}
      >
        <Box>
          <Button
            variant="outlined"
            style={{ marginRight: '10px', backgroundColor: 'white' }}
            onClick={() => {
              setPreviewData(null);
              closeAddWidgetDialog();
            }}
          >
            Close
          </Button>
          <Tooltip
            title={!previewData ? 'Preview the widget first' : ''}
            placement="top"
          >
            <span>
              <Button
                variant="contained"
                id="btn-save-widget"
                color="primary"
                onClick={saveChange}
                disabled={!previewData}
              >
                Save
              </Button>
            </span>
          </Tooltip>
        </Box>
      </Box>
    </Box>
  );
};

export default WidgetCreator;
