import { Fullscreen, MoreVert } from '@mui/icons-material';
import {
  Box,
  Card,
  CardContent,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Typography,
} from '@mui/material';
import { GridLegacy as Grid } from '@mui/material';
import BigNumber from 'bignumber.js';
import {
  WidgetDataSourceLabels,
  type WidgetDefinition,
  WidgetFilterByDateFieldLabels,
  WidgetTypes,
} from 'common/dto/widgets';
import Formatter from 'common/Formatter';
import { SystemRoles } from 'common/globalTypes';
import type React from 'react';
import { useContext, useMemo, useState } from 'react';

import { UIStateContext } from '@/contexts/UIStateProvider';
import { exportCsv } from '@/services/helpers';
import { useRoleStore } from '@/store';
import { Roles } from '@/types';
import TableWidget from './TableWidget';
import ChartWidget from './ChartWidget';
import BoxWidget from './BoxWidget';

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const getTableData = (data: any, resultFormatter: any) => {
  if (!data.legend) {
    return data?.xAxis.data.map((key, i) => [key, data?.series?.[0].data?.[i]]);
  }

  return data?.xAxis.data.map((key, i) => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const rowData = data.series.map((series: any) => {
      const value = series.data[i];
      return typeof value === 'string' ? parseFloat(value) : value;
    });

    let total = rowData.reduce((acc: number, curr: number) => acc + curr, 0);
    total = BigNumber(total).toFormat(2);
    if (resultFormatter) {
      total = Formatter[resultFormatter]?.(total) ?? total;
      rowData.forEach((value, i) => {
        rowData[i] = Formatter[resultFormatter]?.(value) ?? value;
      });
    }
    return [key, ...rowData, total];
  });
};

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const getHeaders = (data: any) => {
  if (!data.legend) {
    return ['key', 'value'];
  }
  return ['Key', ...data.legend.data, 'Total'];
};

interface WidgetWrapperProps {
  displayName: string;
  onEdit?: () => void;
  onDelete?: () => void;
  sharedWidget?: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onCopy?: (data: any) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data?: any;
  spec: WidgetDefinition;
  isPreview?: boolean;
  id?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  formatters?: any;
}

const WidgetWrapper = ({
  id,
  data,
  displayName,
  onEdit,
  onDelete,
  onCopy,
  spec,
  isPreview = false,
  formatters,
}: WidgetWrapperProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [view, setView] = useState('widget');
  const {
    role: [role],
  } = useContext(UIStateContext);
  const isFintaryAdmin = role === SystemRoles.ADMIN;
  const { userRole } = useRoleStore();
  const isAccountAdmin = userRole === Roles.ACCOUNT_ADMIN;

  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getPropsFromSpec = (spec) => {
    if (!spec) {
      return {};
    }
    let dataField = Array.isArray(spec.dataField)
      ? spec.dataField[0]
      : spec.dataField;
    let aggregationMethod = spec.calculation;
    let resultFormatter = spec.resultFormatter;
    if (spec.aggregationSelectors?.length > 0) {
      dataField = spec.aggregationSelectors[0].field;
      aggregationMethod = spec.aggregationSelectors[0].aggregation_method;
      resultFormatter = spec.aggregationSelectors[0].formatter;
    }
    const type = spec.type;
    return { dataField, aggregationMethod, resultFormatter, type };
  };
  const { resultFormatter, type } = getPropsFromSpec(spec);

  const computeHeaders = useMemo(() => {
    if (type === WidgetTypes.CHART_DONUT) {
      return Object.keys(data?.series?.[0]?.data?.[0] || {});
    }
    if (type === WidgetTypes.CHART_BAR) {
      return getHeaders(data);
    }
    if (type === WidgetTypes.CHART_LINE) {
      return ['key', 'value'];
    }
    if (type === WidgetTypes.TABLE) {
      return data ? data[0] : [];
    }
    if (type === WidgetTypes.BOX) {
      return [displayName];
    }
    return [];
  }, [type, data, displayName]);

  const computeRows = useMemo(() => {
    if (type === WidgetTypes.CHART_DONUT) {
      return data?.series?.[0]?.data?.map((row) => Object.values(row)) ?? [];
    }
    if (type === WidgetTypes.CHART_BAR) {
      return getTableData(data, resultFormatter);
    }
    if (type === WidgetTypes.CHART_LINE) {
      return (
        data?.xAxis.data.map((key, i) => [key, data?.series?.[0].data?.[i]]) ??
        []
      );
    }
    if (type === WidgetTypes.TABLE) {
      return data ? data.slice(1) : [];
    }
    if (type === WidgetTypes.BOX) {
      return [data?.toString()];
    }
    return [];
  }, [type, data, resultFormatter]);

  const widgetLegend = (
    <Typography variant="body2">
      {`Data source: ${WidgetDataSourceLabels[spec.dataSource]}`}
      {spec?.filterByDate && (
        <>
          <br />
          {`Date filter: ${WidgetFilterByDateFieldLabels[spec.filterByDate]}`}
        </>
      )}
      {spec?.groupBys && spec?.groupBys.length > 0 && (
        <>
          <br />
          {`Group by: ${spec.groupBys[0].field}`}
        </>
      )}
      {spec?.aggregationSelectors?.length > 0 && (
        <>
          <br />
          {`Aggregation: ${spec.aggregationSelectors
            .map(
              (selector) => `${selector.field} (${selector.aggregation_method})`
            )
            .join(', ')}`}
        </>
      )}
    </Typography>
  );

  const canEditAndDelete =
    spec?.access === 'global'
      ? isFintaryAdmin
      : isFintaryAdmin || isAccountAdmin;

  const renderWidget = () => {
    switch (type) {
      case WidgetTypes.BOX:
        return <BoxWidget id={id} displayName={displayName} value={data} />;
      case WidgetTypes.CHART_BAR:
        return <ChartWidget data={data} />;
      case WidgetTypes.CHART_LINE:
        return <ChartWidget data={data} />;
      case WidgetTypes.CHART_DONUT:
        return <ChartWidget data={data} />;
      case WidgetTypes.TABLE:
        return (
          <TableWidget
            data={data}
            formatters={formatters}
            closeModal={handleCloseModal}
            isModalOpen={isModalOpen}
          />
        );
      default:
        return <h5>hello world</h5>;
    }
  };

  return (
    <Card style={{ height: '100%', overflowY: 'hidden' }}>
      <CardContent
        sx={{
          padding: 0,
          height: '100%',
          position: 'relative',
          '&:last-child': { paddingBottom: 0 },
        }}
      >
        <Grid
          container
          justifyContent="space-between"
          className="grid-item__title"
          p={1}
          style={{
            boxShadow: '0.5px 1px 1px #ececec',
            height: '48px',
            minHeight: '48px',
            maxHeight: '48px',
          }}
        >
          <Grid
            item
            sx={{
              flex: 1,
              minWidth: 0,
              display: 'flex',
              alignItems: 'center',
              flexGrow: 1,
            }}
            className="dragHandle"
          >
            <Box
              sx={{ width: '100%' }}
              minHeight="100px"
              maxHeight="100px"
              flex="1"
              display="flex"
              flexDirection="row"
              justifyContent="flex-start"
              alignItems="center"
              flexWrap="wrap"
              flexGrow={1}
              gap={1}
              minWidth={0}
            >
              {!isPreview ? (
                <Tooltip title={widgetLegend}>
                  <Typography
                    sx={{ p: 0, m: 0 }}
                    gutterBottom
                    variant="body1"
                    noWrap
                  >
                    {displayName}
                  </Typography>
                </Tooltip>
              ) : (
                <Typography noWrap sx={{ p: 0, m: 0 }}>
                  {displayName}
                </Typography>
              )}
            </Box>
          </Grid>

          <Grid
            item
            sx={{
              flexShrink: 0,
              display: 'flex',
              alignItems: 'center',
              padding: 0,
            }}
          >
            {!isPreview && (
              <>
                {type === WidgetTypes.TABLE && (
                  <IconButton
                    onClick={handleOpenModal}
                    sx={{ p: 0, ml: 1 }}
                    aria-label="Expand table"
                  >
                    <Fullscreen />
                  </IconButton>
                )}
                <IconButton onClick={handleMenuClick} sx={{ p: 0 }}>
                  <MoreVert />
                </IconButton>
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                >
                  {[
                    onEdit && (
                      <MenuItem
                        key="edit"
                        onClick={() => {
                          handleMenuClose();
                          onEdit();
                        }}
                        className="flex justify-between"
                        disabled={!canEditAndDelete}
                      >
                        Edit{' '}
                        {!canEditAndDelete && (
                          <span
                            style={{
                              color: 'transparent',
                              textShadow: '0 0 0 #e8e8e8',
                            }}
                          >
                            🔒
                          </span>
                        )}
                      </MenuItem>
                    ),
                    onDelete && (
                      <MenuItem
                        key="delete"
                        onClick={() => {
                          handleMenuClose();
                          onDelete();
                        }}
                        className="flex justify-between"
                        disabled={!canEditAndDelete}
                      >
                        Delete{' '}
                        {!canEditAndDelete && (
                          <span
                            style={{
                              color: 'transparent',
                              textShadow: '0 0 0 #e8e8e8',
                            }}
                          >
                            🔒
                          </span>
                        )}
                      </MenuItem>
                    ),
                  ]}
                  {onCopy && (
                    <MenuItem
                      key="copy"
                      onClick={() => {
                        handleMenuClose();
                        onCopy(spec);
                      }}
                    >
                      Create copy
                    </MenuItem>
                  )}
                  {typeof type === 'string' &&
                    ['chart-donut', 'chart-bar'].includes(type) && [
                      <MenuItem
                        key="toggleView"
                        onClick={() => {
                          setView(view === 'table' ? 'widget' : 'table');
                        }}
                      >
                        {view === 'table' ? 'Widget view' : 'Table view'}
                      </MenuItem>,
                    ]}
                  <MenuItem
                    key="export"
                    onClick={() => {
                      exportCsv(
                        computeHeaders,
                        computeRows,
                        'Fintary-Export.csv'
                      );
                    }}
                  >
                    Export data
                  </MenuItem>
                </Menu>
              </>
            )}
          </Grid>
        </Grid>
        {view === 'table' ? (
          <TableWidget
            data={computeRows}
            formatters={formatters}
            closeModal={handleCloseModal}
            isModalOpen={isModalOpen}
          />
        ) : (
          renderWidget()
        )}
      </CardContent>
    </Card>
  );
};

export default WidgetWrapper;
