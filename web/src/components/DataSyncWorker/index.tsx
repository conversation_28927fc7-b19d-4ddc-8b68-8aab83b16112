import { getEnvVariable } from '@/env';
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormLabel,
  TextField,
} from '@mui/material';
import { AccountIds, WorkerNames } from 'common/constants';
import type {
  NowCertsPolicyStatus,
  SyncParamsDTO,
} from 'common/dto/data_processing/sync';
import dayjs from 'dayjs';
import { useState } from 'react';

import AdminCard from '@/common/AdminCard';
import BasicDateRangePicker from '@/common/BasicDateRangePicker';
import MultiSelect from '@/components/molecules/MultiSelect';
import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import { useAccountStore } from '@/store';

export const DataSyncWorker = ({ workerInfo, onSyncComplete, onSyncStart }) => {
  const { showSnackbar } = useSnackbar();

  const isAgencyIntegrator =
    workerInfo?.worker === WorkerNames.AgencyIntegratorWorker;
  const isTransGlobal = workerInfo?.worker === WorkerNames.TransGlobalWorker;
  const isSmartOffice = workerInfo?.worker === WorkerNames.SmartOfficeWorker;
  const isMyAdvisorGrids =
    workerInfo?.worker === WorkerNames.MyAdvisorGridsWorker;
  const [showSyncDateRange, setShowSyncDateRange] = useState(false);
  const { selectedAccount } = useAccountStore();
  const [loading, setLoading] = useState(false);
  const policyStates: NowCertsPolicyStatus[] = [
    'Active',
    'Cancelled',
    'Expired',
    'Renewed',
    'Fintary',
    'Non-Renewal',
    'Pending Cancel',
    'Renewing',
    'Replaced',
  ];

  const isTrailStoneAccount =
    selectedAccount?.accountId === AccountIds.TRAILSTONE;
  const SYNCERS = workerInfo?.entities || [];
  const [syncArgs, setSyncArgs] = useState<SyncParamsDTO>({
    entities: SYNCERS,
    sync: false,
    startDate: null,
    endDate: null,
    worker: workerInfo?.worker,
    policyStates: isTrailStoneAccount ? policyStates : undefined,
    policyNumbers: [],
    compGridIds: [],
  });

  const [showPolicyNumbers, setShowPolicyNumbers] = useState(false);
  const { data: compGrids } = API.getBasicQuery('comp-grids');

  const syncData = async () => {
    const res = await fetch(
      `${getEnvVariable('API')}/api/data_processing/sync`,
      {
        method: 'POST',
        headers: await API.getHeaders(),
        body: JSON.stringify(syncArgs),
      }
    );
    return res;
  };

  const handleSync = async () => {
    try {
      setLoading(true);
      const res = await syncData();
      const data = await res.json();
      if (res.status === 200) {
        if (!data?.success) {
          showSnackbar(data.message, 'error');
        } else {
          onSyncStart?.(data.taskId);
          showSnackbar(
            'Data sync task has been created, please check data processing log to view task status',
            'success'
          );
        }
      } else if (res.status >= 400) {
        showSnackbar(data.message || data.statusText, 'error');
      } else {
        showSnackbar('Error syncing data', 'error');
      }
    } catch {
      showSnackbar('Error syncing data', 'error');
    } finally {
      setLoading(false);
      onSyncComplete?.();
    }
  };

  return (
    <Box sx={{ mb: 2 }}>
      <FormControl>
        <FormLabel>Data sync ({workerInfo?.worker})</FormLabel>
        <MultiSelect
          label="Entities"
          values={SYNCERS?.sort()}
          selectedValues={syncArgs.entities || []}
          setSelectedValues={(entities) =>
            setSyncArgs({
              ...syncArgs,
              entities,
            })
          }
          sx={{ minWidth: 200, maxWidth: 266, mt: 1 }}
        />
        {syncArgs?.entities?.includes('compGrids') && (
          <Autocomplete
            multiple
            sx={{ my: 1 }}
            options={compGrids?.map((r) => ({
              label: r.name,
              id: r.id,
            }))}
            getOptionLabel={(option) => option.label}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            value={compGrids?.filter((grid) =>
              (syncArgs.compGridIds ?? []).includes(grid.id)
            )}
            onChange={(_event, newValue) => {
              setSyncArgs({
                ...syncArgs,
                compGridIds: newValue?.map((grid) => grid.id),
              });
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Comp grids"
                placeholder="Select comp grids"
              />
            )}
          />
        )}
        {(isTransGlobal ||
          isAgencyIntegrator ||
          isSmartOffice ||
          isMyAdvisorGrids) && (
          <FormControlLabel
            control={
              <Checkbox
                onChange={(e) => {
                  setSyncArgs({
                    ...syncArgs,
                    isFullSync: e.target.checked,
                  });
                }}
              />
            }
            label="Full run"
          />
        )}
        {isAgencyIntegrator && (
          <>
            <FormControlLabel
              control={
                <Checkbox
                  checked={showPolicyNumbers}
                  onChange={(e) => {
                    setShowPolicyNumbers(e.target.checked);
                    if (!e.target.checked) {
                      setSyncArgs({
                        ...syncArgs,
                        policyNumbers: [],
                      });
                    }
                  }}
                />
              }
              label="Sync with policy numbers"
            />
            {showPolicyNumbers && (
              <TextField
                label="Policy numbers"
                placeholder="Enter policy numbers separated by new lines or commas"
                multiline
                sx={{ my: 1 }}
                maxRows={5}
                onChange={(e) => {
                  const policyNumbers = e.target.value
                    .split(/[\n,]/g)
                    .map((num) => num.trim())
                    .map((num) => num.replace(/'|"/g, ''))
                    .filter((r) => r);
                  setSyncArgs({
                    ...syncArgs,
                    policyNumbers,
                  });
                }}
              />
            )}
          </>
        )}

        {isTrailStoneAccount && (
          <>
            <MultiSelect
              label="Filter policy"
              values={policyStates?.sort()}
              sx={{ minWidth: 200, maxWidth: 266, my: 1 }}
              selectedValues={syncArgs.policyStates || []}
              setSelectedValues={(states) => {
                setSyncArgs({
                  ...syncArgs,
                  policyStates: states.filter((s) => s),
                });
              }}
            />
            <FormControlLabel
              label="Sync date range"
              control={<Checkbox />}
              checked={showSyncDateRange}
              onClick={(_e) => setShowSyncDateRange(!showSyncDateRange)}
            />
            {showSyncDateRange && (
              <Box>
                <BasicDateRangePicker
                  range={{
                    startDate: syncArgs?.startDate
                      ? dayjs.utc(syncArgs.startDate)
                      : null,
                    startDateLabel: 'Start date',
                    endDate: syncArgs?.endDate
                      ? dayjs.utc(syncArgs.endDate)
                      : null,
                    endDateLabel: 'End date',
                  }}
                  onChange={({ startDate, endDate }) => {
                    setSyncArgs({
                      ...syncArgs,
                      startDate: startDate
                        ? dayjs.isDayjs(startDate)
                          ? startDate.toDate()
                          : new Date(startDate)
                        : null,
                      endDate: endDate
                        ? dayjs.isDayjs(endDate)
                          ? endDate.toDate()
                          : new Date(endDate)
                        : null,
                    });
                  }}
                  my={0.5}
                />
              </Box>
            )}
          </>
        )}
        <AdminCard showIcon iconPosition="right">
          <FormControlLabel
            control={
              <Checkbox
                checked={!syncArgs.sync}
                onChange={(e) =>
                  setSyncArgs({
                    ...syncArgs,
                    sync: !e.target.checked,
                  })
                }
              />
            }
            label="Use workers"
          />
        </AdminCard>
        <Button loading={loading} onClick={handleSync} variant="contained">
          Sync
        </Button>
      </FormControl>
    </Box>
  );
};
