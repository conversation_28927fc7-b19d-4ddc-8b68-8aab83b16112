import { render, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import { vi, type Mocked } from 'vitest';

import authentication from '@/services/authentication';
import EmailVerificationDialog from '@/components/EmailVerificationDialog';

vi.mock('@/services/authentication', () => ({
  default: {
    sendEmailVerification: vi.fn(),
  },
}));

describe('SignUpDialog', () => {
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let mockDialogProps;

  const theme = createTheme({});

  const mockAuth = authentication as Mocked<typeof authentication>;

  beforeEach(() => {
    mockDialogProps = {
      open: true,
      onClose: vi.fn(),
      email: '<EMAIL>',
    };

    mockAuth.sendEmailVerification.mockImplementationOnce(
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (): Promise<any> => Promise.resolve({})
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const renderComponent = () =>
    render(
      <ThemeProvider theme={theme}>
        <EmailVerificationDialog dialogProps={mockDialogProps} theme={theme} />
      </ThemeProvider>
    );

  describe('Rendering and Initial State', () => {
    it('renders without crashing', () => {
      const { getByText } = renderComponent();
      expect(getByText('Verify your e-mail address')).toBeInTheDocument();
    });

    it('renders the email address', () => {
      const { getByText } = renderComponent();
      expect(getByText(/<EMAIL>/i)).toBeInTheDocument();
    });
  });

  describe('Email Verification', () => {
    it('calls sendEmailVerification when button is clicked', async () => {
      const { getByText } = renderComponent();
      fireEvent.click(getByText('Send e-mail verification'));
      await waitFor(() =>
        expect(mockAuth.sendEmailVerification).toHaveBeenCalledTimes(1)
      );
    });

    it('displays snackbar message when email verification is sent', async () => {
      const { getByText } = renderComponent();
      fireEvent.click(getByText('Send e-mail verification'));
    });
  });
});
