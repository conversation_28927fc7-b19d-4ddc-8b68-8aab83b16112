import { CalculateOutlined } from '@mui/icons-material';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';

import API from '@/services/API';

const BulkReceivablesCalc = ({
  selectedData,
  setLoadingConfig,
  showSnackbar,
}) => {
  const receivableCalcPoster = API.getMutation(
    'receivables-schedule/calc',
    'POST'
  );

  const handleBulkReceivablesCalc = async () => {
    try {
      setLoadingConfig({
        loading: true,
        message: 'Receivables calc...',
      });

      const reportIds = selectedData.map((r) => r.id);

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const calculateReceivables: any[] =
        await receivableCalcPoster.mutateAsync({
          report_ids: reportIds,
        });

      const successMsg: string[] = [];
      const errorsMsg: string[] = [];

      for (const calculateReceivable of calculateReceivables) {
        if (calculateReceivable.success) {
          successMsg.push(`report ${calculateReceivable?.result?.id}`);
        } else {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          errorsMsg.push((calculateReceivable as any)?.message);
        }
      }

      if (successMsg.length > 0) {
        showSnackbar(
          `Receivables calculated: ${successMsg.join(', ')}`,
          'success'
        );
      }
      if (errorsMsg.length > 0) {
        showSnackbar(
          `Receivables calc failed: ${errorsMsg.join(', ')}`,
          'error'
        );
      }
    } catch (error) {
      console.error('Error in receivables calculation:', error);
      showSnackbar(`Receivables calc failed: ${error}`, 'error');
    } finally {
      setLoadingConfig({
        loading: false,
        message: '',
      });
    }
  };

  return (
    <Button onClick={handleBulkReceivablesCalc} color="primary">
      <Tooltip title="Receivables calc">
        <CalculateOutlined />
      </Tooltip>
    </Button>
  );
};

export default BulkReceivablesCalc;
