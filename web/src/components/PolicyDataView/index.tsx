import { getEnvVariable } from '@/env';
import { useMediaQuery } from '@mui/material';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { AccountIds, WorkerNames } from 'common/constants';
import type { SyncParamsDTO } from 'common/dto/data_processing/sync';
import { useContext, useEffect, useState } from 'react';
import { Navigate, useLocation, useSearchParams } from 'react-router-dom';

import { SyncedEntity } from '@/common/SyncedEntity';
import { SyncEndAdornment } from '@/common/SyncEndAdornment';
import EnhancedDataView from '@/components/organisms/EnhancedDataView';
import BulkReceivablesCalc from '@/components/PolicyDataView/BulkReceivablesCalc';
import { LoadingContext } from '@/contexts/LoadingContext';
import { ReconciliationConfirmProvider } from '@/contexts/ReconciliationConfirmProvider';
import useSnackbar from '@/contexts/useSnackbar';
import { useSyncedFieldsNew } from '@/contexts/useSyncedFields';
import { useExportOptions } from '@/hooks/useExportOptions';
import { useFeatureFlags } from '@/hooks/useFeatureFlags';
import API from '@/services/API';
import Reports from '@/services/Reports';
import { useAccountStore, useRoleStore } from '@/store';
import { Roles } from '@/types';
import { useAdvanceSchedule } from '@/hooks/useAdvanceSchedule';

const PolicyDataView = ({ reportId = null }) => {
  const [searchParams, _] = useSearchParams();
  const location = useLocation();
  const prefillData = location.state?.prefillData;
  const { handleAdvanceSchedule, handleStopAdvanceSchedule } =
    useAdvanceSchedule();

  const { data: settingsData, isFetched: isFetchedUserSettings } =
    API.getUser();
  const { data: accountSettings, isFetched: isFetchedAccountSettings } =
    API.getBasicQuery(`accounts/settings`);
  const { selectedAccount } = useAccountStore();
  const { userRole } = useRoleStore();
  const exportOptions = useExportOptions('policies-report');
  const mode = selectedAccount?.accountMode;
  const syncPolicy = API.getMutation(
    'data_processing/sync/nowcerts/policy',
    'POST'
  );
  const { isFeatureFlagEnabled } = useFeatureFlags();

  const syncService = API.getMutation('data_processing/sync', 'POST');
  const receivableScheduleService = API.getMutation(
    'receivables-schedule/calc',
    'POST'
  );

  const [selectedData, setSelectedData] = useState<
    { id: number; policy_id: string }[]
  >([]);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [filterCacheKey, setFilterCacheKey] = useState<any>([]);
  const queryClient = useQueryClient();
  const { workerSyncedFields, isSyncedField } = useSyncedFieldsNew();

  const { showSnackbar } = useSnackbar();
  const { setLoadingConfig } = useContext(LoadingContext);

  const BulkReceivablesCalcActions = () => (
    <BulkReceivablesCalc
      selectedData={selectedData}
      setLoadingConfig={setLoadingConfig}
      showSnackbar={showSnackbar}
    />
  );

  useEffect(() => {
    // Cancel previous query when searchParams or selectedAccount changes, avoid previous query from returning stale data and causing UI mismatch
    setFilterCacheKey((prev) => {
      const cacheKey = [
        selectedAccount?.accountId,
        // Filter out pagination & order params from searchParams
        new URLSearchParams(
          Array.from(searchParams.entries()).filter(
            ([key]) => !['limit', 'page', 'sort', 'orderBy'].includes(key)
          )
        ).toString(),
      ];
      if (prev.length > 0 && prev.join() !== cacheKey.join()) {
        queryClient.cancelQueries({ queryKey: prev });
      }
      return cacheKey;
    });
  }, [queryClient, searchParams, selectedAccount?.accountId]);

  const { data: filters } = useQuery({
    queryKey: filterCacheKey,
    queryFn: async ({ signal }) => {
      const url = `${getEnvVariable('API')}/api/report_data/filters?${new URLSearchParams(searchParams).toString()}`;
      try {
        const res = await fetch(url, {
          method: 'GET',
          headers: await API.getHeaders(),
          signal,
        });
        return res.json();
      } catch (err) {
        if ((err as Error)?.name !== 'AbortError') {
          console.error(err);
        }
        return {};
      }
    },
  });

  const reports = new Reports(mode, {
    account_id: selectedAccount?.accountId,
    isFeatureFlagEnabled,
  });
  const isMobile = useMediaQuery('(max-width:600px)');

  const pageSettingFields = isMobile
    ? accountSettings?.pages_settings?.policies?.outstandingMobileFields
    : accountSettings?.pages_settings?.policies?.fields;
  const newFields = (pageSettingFields ?? []).reduce((acc, cur) => {
    acc[cur] = {
      ...reports.fields[cur],
    };
    return acc;
  }, {});
  if (Object.keys(newFields).length > 0) reports.fields = newFields;

  if (accountSettings?.pages_settings?.policies?.page_label) {
    reports.label = accountSettings?.pages_settings?.policies?.page_label;
  }

  if (
    isFetchedAccountSettings &&
    accountSettings?.pages_settings?.policies?.show_page === false
  ) {
    return (
      // TODO: Remove navigate after figuring out how to handle this in router
      <Navigate to="/settings" />
      // <Box sx={{ textAlign: 'center', mt: 6, width: '100%' }}>
      //   <Typography variant="h5">No results</Typography>
      // </Box>
    );
  }

  const isTrailStoneAccount =
    AccountIds.TRAILSTONE === selectedAccount?.accountId;
  const hasAgencyIntegratorWorker =
    !!workerSyncedFields?.[WorkerNames.AgencyIntegratorWorker];

  const showBulkSync = isTrailStoneAccount || hasAgencyIntegratorWorker;

  for (const key in reports.fields) {
    const field = reports.fields[key];
    field.readOnly =
      field.readOnly ||
      ((data) => {
        const syncedFields =
          workerSyncedFields?.[data?.sync_worker]?.report_data;
        if ((syncedFields || []).includes(field.id)) {
          return isSyncedField(data, syncedFields, field.id, data.config);
        }
        return false;
      });
    field.endAdornment = (data, field, setNewData) => (
      <SyncEndAdornment
        syncedFields={workerSyncedFields?.[data?.sync_worker]?.report_data}
        syncId={data?.sync_id}
        fieldId={field?.id}
        data={data}
        fieldType={field.type}
        onChange={(newOverrideFields) => {
          setNewData({
            ...data,
            config: {
              ...(data.config || {}),
              overrideFields: newOverrideFields,
            },
          });
        }}
      />
    );
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const onBulkSync = async (params: any) => {
    try {
      let result = null;
      if (isTrailStoneAccount) {
        result = await syncPolicy.mutateAsync(params);
      } else if (hasAgencyIntegratorWorker) {
        const lookupData = new Map(selectedData.map((item) => [item.id, item]));
        const policyNumbers = params.ids.map(
          (id) => lookupData.get(id)?.policy_id
        );
        if (!policyNumbers.length) {
          return;
        }
        result = await syncService.mutateAsync({
          entities: ['policies'],
          policyNumbers: policyNumbers,
          sync: true,
          worker: WorkerNames.AgencyIntegratorWorker,
        } as SyncParamsDTO);
      }
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if (!(result as any)?.success) {
        showSnackbar(
          `Sync failed: ${
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            (result as any)?.message
          }`,
          'error'
        );
      } else {
        showSnackbar('Sync completed', 'success');
      }
    } catch (error) {
      console.error(error);
      showSnackbar(`Sync failed: ${(error as Error).message}`, 'error');
    }
  };

  return settingsData && isFetchedUserSettings && isFetchedAccountSettings ? (
    <ReconciliationConfirmProvider mode={mode}>
      <EnhancedDataView
        enableRowCreateCopyAction
        enableRowDeleteAction
        reportId={reportId}
        dataSpec={reports}
        enableBulkEditCsv
        bulkAdd
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        exportOptions={exportOptions as any}
        outstandingMobileFields={
          accountSettings?.pages_settings?.policies?.outstandingMobileFields
        }
        defaultData={prefillData}
        filters={filters}
        enableSaves
        showTotals
        onBulkSync={
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (showBulkSync ? (ids) => onBulkSync({ ids }) : undefined) as any
        }
        setSelectedData={(data) => setSelectedData(data)}
        actions={
          [
            ...(reports.actions ?? []),
            ...[
              {
                type: isTrailStoneAccount ? 'iconButton' : 'icon',
                label: 'Sync',
                icon: (
                  <SyncedEntity
                    isSynced={true}
                    disabled={!isTrailStoneAccount}
                  />
                ),
                enabled: (row) => !!row.sync_id,
                onClick: isTrailStoneAccount
                  ? async (row, e) => {
                      e?.stopPropagation();
                      e?.preventDefault();
                      if (isTrailStoneAccount) {
                        await onBulkSync({ syncId: row.sync_id });
                      }
                    }
                  : // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    () => {},
              },
              {
                id: 'advance-schedule',
                label: 'Advance schedule',
                onClick: (row) => handleAdvanceSchedule(row.id),
                enabled: (row) => row?.config?.advanced_commission_schedules,
              },
              {
                id: 'stop advance-schedule',
                label: 'Stop advance schedule',
                onClick: (row) => handleStopAdvanceSchedule(row.id),
                enabled: (row) => row?.config?.advanced_commission_schedules,
              },
              {
                id: 'receivable',
                label: 'Calculate receivables',
                onClick: async (row) => {
                  try {
                    const result = await receivableScheduleService.mutateAsync({
                      report_id: row.id,
                    });
                    if (result.success) {
                      showSnackbar('Receivables calculated', 'success');
                    } else {
                      showSnackbar(result.message, 'error');
                    }
                    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  } catch (err: any) {
                    showSnackbar(err.message, 'error');
                  }
                },
              },
            ],
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          ] as any
        }
        actionsEnabled={
          ((row) => {
            return !!row.sync_id || reports.actions.length > 0;
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          }) as any
        }
        // TODO (frank.santillan): Move to settings after we migrate reconciliation / commissions / policies to pages_settings.
        readOnly={userRole === Roles.PRODUCER}
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        bulkActions={[BulkReceivablesCalcActions] as any}
      />
    </ReconciliationConfirmProvider>
  ) : null;
};

export default PolicyDataView;
