import { useEffect, useRef } from 'react';
import { AccountConfigsTypes } from 'common/constants/accounts_configs';

import {
  FeatureFlagsEditor,
  DataSyncEditor,
  DefaultEditor,
} from './components';
import type { ValueEditorProps } from './types';

const ValueEditor = ({ field, row, setter }: ValueEditorProps) => {
  const prevTypeRef = useRef<string | null>(null);

  // Set default values for specific types
  useEffect(() => {
    // Clear data when type changes
    if (prevTypeRef.current !== null && prevTypeRef.current !== row.type) {
      setter({ ...row, [field.id]: undefined });
    }
    prevTypeRef.current = row.type;

    if (row.type === AccountConfigsTypes.SESSION && !row[field.id]) {
      const defaultValue = { session: 60 };
      setter({ ...row, [field.id]: defaultValue });
    }
    if (row.type === AccountConfigsTypes.FEATURE_FLAGS && !row[field.id]) {
      setter({ ...row, [field.id]: {} });
    }
    if (row.type === AccountConfigsTypes.DATA_SYNC && !row[field.id]) {
      setter({
        ...row,
        [field.id]: { worker: '', credentials: {}, entities: [] },
      });
    }
  }, [row.type, row, field.id, setter]);

  switch (row.type) {
    case AccountConfigsTypes.FEATURE_FLAGS:
      return <FeatureFlagsEditor field={field} row={row} setter={setter} />;
    case AccountConfigsTypes.DATA_SYNC:
      return <DataSyncEditor field={field} row={row} setter={setter} />;
    default:
      return <DefaultEditor field={field} row={row} setter={setter} />;
  }
};

export default ValueEditor;
