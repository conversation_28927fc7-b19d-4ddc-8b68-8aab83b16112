import { getEnvVariable } from '@/env';
import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { useState, useEffect } from 'react';

import API from '@/services/API';
import type {
  ValueEditorProps,
  CredentialField,
  DataSyncValue,
  IntegrationConfig,
} from '../types';
import { INTEGRATION_CONFIGS } from '../config';

const DataSyncEditor = ({ field, row, setter }: ValueEditorProps) => {
  const [workersEntities, setWorkersEntities] = useState<
    Record<string, string[]>
  >({});
  const [loadingEntities, setLoadingEntities] = useState(false);

  // Fetch available entities for workers
  useEffect(() => {
    const fetchWorkersEntities = async () => {
      try {
        setLoadingEntities(true);
        const response = await fetch(
          `${getEnvVariable('API')}/api/data_processing/sync/workers-entities`,
          {
            method: 'GET',
            headers: await API.getHeaders(),
          }
        );
        if (response.ok) {
          const data = await response.json();
          setWorkersEntities(data);
        }
      } catch (error) {
        console.error('Failed to fetch workers entities:', error);
      } finally {
        setLoadingEntities(false);
      }
    };

    fetchWorkersEntities();
  }, []);

  const getIntegrationConfig = (
    worker: string
  ): IntegrationConfig | undefined => {
    return INTEGRATION_CONFIGS.find((config) => config.value === worker);
  };

  const getDefaultCredentials = (worker: string): Record<string, string> => {
    const config = getIntegrationConfig(worker);
    return config?.defaultValues || {};
  };

  const getCredentialFields = (worker: string): CredentialField[] => {
    const config = getIntegrationConfig(worker);
    return config?.credentials || [];
  };

  const handleDataSyncChange = (worker: string) => {
    const newValue: DataSyncValue = {
      worker,
      credentials: getDefaultCredentials(worker),
      entities: workersEntities[worker] || [],
    };

    setter({
      ...row,
      [field.id]: newValue,
    });
  };

  const handleEntitiesChange = (entity: string, checked: boolean) => {
    const currentValue = row[field.id] || {};
    const currentEntities = currentValue.entities || [];

    const newEntities = checked
      ? [...currentEntities, entity]
      : currentEntities.filter((e) => e !== entity);

    const newValue = {
      ...currentValue,
      entities: newEntities,
    };

    setter({
      ...row,
      [field.id]: newValue,
    });
  };

  const handleCredentialsChange = (
    credentialKey: string,
    credentialValue: string
  ) => {
    const currentValue = row[field.id] || {};
    const newValue = {
      ...currentValue,
      credentials: {
        ...currentValue.credentials,
        [credentialKey]: credentialValue,
      },
    };

    setter({
      ...row,
      [field.id]: newValue,
    });
  };

  const value = row[field.id] || {};
  const selectedWorker = value.worker || '';
  const credentials = value.credentials || {};
  const selectedEntities = value.entities || [];
  const credentialFields = getCredentialFields(selectedWorker);
  const availableEntities = workersEntities[selectedWorker] || [];

  return (
    <Box>
      <Box sx={{ mb: 2 }}>
        <strong>Data sync configuration</strong>
      </Box>
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>Integration type</InputLabel>
        <Select
          value={selectedWorker}
          label="Integration type"
          onChange={(e) => handleDataSyncChange(e.target.value)}
        >
          {INTEGRATION_CONFIGS.map((config) => (
            <MenuItem key={config.value} value={config.value}>
              {config.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {selectedWorker && availableEntities.length > 0 && (
        <Box sx={{ mb: 2 }}>
          <FormLabel component="legend" sx={{ mb: 1, fontWeight: 'medium' }}>
            Entities to sync:
          </FormLabel>
          {loadingEntities ? (
            <Box>Loading entities...</Box>
          ) : (
            <FormGroup>
              {availableEntities.map((entity) => (
                <FormControlLabel
                  key={entity}
                  control={
                    <Checkbox
                      checked={selectedEntities.includes(entity)}
                      onChange={(e) =>
                        handleEntitiesChange(entity, e.target.checked)
                      }
                    />
                  }
                  label={entity}
                />
              ))}
            </FormGroup>
          )}
        </Box>
      )}

      {selectedWorker && credentialFields.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Box sx={{ mb: 1, fontWeight: 'medium' }}>
            Credentials for{' '}
            {getIntegrationConfig(selectedWorker)?.label || selectedWorker}:
          </Box>
          {credentialFields.map((credField) => (
            <TextField
              key={credField.key}
              fullWidth
              size="small"
              label={credField.label}
              value={credentials[credField.key] || ''}
              onChange={(e) =>
                handleCredentialsChange(credField.key, e.target.value)
              }
              required={credField.required}
              sx={{ mb: 1 }}
              type={credField.key === 'password' ? 'password' : 'text'}
            />
          ))}
        </Box>
      )}
    </Box>
  );
};

export default DataSyncEditor;
