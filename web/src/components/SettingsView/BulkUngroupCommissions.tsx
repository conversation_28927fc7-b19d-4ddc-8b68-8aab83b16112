import {
  Box,
  FormControl,
  FormLabel,
  Autocomplete,
  TextField,
  Chip,
  Button,
} from '@mui/material';
import type { BulkUngroupingDTO } from 'common/dto/data_processing/grouping';
import { useCallback, useState } from 'react';
import dayjs from 'dayjs';

import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import BasicDateRangePicker from '@/common/BasicDateRangePicker';
import { getEnvVariable } from '@/env';

const bulkUngroupCommissions = async (args: BulkUngroupingDTO) => {
  const res = await fetch(
    `${getEnvVariable('API')}/api/data_processing/grouping/bulkUnGrouping`,
    {
      method: 'POST',
      headers: await API.getHeaders(),
      body: JSON.stringify(args),
    }
  );
  const resJson = await res.json();

  if (res.status >= 400) {
    throw new Error(resJson.message || 'Failed to bulk ungroup commissions');
  }

  return resJson;
};

const BulkUngroupCommissions = () => {
  const [loading, setLoading] = useState(false);
  const [bulkUngroupArgs, setBulkUngroupArgs] = useState<BulkUngroupingDTO>({
    startDate: null,
    endDate: null,
    carriers: [],
    isSync: true,
  });

  const { showSnackbar } = useSnackbar();

  // Get available carriers/paying entities
  const { data: _payingEntities } = API.getBasicQuery(
    'statement_data/carrier_name'
  );
  const payingEntities = _payingEntities?.values ?? [];

  const onBulkUngroupArgsChange = useCallback(
    (data: Partial<BulkUngroupingDTO>) => {
      setBulkUngroupArgs((prev) => ({
        ...prev,
        ...data,
      }));
    },
    []
  );

  const handleBulkUngroup = async () => {
    // Validate required fields
    if (!bulkUngroupArgs.startDate || !bulkUngroupArgs.endDate) {
      showSnackbar('Start date and end date are required', 'error');
      return;
    }

    try {
      setLoading(true);
      const result = await bulkUngroupCommissions(bulkUngroupArgs);

      if (result.success) {
        showSnackbar(
          result.message ||
            `Successfully ungrouped ${result.ungroupedCount || 0} commission lines`,
          'success'
        );
      } else {
        showSnackbar(
          result.message || 'Error bulk ungrouping commissions',
          'error'
        );
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      showSnackbar(
        errorMessage || 'Error bulk ungrouping commissions',
        'error'
      );
    } finally {
      setLoading(false);
    }
  };

  // Check if form is valid
  const isFormValid = bulkUngroupArgs.startDate && bulkUngroupArgs.endDate;

  return (
    <Box>
      <FormControl>
        <FormLabel id="bulk-ungroup-commissions">
          Bulk ungroup commissions
        </FormLabel>

        <Box sx={{ mt: 2 }}>
          <FormLabel>Processing date range (required) *</FormLabel>
          <BasicDateRangePicker
            range={{
              startDate: bulkUngroupArgs.startDate
                ? dayjs.utc(bulkUngroupArgs.startDate)
                : null,
              startDateLabel: 'Start date *',
              endDate: bulkUngroupArgs.endDate
                ? dayjs.utc(bulkUngroupArgs.endDate)
                : null,
              endDateLabel: 'End date *',
            }}
            onChange={({ startDate, endDate }) => {
              onBulkUngroupArgsChange({
                startDate: startDate
                  ? dayjs.isDayjs(startDate)
                    ? startDate.toDate()
                    : new Date(startDate)
                  : null,
                endDate: endDate
                  ? dayjs.isDayjs(endDate)
                    ? endDate.toDate()
                    : new Date(endDate)
                  : null,
              });
            }}
            mt={1}
          />
        </Box>

        <Box sx={{ mt: 2 }}>
          <Autocomplete
            multiple
            freeSolo
            options={payingEntities}
            value={bulkUngroupArgs.carriers || []}
            onChange={(_, newValue) => {
              onBulkUngroupArgsChange({
                carriers: newValue,
              });
            }}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option}
                  {...getTagProps({ index })}
                  key={option}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                label="Carriers (optional)"
                placeholder="Select or type carrier names"
                helperText="You can select from the list or type custom carrier names"
              />
            )}
            sx={{ width: 400, mt: 1 }}
            filterSelectedOptions
          />
        </Box>

        <Box sx={{ mt: 2 }}>
          <Button
            loading={loading}
            onClick={handleBulkUngroup}
            variant="contained"
            color="warning"
            sx={{ minWidth: 200 }}
            disabled={!isFormValid}
          >
            Bulk Ungroup Commissions
          </Button>
        </Box>
      </FormControl>
    </Box>
  );
};

export default BulkUngroupCommissions;
