import { getEnvVariable } from '@/env';
import { useMutation, useQuery } from '@tanstack/react-query';
import axios from 'axios';

import type { InviteFormValues } from './validator';

const Prefix = `${getEnvVariable('API')}/api`;
const API_PATHS = {
  invite_new_user: `${Prefix}/users/invite_new_user`,
  agentList: `${Prefix}/contacts/contacts_without_user`,
  roles: `${Prefix}/roles`,
};

const inviteNewUser = async (
  userData: InviteFormValues & { accountId: string; accountName: string }
) => {
  const response = await axios.post(API_PATHS.invite_new_user, userData);
  return response.data;
};

export const useInviteUser = () => {
  return useMutation({
    mutationKey: [API_PATHS.invite_new_user],
    mutationFn: inviteNewUser,
  });
};

export const useAgentList = (enabled = true) => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  return useQuery<any[]>({
    queryKey: [API_PATHS.agentList],
    queryFn: () => axios.get(API_PATHS.agentList).then((res) => res.data),
    enabled,
  });
};

export const useRoles = () => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  return useQuery<any[]>({
    queryKey: [API_PATHS.roles],
    queryFn: () => axios.get(API_PATHS.roles).then((res) => res.data),
  });
};
