import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import { ThemeProvider } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { QueryParamProvider } from 'use-query-params';
import { ReactRouter6Adapter } from 'use-query-params/adapters/react-router-6';

import authentication, { INVITE_CODE } from '@/services/authentication';
import { SignInUp } from './SignInUp';
import type { Mock } from 'vitest';

vi.mock('@/services/authentication');

const mockedNavigate = vi.fn();
vi.mock('react-router-dom', async (importOriginal) => {
  const actual = await importOriginal<typeof import('react-router-dom')>();
  return {
    ...actual,
    useNavigate: () => mockedNavigate,
  };
});

describe('SignInUp', () => {
  const theme = createTheme({});

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderComponent = (initialEntries = ['/']) => {
    return render(
      <ThemeProvider theme={theme}>
        <MemoryRouter initialEntries={initialEntries}>
          <QueryParamProvider
            adapter={ReactRouter6Adapter}
            options={{ removeDefaultsFromUrl: true }}
          >
            <Routes>
              <Route path="/" element={<SignInUp />} />
            </Routes>
          </QueryParamProvider>
        </MemoryRouter>
      </ThemeProvider>
    );
  };

  describe('SignIn View', () => {
    it('Given the component is rendered without query parameters, should display the sign in view by default', () => {
      renderComponent();
      expect(
        screen.getByRole('button', { name: /send sign-in link/i })
      ).toBeInTheDocument();
    });

    it('Given an email is provided as a query parameter, should pre-fill the email input field', () => {
      renderComponent(['/?email=<EMAIL>']);
      const emailInput = screen.getByLabelText(
        /email address/i
      ) as HTMLInputElement;
      expect(emailInput.value).toBe('<EMAIL>');
    });

    it(`Given the user is on the sign in view, when the "Don't have an account?" link is clicked, should switch to the sign up view`, async () => {
      renderComponent();
      fireEvent.click(
        screen.getByRole('button', { name: /don't have an account/i })
      );
      await waitFor(() => {
        expect(
          screen.getByLabelText(/enter your invite code/i)
        ).toBeInTheDocument();
      });
    });

    it('Given a valid email and password, when the sign in button is clicked, should call the signIn service and navigate on success', async () => {
      (authentication.signIn as Mock).mockResolvedValue({
        email: '<EMAIL>',
      });
      renderComponent();

      fireEvent.change(screen.getByLabelText(/email address/i), {
        target: { value: '<EMAIL>' },
      });
      fireEvent.change(screen.getByLabelText(/password/i), {
        target: { value: 'password123' },
      });
      fireEvent.click(screen.getByRole('button', { name: /^sign in$/i }));

      await waitFor(() => {
        expect(authentication.signIn).toHaveBeenCalledWith(
          '<EMAIL>',
          'password123'
        );
        expect(mockedNavigate).toHaveBeenCalledWith('/');
      });
    });
  });

  describe('SignUp View', () => {
    it('Given the loginType query parameter is "signUp", should display the sign up view', () => {
      renderComponent(['/?loginType=signUp']);
      expect(
        screen.getByLabelText(/enter your invite code/i)
      ).toBeInTheDocument();
    });

    it('Given an invalid invite code is submitted, should display an error message', async () => {
      renderComponent(['/?loginType=signUp']);
      fireEvent.change(screen.getByLabelText(/enter your invite code/i), {
        target: { value: 'WRONGCODE' },
      });
      fireEvent.click(screen.getByRole('button', { name: /continue/i }));

      await waitFor(() => {
        expect(
          screen.getByText(/we can't create an account for you at the moment/i)
        ).toBeInTheDocument();
      });
    });

    it('Given a valid invite code is submitted, should display the full sign up form', async () => {
      renderComponent(['/?loginType=signUp']);
      fireEvent.change(screen.getByLabelText(/enter your invite code/i), {
        target: { value: INVITE_CODE },
      });
      fireEvent.click(screen.getByRole('button', { name: /continue/i }));

      await waitFor(() => {
        expect(
          screen.getByRole('button', { name: /sign up with google/i })
        ).toBeInTheDocument();
        expect(
          screen.getByRole('button', { name: /^sign up$/i })
        ).toBeInTheDocument();
      });
    });

    it('Given the sign up form is submitted with valid data, should call the signUp service and display a success message', async () => {
      (
        authentication.signUpWithEmailAddressAndPassword as Mock
      ).mockResolvedValue(undefined);
      renderComponent([`/?loginType=signUp&inviteCode=${INVITE_CODE}`]);

      screen.debug();
      fireEvent.change(screen.getByLabelText(/^Email address/i), {
        target: { value: '<EMAIL>' },
      });
      fireEvent.change(screen.getByLabelText(/confirm email address/i), {
        target: { value: '<EMAIL>' },
      });
      fireEvent.change(screen.getByLabelText(/^Password/i), {
        target: { value: 'Password123!' },
      });
      fireEvent.change(screen.getByLabelText(/confirm password/i), {
        target: { value: 'Password123!' },
      });

      fireEvent.click(screen.getByRole('button', { name: /^sign up$/i }));

      await waitFor(() => {
        expect(
          authentication.signUpWithEmailAddressAndPassword
        ).toHaveBeenCalledWith({
          emailAddress: '<EMAIL>',
          password: 'Password123!',
        });
        expect(screen.getByText(/signed in/i)).toBeInTheDocument();
      });
    });

    it('Given the user is on the sign up view, when the "Already have an account?" link is clicked, should switch to the sign in view', async () => {
      renderComponent(['/?loginType=signUp']);
      fireEvent.click(
        screen.getByRole('button', { name: /already have an account/i })
      );

      await waitFor(() => {
        expect(
          screen.getByRole('button', { name: /send sign-in link/i })
        ).toBeInTheDocument();
      });
    });
  });
});
