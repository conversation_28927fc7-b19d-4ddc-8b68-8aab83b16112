import { DeleteOutlined } from '@mui/icons-material';
import {
  Box,
  Button,
  CircularProgress,
  FormControl,
  IconButton,
} from '@mui/material';
import { GridLegacy as Grid } from '@mui/material';
import { CompProfileMatcherState } from 'common/globalTypes';
import { useEffect, useState } from 'react';

import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import CompProfileMatcherResultDisplay from '@/components/ToolsPage/CompProfileMatcherTools/CompProfileMatcherResultDisplay';
import type {
  Agent,
  CompProfile,
} from '@/components/ToolsPage/CompProfileMatcherTools/CompProfileMatcherTool';

export interface MatchResult {
  state: CompProfileMatcherState;
  profileName?: string;
  rule?: string;
  message?: string;
  updateAt?: Date;
}

export type AgentCompProfileMatchResult = Record<
  string,
  Record<string, MatchResult>
>;

interface CompProfileMatcherRowProps {
  initialAgent: Agent;
  contacts: Agent[];
  compProfiles: CompProfile[];
  onRemove: () => void;
  handleRemoveAllData: (agentStrId: string, compProfileStrId: string) => void;
  isContactsLoading: boolean;
  isCompProfilesLoading: boolean;
  updateCompProfileMatchersArg: (
    agentStrId: string | undefined,
    compProfileStrId: string | undefined
  ) => void;
  matchCompProfile: (
    agentStrId: string,
    compProfileStrId: string
  ) => Promise<MatchResult>;
  matchAllResults: AgentCompProfileMatchResult;
  isLoadingAllResults: boolean;
}

const CompProfileMatcherRow: React.FC<CompProfileMatcherRowProps> = ({
  initialAgent,
  contacts,
  compProfiles,
  onRemove,
  handleRemoveAllData,
  isContactsLoading,
  isCompProfilesLoading,
  updateCompProfileMatchersArg,
  matchCompProfile,
  matchAllResults,
  isLoadingAllResults,
}) => {
  const [agent, setAgent] = useState<Agent | undefined>(
    contacts.find((c) => c.str_id === initialAgent.str_id) || initialAgent
  );
  const [compProfile, setCompProfile] = useState<CompProfile | undefined>(
    undefined
  );
  const [isLoadingResult, setIsLoadingResult] = useState<boolean>(false);
  const [matchResult, setMatchResult] = useState<MatchResult | undefined>(
    undefined
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    updateCompProfileMatchersArg(agent?.str_id, compProfile?.str_id);
    if (!agent || !compProfile) setMatchResult(undefined);
  }, [agent, compProfile, setMatchResult, updateCompProfileMatchersArg]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (
      !agent?.str_id ||
      !compProfile?.str_id ||
      !Object.keys(matchAllResults).length
    )
      return;

    const agentResults = matchAllResults[agent.str_id];
    const compProfileResult = agentResults?.[compProfile.str_id];

    if (compProfileResult) {
      setMatchResult(compProfileResult);
    }
  }, [matchAllResults, agent, compProfile, setMatchResult]);

  const handleAgentChange = (selected: { id: number }) => {
    setAgent(contacts.find((c) => c.id === selected.id));
  };

  const handleCompProfileChange = (selected: { id: number }) => {
    setCompProfile(compProfiles.find((cp) => cp.id === selected.id));
  };

  const handleRemoveRow = () => {
    onRemove();
    if (agent && compProfile)
      handleRemoveAllData(agent?.str_id, compProfile?.str_id);
  };

  const handleMatch = async () => {
    if (!agent || !compProfile) {
      setMatchResult({
        state: CompProfileMatcherState.UNMATCHED,
        message: 'Agent or comp profile not selected',
        updateAt: new Date(),
      });
      return;
    }

    setIsLoadingResult(true);
    try {
      const compProfileMatcherResponse = await matchCompProfile(
        agent.str_id,
        compProfile.str_id
      );
      setMatchResult(
        compProfileMatcherResponse[agent.str_id][compProfile.str_id]
      );
    } catch (error) {
      console.error('Error matching profile:', error);
      setMatchResult({
        state: CompProfileMatcherState.UNMATCHED,
        message: 'An unexpected error occurred.',
        updateAt: new Date(),
      });
    } finally {
      setIsLoadingResult(false);
    }
  };

  return (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={4} md={3}>
        <FormControl fullWidth>
          {isContactsLoading ? (
            <CircularProgress size={24} />
          ) : (
            <EnhancedSelect
              disabled={isLoadingResult || isLoadingAllResults}
              enableSearch
              value={agent}
              options={contacts ?? []}
              label="Agents"
              onChange={handleAgentChange}
              valueKey="id"
              labelKey="name"
            />
          )}
        </FormControl>
      </Grid>

      <Grid item xs={4} md={3}>
        <FormControl fullWidth>
          {isCompProfilesLoading ? (
            <CircularProgress size={24} />
          ) : (
            <EnhancedSelect
              disabled={isLoadingResult || isLoadingAllResults}
              enableSearch
              value={compProfile}
              options={compProfiles ?? []}
              label="Comp profiles"
              onChange={handleCompProfileChange}
              valueKey="id"
              labelKey="name"
            />
          )}
        </FormControl>
      </Grid>

      <Grid item>
        <Button
          variant="contained"
          color="primary"
          onClick={handleMatch}
          disabled={
            !agent?.str_id ||
            !compProfile?.str_id ||
            isContactsLoading ||
            isCompProfilesLoading ||
            isLoadingResult ||
            isLoadingAllResults
          }
        >
          Match
        </Button>
      </Grid>

      <Grid item>
        <IconButton
          onClick={handleRemoveRow}
          disabled={
            isContactsLoading ||
            isCompProfilesLoading ||
            isLoadingResult ||
            isLoadingAllResults
          }
        >
          <DeleteOutlined />
        </IconButton>
      </Grid>

      <Grid item xs>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            textOverflow: 'ellipsis',
          }}
        >
          {(!agent?.str_id || !compProfile?.str_id) && isLoadingAllResults && (
            <span style={{ color: 'red' }}>
              Agent or compensation profile not specified.
            </span>
          )}
          {(isLoadingAllResults || isLoadingResult) &&
            agent?.str_id &&
            compProfile?.str_id && <CircularProgress size={24} />}
          {matchResult && (
            <CompProfileMatcherResultDisplay
              matchResult={matchResult}
              initialExpanded={true}
            />
          )}
        </Box>
      </Grid>
    </Grid>
  );
};

export default CompProfileMatcherRow;
