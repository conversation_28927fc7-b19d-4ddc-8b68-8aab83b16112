import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>ir<PERSON><PERSON><PERSON>ress,
  <PERSON><PERSON>ield,
  Typography,
} from '@mui/material';
import { GridLegacy as Grid } from '@mui/material';
import Formatter from 'common/Formatter';
import type React from 'react';
import { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import axios from 'axios';

import CompProfileMatcherRow, {
  type AgentCompProfileMatchResult,
} from '@/components/ToolsPage/CompProfileMatcherTools/CompProfileMatcherRow';
import API from '@/services/API';
import { useAccountStore } from '@/store';
import { getEnvVariable } from '@/env';

export interface CommissionRecord {
  id: string;
}

export interface CompProfile {
  id: number;
  name: string;
  str_id: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  criteria?: Record<string, any>;
}

export interface Agent {
  id: number;
  name: string;
  str_id: string;
}

interface CompProfileMatcher {
  agentStrId: string;
  compProfileStrId: string;
}

const CompProfileMatcherTool: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { selectedAccount } = useAccountStore();
  const initialStatementId = searchParams.get('statement_id') || '';
  const initialAgents = searchParams.get('agents')?.split(',') || [];
  const [compProfileMatchersArg, setCompProfileMatchersArg] = useState<
    CompProfileMatcher[]
  >([]);

  const [matcherRows, setMatcherRows] = useState<Agent[]>([]);
  const [commissionId, setCommissionId] = useState(initialStatementId);
  const [matchAllResults, setMatchAllResults] =
    useState<AgentCompProfileMatchResult>({});
  const [isLoadingAllResults, setIsLoadingAllResults] =
    useState<boolean>(false);

  const { data: contactsData, isLoading: isLoadingContacts } =
    API.getBasicQuery(
      'contacts',
      `is_dynamic_select=true&account_id=${selectedAccount?.accountId}`
    );
  const { data: compProfilesData, isLoading: isLoadingCompProfiles } =
    API.getBasicQuery(
      'schedules/comp-profiles',
      `is_dynamic_select=true&account_id=${selectedAccount?.accountId}`
    );

  const contacts = useMemo(
    () =>
      (contactsData?.data ?? [])
        .map((contact) => ({
          id: contact.id,
          name: Formatter.contact(contact, {
            incl_email: false,
            account_id: selectedAccount?.accountId,
          }),
          str_id: contact.str_id,
        }))
        .sort((a, b) => a.name.localeCompare(b.name)),
    [contactsData?.data, selectedAccount?.accountId]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (
      contacts.length > 0 &&
      initialAgents.length > 0 &&
      !matcherRows.some((row: Agent) => row.name === '')
    ) {
      const filteredContacts = contacts.filter((contact) =>
        initialAgents.includes(contact.str_id)
      );

      const newMatcherRows = filteredContacts.map((contact) => ({
        id: contact.id,
        name: contact.name,
        str_id: contact.str_id,
      }));

      setMatcherRows((prevMatcherRows) => {
        const isEqual =
          prevMatcherRows.length === newMatcherRows.length &&
          prevMatcherRows.every(
            (row, index) =>
              row.id === newMatcherRows[index].id &&
              row.name === newMatcherRows[index].name &&
              row.str_id === newMatcherRows[index].str_id
          );

        return isEqual ? prevMatcherRows : newMatcherRows;
      });
    }
    /**
     * https://linear.app/fintary/issue/PLT-2290
     *
     * WARN SINCE:  May/2025
     * MISSED REFs:   'matcherRows'
     *
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contacts, initialAgents]);

  const handleAddRow = () => {
    setMatcherRows((prevRows) => [
      ...prevRows,
      { id: Date.now(), name: '', str_id: '' },
    ]);
  };

  const handleRemoveRow = (idToRemove: number) => {
    setMatcherRows((prevRows) =>
      prevRows.filter((row) => row.id !== idToRemove)
    );
  };

  const handleRemoveAllData = (
    agentStrId: string,
    compProfileStrId: string
  ) => {
    setCompProfileMatchersArg((prev) =>
      prev.filter(
        (matcher) =>
          matcher.agentStrId !== agentStrId ||
          matcher.compProfileStrId !== compProfileStrId
      )
    );
    setMatchAllResults((prev) => {
      const updatedResults = { ...prev };
      delete updatedResults[agentStrId]?.[compProfileStrId];
      return updatedResults;
    });
  };

  const handleCommissionIdChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setCommissionId(event.target.value);
  };

  const updateCompProfileMatchersArg = (
    agentStrId: string | undefined,
    compProfileStrId: string | undefined
  ) => {
    if (agentStrId && compProfileStrId) {
      if (
        !compProfileMatchersArg.some(
          (matcher) =>
            matcher.agentStrId === agentStrId &&
            matcher.compProfileStrId === compProfileStrId
        )
      ) {
        setCompProfileMatchersArg((prev) => [
          ...prev,
          { agentStrId, compProfileStrId },
        ]);
      }
    }
  };

  const sendCompProfileMatcherRequest = async (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    payload: Record<string, any>,
    endpoint: string
  ) => {
    try {
      const headers = await API.getHeaders();
      const response = await axios.post(
        `${getEnvVariable('API')}${endpoint}`,
        payload,
        { headers }
      );
      return response.data;
    } catch (error) {
      console.error(
        'Unexpected error while sending comp profile matcher request:',
        error
      );
      return null;
    }
  };

  const handleMatchCompProfile = async (
    agentStrId: string,
    compProfileStrId: string
  ) => {
    const payload = {
      commissionId: commissionId,
      compProfileMatchers: [
        {
          agentStrId,
          compProfileStrId,
        },
      ],
    };

    return sendCompProfileMatcherRequest(
      payload,
      '/api/admin/tools/comp-profile-matcher'
    );
  };

  const handleMatchAllCompProfile = async () => {
    setIsLoadingAllResults(true);
    const payload = {
      commissionId: commissionId,
      compProfileMatchers: compProfileMatchersArg,
    };

    const result = await sendCompProfileMatcherRequest(
      payload,
      '/api/admin/tools/comp-profile-matcher'
    );

    if (result) {
      setMatchAllResults(result);
    }
    setIsLoadingAllResults(false);
  };

  return (
    <Card sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
        Comp profile matcher
      </Typography>
      <Grid container spacing={3} direction="column">
        <Grid item xs={12}>
          <TextField
            label="Commission id"
            value={commissionId}
            onChange={handleCommissionIdChange}
            margin="normal"
            size="small"
            sx={{ width: '25%' }}
          />
        </Grid>

        <Grid item xs={12}>
          <Grid container spacing={2} direction="column">
            {isLoadingContacts && (
              <Grid item>
                <CircularProgress size={24} />
              </Grid>
            )}
            {matcherRows.map((row) => (
              <Grid item key={row.id}>
                <CompProfileMatcherRow
                  initialAgent={row}
                  contacts={contacts}
                  compProfiles={compProfilesData?.data || []}
                  onRemove={() => handleRemoveRow(row.id)}
                  handleRemoveAllData={handleRemoveAllData}
                  isContactsLoading={isLoadingContacts}
                  isCompProfilesLoading={isLoadingCompProfiles}
                  updateCompProfileMatchersArg={updateCompProfileMatchersArg}
                  matchCompProfile={handleMatchCompProfile}
                  matchAllResults={matchAllResults}
                  isLoadingAllResults={isLoadingAllResults}
                />
              </Grid>
            ))}
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Button
            disabled={isLoadingAllResults || isLoadingContacts}
            variant="outlined"
            onClick={handleAddRow}
            sx={{
              width: '150px',
              alignSelf: 'flex-start',
            }}
          >
            Add agent
          </Button>
        </Grid>
        {matcherRows.length > 0 && (
          <Grid item xs={12}>
            <Button
              disabled={
                compProfileMatchersArg.length === 0 || isLoadingAllResults
              }
              variant="contained"
              onClick={handleMatchAllCompProfile}
              color="primary"
              sx={{
                width: '150px',
                alignSelf: 'flex-start',
              }}
            >
              Match all
            </Button>
          </Grid>
        )}
      </Grid>
    </Card>
  );
};

export default CompProfileMatcherTool;
