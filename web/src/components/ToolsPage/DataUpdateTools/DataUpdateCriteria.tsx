import type React from 'react';
import { useState, useMemo, useCallback } from 'react';
import { DataEntities, DataUpdateAccessTypesOptions } from 'common/globalTypes';
import { useQueryClient } from '@tanstack/react-query';

import {
  type DataUpdateActionsObject,
  DataUpdateCopyTypes,
  type Fields,
} from '@/components/ToolsPage/DataUpdateTools/types';
import EnhancedDataView from '@/components/organisms/EnhancedDataView';
import { FieldTypes } from '@/types';
import CriteriaActionsViewer from '@/components/ToolsPage/DataUpdateTools/CriteriaActionsViewer';
import API from '@/services/API';
import DataUpdateCopyModal from '@/components/ToolsPage/DataUpdateTools/DataUpdateCopyModal';
import { useCanEditAccess } from './hooks/useCanEditAccess';

interface DataUpdateCriteriaProps {
  fields: Fields;
}

const DataUpdateCriteria: React.FC<DataUpdateCriteriaProps> = ({ fields }) => {
  const [showGlobal, setShowGlobal] = useState(false);
  const [selectedData, setSelectedData] = useState<
    DataUpdateActionsObject[] | []
  >([]);
  const [openCopy, setOpenCopy] = useState(false);
  const [queryKey, setQueryKey] = useState(null);
  const queryClient = useQueryClient();
  const canEditAccess = useCanEditAccess();

  const poster = API.getMutation(`data-update/criteria?copy=true`, 'POST');

  const handleClose = useCallback(() => {
    setOpenCopy(false);
  }, []);

  const handleRefetch = useCallback(() => {
    if (queryKey) {
      queryClient.refetchQueries({
        queryKey: queryKey,
        type: 'active',
        exact: true,
      });
    }
  }, [queryKey, queryClient]);

  const extraActions = useMemo(
    () => [
      {
        label: 'Toggle access',
        onClick: () => setShowGlobal(!showGlobal),
        type: 'button',
      },
      {
        label: 'Copy selected',
        onClick: () => setOpenCopy(true),
        disabled: selectedData.length === 0,
        type: 'button',
      },
    ],
    [showGlobal, selectedData.length]
  );

  const dataSpec = useMemo(
    () => ({
      label: 'Data update criteria',
      table: `data-update/criteria?global=${showGlobal}&`,
      fields: {
        data_entity: {
          label: 'Data entity',
          type: FieldTypes.SELECT,
          options: [DataEntities.COMMISSIONS, DataEntities.POLICIES],
          enabled: true,
        },
        name: {
          label: 'Name',
          enabled: true,
        },
        custom_data_update_criteria_mode: {
          label: 'Custom data update criteria mode',
          type: FieldTypes.BOOLEAN,
          enabled: true,
        },
        custom_data_update_criteria: {
          label: 'Custom data update criteria',
          type: FieldTypes.CODE,
          enabled: true,
          condition: (row) => row?.custom_data_update_criteria_mode,
        },
        custom_data_update_criteria_params: {
          label: 'Relational fields for custom criteria',
          type: FieldTypes.STRING_ARRAY,
          enabled: true,
          condition: (row) => row?.custom_data_update_criteria_mode,
        },
        data_update_criteria: {
          label: 'Data update criteria',
          type: FieldTypes.FIELD_MATCHER,
          fieldGetter: (row) => {
            if (!row.data_entity) return [];
            return fields[row.data_entity as DataEntities];
          },
          shouldUsePolicyData: (row) => {
            if (!row.data_entity) return [];
            return row.data_entity === DataEntities.COMMISSIONS;
          },
          fields: Object.values(fields),
          tableFormatter: (field, row) => {
            return (
              <CriteriaActionsViewer
                fieldData={field}
                rowData={row}
                fields={fields}
              />
            );
          },
          enabled: true,
          condition: (row) => !row?.custom_data_update_criteria_mode,
        },
        notes: {
          label: 'Notes',
          enabled: true,
        },
        access: {
          label: 'Access',
          type: FieldTypes.SELECT,
          options: DataUpdateAccessTypesOptions,
          readOnly: !canEditAccess,
          enabled: true,
        },
      },
    }),
    [showGlobal, fields, canEditAccess]
  );

  return (
    <>
      <EnhancedDataView
        dataSpec={dataSpec}
        hideSelectedCount
        hideExport={true}
        setSelectedData={setSelectedData}
        extraActions={extraActions}
        onQueryKeyChange={setQueryKey}
      />
      <DataUpdateCopyModal
        handleClose={handleClose}
        open={openCopy}
        type={DataUpdateCopyTypes.CRITERIA}
        selectedData={selectedData}
        poster={poster}
        refetch={handleRefetch}
      />
    </>
  );
};

export default DataUpdateCriteria;
