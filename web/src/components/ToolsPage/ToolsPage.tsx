import { Box, Tab, Tabs, Typography } from '@mui/material';
import { nanoid } from 'nanoid';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import BulkEditPolicyAgentSplit from '@/components/ToolsPage/components/bulk-operations/BulkEditPolicyAgentSplit';
import CsvToJson from '@/components/ToolsPage/CsvToJson';
import GenericTool from '@/components/ToolsPage/GenericTool';
import JsonToCsv from '@/components/ToolsPage/JsonToCsv';
import PaymentLinks from '@/components/ToolsPage/PaymentLinks';
import RegressionTest from '@/components/ToolsPage/RegressionTest';
import UserTools from '@/components/ToolsPage/UserTools';
import { BulkUpdateAgentUpline } from './components/bulk-operations/BulkUpdateAgentUpline';
import { CompGridImport } from '@/components/ToolsPage/components/comp-grid/CompGridImport';
import CompProfilesMatcherTool from '@/components/ToolsPage/CompProfileMatcherTools/CompProfileMatcherTool';
import { BulkUpdateAgentCarrierGridLevels } from './components/bulk-operations/BulkUpdateAgentGridLevels';
import { BulkUpdateCompProfilesAgentAssign } from './components/bulk-operations/BulkUpdateCompProfilesAgentAssign';

const uniques = (strs, delimiter = '\n') =>
  Array.from(new Set(strs.split(delimiter))).join('\n');

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`tabpanel-${index}`}
    {...other}
  >
    {value === index && (
      <Box sx={{ pt: 2 }}>
        <Typography component="div">{children}</Typography>
      </Box>
    )}
  </div>
);

const tabs = [
  {
    label: 'Policy split import',
    path: 'agent-split-import',
    component: <BulkEditPolicyAgentSplit />,
  },
  {
    label: 'Agent upline import',
    path: 'agent-upline-import',
    component: <BulkUpdateAgentUpline />,
  },
  {
    label: 'Update agent grid levels',
    path: 'agent-carrier-grid-levels',
    component: <BulkUpdateAgentCarrierGridLevels />,
  },
  {
    label: 'Comp profiles bulk agent assign',
    path: 'comp-profiles-bulk-agent-assign',
    component: <BulkUpdateCompProfilesAgentAssign />,
  },
  {
    label: 'Comp grid import',
    path: 'comp-grid-import',
    component: <CompGridImport />,
  },
  {
    label: 'Comp profile matcher',
    path: 'comp-profile-matcher',
    component: <CompProfilesMatcherTool />,
  },
  {
    label: 'User tools',
    path: 'user-tools',
    component: <UserTools />,
  },
  {
    label: 'Payment links',
    path: 'payment-links',
    component: <PaymentLinks />,
  },
  {
    label: 'Regression Test',
    path: 'regression-test',
    component: <RegressionTest />,
  },
  {
    label: 'Unique lines',
    path: 'unique-lines',
    component: <GenericTool outFn={uniques} />,
  },
  {
    label: 'Nano IDs',
    path: 'nano-ids',
    component: (
      <GenericTool
        outFn={(num) =>
          Array.from(
            new Array(
              Number.isInteger(Number.parseInt(num)) ? Number.parseInt(num) : 1
            )
          )
            .map(() => nanoid())
            .join('\n')
        }
      />
    ),
  },
  {
    label: 'CSV to JSON',
    path: 'csv-to-json',
    component: <CsvToJson />,
  },
  {
    label: 'JSON to CSV',
    path: 'json-to-csv',
    component: <JsonToCsv />,
  },
];

// biome-ignore lint/correctness/noEmptyPattern: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const ToolsPage = ({}) => {
  const { tab = 'list' } = useParams();
  const tabPaths = tabs.map((tab) => tab.path);
  const routedTab = tabPaths.indexOf(tab) >= 0 ? tabPaths.indexOf(tab) : 0;
  const [value, setValue] = useState(routedTab);
  const navigate = useNavigate();

  const handleChange = (_event, newValue) => {
    navigate(`/admin/tools/${tabs[newValue].path}`);
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%', overflow: 'scroll' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={value} onChange={handleChange}>
          {tabs.map((tab) => (
            <Tab label={tab.label} key={tab.label} />
          ))}
        </Tabs>
      </Box>
      {tabs.map((tab) => (
        <TabPanel value={value} index={tabs.indexOf(tab)} key={tab.label}>
          {tab.component}
        </TabPanel>
      ))}
    </Box>
  );
};

ToolsPage.propTypes = {
  user: PropTypes.object,
};

export default ToolsPage;
