import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControlLabel,
  TextField,
  Typography,
} from '@mui/material';
import { isValidJsonString } from 'common/helpers';
import isNil from 'lodash-es/isNil';
import { useEffect, useState } from 'react';
import * as XLSX from 'xlsx';

import BasicTable from '@/components/molecules/BasicTable';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { ExpandableTextField } from '@/components/molecules/ExpandableTextField';
import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import { useCompGridParse } from './hooks/useCompGridParse';

const splitByParens = (str: string): [string | null, string | null] => {
  const match = str.match(/(.*?)\s*\(([^)]+)\)/);
  if (match) {
    return [match[1].trim(), match[2]];
  } else {
    return [str.trim(), null];
  }
};

const parseRange = (range) => {
  const result: { min: null | number; max: null | number } = {
    min: null,
    max: null,
  };
  if (!range) return result;
  const cleanedRange = range
    .replace(/\s+/g, '')
    .replace(/(\d+)<(?!\d)/g, (_match, p1) => {
      const number = parseInt(p1, 10);
      return (number - 1).toString();
    });

  const rangePattern = /^(\d+)-(\d+)$/;
  const minPattern = /^>(\d+)$/;
  const maxPattern = /^<(\d+)$/;
  const minPlusPattern = /^(\d+)\+$/;
  const sameSamePattern = /^(\d+)$/;

  if (rangePattern.test(cleanedRange)) {
    const [, min, max] = cleanedRange.match(rangePattern);
    result.min = Number(min);
    result.max = Number(max);
  } else if (minPattern.test(cleanedRange)) {
    const [, min] = cleanedRange.match(minPattern);
    result.min = Number(min);
  } else if (maxPattern.test(cleanedRange)) {
    const [, max] = cleanedRange.match(maxPattern);
    result.max = Number(max);
  } else if (minPlusPattern.test(cleanedRange)) {
    const [, min] = cleanedRange.match(minPlusPattern);
    result.min = Number(min);
  } else if (sameSamePattern.test(cleanedRange)) {
    const [, min] = cleanedRange.match(sameSamePattern);
    result.min = Number(min);
    result.max = Number(min);
  } else {
    throw new Error(`Invalid range format: "${range}"`);
  }

  return result;
};

const dropColumnsByStrings = (
  data,
  colsToDrop,
  opts = { substringMatch: true }
) => {
  if (data.length === 0) {
    throw new Error('The input array must not be empty.');
  }

  const headerRow = data[0];
  const columnIndexes = headerRow
    .map((header, index) =>
      colsToDrop.some((substring) =>
        opts.substringMatch
          ? header.toLowerCase().includes(substring.toLowerCase())
          : header.toLowerCase() === substring.toLowerCase()
      )
        ? index
        : -1
    )
    .filter((index) => index !== -1);

  if (columnIndexes.length === 0) {
    console.warn(
      `No headers include any of the substrings "${colsToDrop.join(', ')}".`
    );
  }

  return data.map((row) =>
    row.filter((_, index) => !columnIndexes.includes(index))
  );
};

const combineHeaders = (data) => {
  if (data.length < 2) {
    throw new Error('The input array must have at least two rows.');
  }

  const firstRow = data[0];
  const secondRow = data[1];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const combinedHeader: any[] = [];
  let currentPrefix = '';
  const levels: string[] = [];

  for (let i = 0; i < secondRow.length; i++) {
    if (firstRow[i]) {
      currentPrefix = firstRow[i];
      levels.push(currentPrefix);
    }
    combinedHeader.push(
      currentPrefix && secondRow[i] !== 'TG rate effective date'
        ? `${currentPrefix}::${secondRow[i]}`
        : secondRow[i].replace('\n', ' ').replace(/\s+/g, ' ')
    );
  }

  return {
    levels,
    data: [combinedHeader, ...data.slice(2)],
  };
};

const duplicateColumn = (
  data,
  header,
  newHeader,
  transformer = (data) => data
) => {
  if (data.length === 0) {
    throw new Error('The input array must not be empty.');
  }

  const headerRow = data[0];
  const columnIndex = headerRow.indexOf(header);

  if (columnIndex === -1) {
    console.warn(`Header "${header}" not found.`);
    // Throw new Error(`Header "${header}" not found.`);
  }

  const newHeaderRow = [...headerRow, newHeader];
  const newData = data.map((row, rowIndex) => {
    if (rowIndex === 0) {
      return newHeaderRow;
    }
    const newCol = transformer(row[columnIndex]);
    const newRow = [...row, newCol];
    return newRow;
  });

  return newData;
};

const padToHeaderLength = (data) => {
  if (data.length === 0) throw new Error('The input array must not be empty.');

  const headerRow = data[0];
  const headerLength = headerRow.length;

  const paddedData = data.map((row) => {
    const paddedRow = [...row];
    while (paddedRow.length < headerLength) {
      paddedRow.push(null);
    }
    return paddedRow;
  });

  return paddedData;
};

const splitByPrefix = (data, sharedFields, levels, fields) => {
  if (data.length === 0) {
    throw new Error('The input array must not be empty.');
  }

  const headerRow = data[0];
  const result = {};

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  levels.forEach((level) => {
    const newHeaderRow = [...sharedFields, ...fields];
    const newData = data.map((row, i) => {
      if (i === 0) return newHeaderRow;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const newRow: any[] = [];
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      newHeaderRow.forEach((header) => {
        const index = sharedFields.includes(header)
          ? headerRow.indexOf(header)
          : headerRow.indexOf(`${level}::${header}`);
        // NewRow.push(index !== -1 ? row[index] : null);
        if (index !== -1) newRow.push(row[index]);
      });
      return newRow;
    });
    const rateIndicies = ['carrier_rate', 'house_rate', 'total_rate'].map(
      (field) => newHeaderRow.indexOf(field)
    );
    const dataWithRates = newData.filter((row) =>
      row.some((_cell) => rateIndicies.some((i) => !isNil(row[i])))
    );
    if (dataWithRates.length > 1) {
      result[level] = dataWithRates;
    }
  });

  return result;
};

const renameHeaders = (data, generateNewHeader) => {
  if (data.length === 0) {
    throw new Error('The input array must not be empty.');
  }

  const headerRow = data[0];
  const newHeaderRow = headerRow.map((header) => generateNewHeader(header));

  return [newHeaderRow, ...data.slice(1)];
};

const groupByColumn = (
  colName,
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  gridsByLevels: Record<string, any>
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
): Record<string, any> => {
  const groupedData = {};
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  Object.entries(gridsByLevels).forEach(([level, rows]) => {
    const colIndex = rows[0].indexOf(colName);
    const headers = rows[0].filter((_, i) => i !== colIndex);
    rows.forEach((row, i) => {
      if (i === 0) return;
      const colVal = row[colIndex];
      if (colIndex === -1) throw Error('Column value cannot be empty');
      if (!groupedData[colVal]) groupedData[colVal] = {};
      if (!groupedData[colVal][level]) groupedData[colVal][level] = [headers];
      groupedData[colVal][level].push(row.filter((_, i) => i !== colIndex));
    });
  });
  return groupedData;
};

const numberifyCols = (data, cols) => {
  if (data.length === 0) {
    throw new Error('The input array must not be empty.');
  }

  const headerRow = data[0];
  const colIndicies = cols.map((col) => headerRow.indexOf(col));

  return data.map((row, i) => {
    if (i === 0) return row;
    const newRow = [...row];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    colIndicies.forEach((index) => {
      if (index !== -1 && row[index] !== null) {
        newRow[index] = Number.isNaN(Number(row[index]))
          ? null
          : Number(row[index]);
      }
    });
    return newRow;
  });
};

const parseFintaryGrid = (jsonRes, _metadata) => {
  let { levels, data: gridData } = combineHeaders(jsonRes);

  gridData = renameHeaders(gridData, (header) => {
    return header
      .replace('Carrier rate', 'carrier_rate')
      .replace('House rate', 'house_rate')
      .replace('Total rate', 'total_rate')
      .replace('Comp grid', 'grid_name')
      .replace('Product type', 'product_type')
      .replace('Product', 'product_name')
      .replace('Policy year start', 'policy_year_start')
      .replace('Policy year end', 'policy_year_end')
      .replace('Age start', 'issue_age_start')
      .replace('Age end', 'issue_age_end')
      .replace('Premium min', 'premium_min')
      .replace('Premium max', 'premium_max')
      .replace('Compensation type', 'compensation_type')
      .replace('Effective date', 'effective_date');
  });

  gridData = numberifyCols(gridData, [
    'issue_age_start',
    'issue_age_end',
    'policy_year_start',
    'policy_year_end',
    'premium_min',
    'premium_max',
  ]);

  levels = levels ?? [];
  const gridsByLevels = splitByPrefix(
    gridData,
    [
      'grid_name',
      'product_type',
      'product_name',
      'issue_age_start',
      'issue_age_end',
      'policy_year_start',
      'policy_year_end',
      'premium_min',
      'premium_max',
      'compensation_type',
      'effective_date',
    ],
    levels,
    ['carrier_rate', 'house_rate', 'total_rate']
  );

  const gridsByGrids = groupByColumn('grid_name', gridsByLevels);

  return gridsByGrids;
};

const parseTGGrid = (jsonRes, _metadata) => {
  let { levels, data: gridData } = combineHeaders(jsonRes);
  levels = levels.filter((level) =>
    [
      'TG',
      'AT',
      'A0',
      'A1',
      'A2',
      'A3',
      'S',
      'A',
      'B',
      'UNI',
      'Mindy Gong',
      'Li Luo',
      'July Li',
    ].includes(level)
  );
  gridData = gridData.filter((row) => row.some((cell) => cell !== null));
  gridData = padToHeaderLength(gridData);
  gridData = dropColumnsByStrings(gridData, [
    'Bonus',
    'Broker',
    'Contract code',
    'LIBRA',
    'TG::Pass Thru',
    'TG::Gross',
  ]);
  gridData = renameHeaders(gridData, (header) => {
    return header
      .replace('Gross Comm', 'total_rate')
      .replace('Comm from TG', 'house_rate')
      .replace('Comm from Carrier', 'carrier_rate')
      .replace('TG rate effective date', 'effective_date')
      .replace('Carrier Grid Effective Date', 'effective_date')
      .replace(/TG Stmt\s+\(3\)/, 'total_rate')
      .replace('Carrier', 'grid_name')
      .replace('Product Type', 'product_type')
      .replace('Product', 'product_name')
      .replace('Commission Type', '_compensation_type')
      .replace('Age Group', 'age_range');
  });
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  levels.forEach((level) => {
    gridData = duplicateColumn(
      gridData,
      'effective_date',
      `${level}::effective_date`
    );
  });
  gridData = duplicateColumn(gridData, 'TG::total_rate', 'TG::carrier_rate');
  gridData = duplicateColumn(
    gridData,
    'TG::total_rate',
    'TG::house_rate',
    (_v) => null
  );
  gridData = duplicateColumn(
    gridData,
    '_compensation_type',
    'policy_year_start',
    (range) =>
      range?.endsWith('(% of FYC)')
        ? null
        : parseRange(
            splitByParens(
              range
                ?.replace(/\s*\(\d\d?-\d\d? mth\)/g, '')
                ?.replace(/ Target\)/g, ')')
            )?.[1]?.replace(/^Yr\s*/g, '')
          ).min
  );
  gridData = duplicateColumn(
    gridData,
    '_compensation_type',
    'policy_year_end',
    (range) =>
      range.endsWith('(% of FYC)')
        ? null
        : parseRange(
            splitByParens(
              range
                ?.replace(/\s*\(\d\d?-\d\d? mth\)/g, '')
                ?.replace(/ Target\)/g, ')')
            )?.[1]?.replace(/^Yr\s*/g, '')
          ).max
  );
  gridData = duplicateColumn(
    gridData,
    '_compensation_type',
    'compensation_type',
    (range) =>
      range.endsWith('(% of FYC)') ? range : splitByParens(range)?.[0]
  );
  gridData = duplicateColumn(
    gridData,
    'age_range',
    'issue_age_start',
    (range) => parseRange(range).min
  );
  gridData = duplicateColumn(
    gridData,
    'age_range',
    'issue_age_end',
    (range) => parseRange(range).max
  );
  const gridsByLevels = splitByPrefix(
    gridData,
    [
      'grid_name',
      'product_type',
      'product_name',
      'issue_age_start',
      'issue_age_end',
      'policy_year_start',
      'policy_year_end',
      'compensation_type',
    ],
    levels,
    ['effective_date', 'carrier_rate', 'house_rate', 'total_rate']
  );

  const gridsByGrids = groupByColumn('grid_name', gridsByLevels);

  return gridsByGrids;
};

export type ParsersType = typeof parsers;
const parsers = {
  Fintary: parseFintaryGrid,
  TransGlobal: parseTGGrid,
};

const DataBulkAdd = ({
  params,
  handleUpdateParams,
  onCancel,
  btnLabel = 'Bulk add',
}: {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  params?: any;
  handleUpdateParams?: () => void;
  onCancel: () => void;
  btnLabel: string;
}) => {
  const mutation = API.getMutation('comp-grids/import-rates', 'POST');

  const [csv, setCsv] = useState('');
  const [grids, setGrids] = useState({});
  const { showSnackbar } = useSnackbar();
  const [selectedPreviewGrid, setSelectedPreviewGrid] = useState('');
  const [selectedPreviewGridLevel, setSelectedPreviewGridLevel] = useState('');
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [selectedGridToUpdate, setSelectedGridToUpdate] = useState<any>(null);
  const [createGridStructure, setCreateGridStructure] = useState(false);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [validationResults, setValidationResults] = useState<any>([]);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [importResults, setImportResults] = useState<any>([]);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [errors, setErrors] = useState<any>([]);
  const {
    selectedParser: parser,
    availableParsers,
    handleSelectParser,
  } = useCompGridParse({
    parsers,
  });

  const { data: existingCompGrids } = API.getBasicQuery('comp-grids');

  useEffect(() => {
    try {
      setGrids({});
      setErrors([]);
      setValidationResults({});
      setImportResults([]);
      const csvSheet = XLSX.read(csv, {
        type: 'string',
        raw: true,
      });

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      let jsonRes: any = XLSX.utils.sheet_to_json(
        csvSheet.Sheets[csvSheet.SheetNames[0]],
        { raw: true, header: 1 }
      );

      // Remove empty leading rows
      jsonRes = jsonRes.slice(
        jsonRes.findIndex((row) =>
          row.some((cell) => cell !== null && cell !== undefined && cell !== '')
        )
      );

      // Remove empty leading columns
      if (jsonRes.length > 0) {
        let emptyLeadingColumns = 0;
        for (let col = 0; col < jsonRes[0].length; col++) {
          const isColumnEmpty = jsonRes.every(
            (row) =>
              row[col] === null || row[col] === undefined || row[col] === ''
          );

          if (isColumnEmpty) {
            emptyLeadingColumns++;
          } else {
            break;
          }
        }
        if (emptyLeadingColumns > 0) {
          jsonRes = jsonRes.map((row) => row.slice(emptyLeadingColumns));
        }
      }

      if (jsonRes.length <= 1) return;

      const gridsByGrids = parser
        ? (availableParsers?.[parser]?.(jsonRes, {
            gridName: selectedGridToUpdate?.name,
          }) ?? {})
        : {};

      setGrids(gridsByGrids);
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } catch (e: any) {
      setErrors([e?.message]);
      console.error(e);
    }
  }, [csv, selectedGridToUpdate, parser, availableParsers]);

  return (
    <Box sx={{ width: '100%', px: 1 }}>
      <Typography>Import grid in csv/tsv format</Typography>
      <Typography variant="body2">
        Comp grids must exist and entities must be unique. Structure can be
        created if selected (products, criteria, levels). Rates with open ended
        date ranges will be ended the day before new date specified.
      </Typography>
      <Box sx={{ mt: 1 }}>
        <EnhancedSelect
          options={Object.keys(availableParsers)}
          value={parser || ''}
          onChange={handleSelectParser}
          enableSearch
          sortLabel
          label="Grid template *"
        />
        {parser === 'BrokersAlliance' && (
          <EnhancedSelect
            options={[{ name: '' }, ...(existingCompGrids ?? [])]}
            label="Comp grid *"
            value={selectedGridToUpdate}
            onChange={setSelectedGridToUpdate}
            labelKey="name"
            valueKey="name"
          />
        )}
      </Box>
      <ExpandableTextField
        label="Comp grid (csv/tsv)"
        defaultValue={csv}
        onChange={setCsv}
        sx={{ mt: 1.5, width: '100%' }}
        editableSx={{ height: 'unset', maxHeight: '25vh', overflow: 'scroll' }}
      />
      {params && (
        <TextField
          label="Params"
          value={params}
          onChange={handleUpdateParams}
          sx={{ width: '100%', mt: 1 }}
          fullWidth
          error={!isValidJsonString(params)}
          helperText={!isValidJsonString(params) && 'Invalid JSON string'}
        />
      )}

      <Box
        sx={{
          mt: 1,
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
        }}
      >
        <Box>
          <Button
            variant="outlined"
            onClick={async () => {
              try {
                const res = await mutation.mutateAsync({
                  data: grids,
                  opts: { validateOnly: true, parser },
                });
                setValidationResults(res.data);
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              } catch (e: any) {
                console.error(e);
                showSnackbar(e.message || e.error, 'error');
              }
            }}
            disabled={mutation.isPending}
          >
            Check structure
          </Button>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={createGridStructure}
                onChange={(e) => setCreateGridStructure(e.target.checked)}
              />
            }
            label="Create grid structure"
          />
          <Button sx={{ mr: 1 }} onClick={onCancel}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={async () => {
              try {
                const res = await mutation.mutateAsync({
                  data: grids,
                  opts: { createGridStructure, parser },
                });
                setImportResults([
                  {
                    timestamp: new Date(),
                    result: 'success',
                    rates: res?.data?.rates ?? {},
                  },
                  ...importResults,
                ]);
                showSnackbar('Grids imported', 'success');
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              } catch (e: any) {
                console.error(e);
                setImportResults([
                  {
                    timestamp: new Date(),
                    result: 'error',
                    error: e.message || e.error,
                  },
                  ...importResults,
                ]);
                showSnackbar(e.message || e.error, 'error');
              }
            }}
            disabled={
              (params && !isValidJsonString(params)) || mutation.isPending
            }
          >
            {btnLabel}
          </Button>
        </Box>
      </Box>

      <Box>
        {errors.map((error) => (
          <Typography key={error} color="error">
            {error}
          </Typography>
        ))}
      </Box>

      {importResults.length > 0 && (
        <Box sx={{ mt: 1 }}>
          <Typography>Import results</Typography>
          {importResults.map(
            ({
              error,
              rates,
              result,
              timestamp,
            }: {
              error: string;
              rates: {
                added: number;
                updated: number;
              };
              result: string;
              timestamp: Date;
            }) => (
              <Box key={timestamp.toISOString()}>
                {result === 'error' ? (
                  <Typography
                    variant="body2"
                    sx={{ ml: 2, color: 'error.main' }}
                  >
                    {`❌ ${timestamp.toLocaleString()} - ${error}`}
                  </Typography>
                ) : (
                  <Typography variant="body2" sx={{ ml: 2 }}>
                    {`✅ ${timestamp.toLocaleString()} - Added: ${rates?.added ?? 0}, Updated: ${rates?.updated ?? 0}`}
                  </Typography>
                )}
              </Box>
            )
          )}
          <Divider sx={{ my: 1 }} />
        </Box>
      )}

      {Object.keys(validationResults).length > 0 && (
        <Box sx={{ mt: 1 }} key={'validationResults'}>
          <Typography>Comp grid structure validation</Typography>
          {Object.entries(validationResults).map(
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            ([gridName, gridValidationResults]: [string, any]) => (
              <Box key={gridName}>
                <Typography variant="body2" sx={{ ml: 2 }}>
                  {gridValidationResults.grid.missing.length
                    ? '❌ '
                    : Object.values(gridValidationResults).every(
                          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                          (v: any) => v.missing.length === 0
                        )
                      ? '✅ '
                      : '⚠️ '}
                  {gridName}
                </Typography>
                {gridValidationResults.grid.missing.length > 0 && (
                  <Typography variant="body2" sx={{ ml: 4 }}>
                    Missing comp grid - needs to be manually created
                  </Typography>
                )}
                {gridValidationResults.grid.missing.length === 0
                  ? Object.entries(gridValidationResults).map(
                      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      ([entity, entityResults]: [string, any]) => (
                        <Box key={entity}>
                          <Typography variant="body2" sx={{ ml: 4 }}>
                            {entity}:{' '}
                            {entityResults.missing.length === 0
                              ? `All match (${entityResults.existing.length})`
                              : `${entityResults.existing.length} match, ${entityResults.missing.length} missing.`}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ ml: 6 }}
                            key={`${entity}-missing-items`}
                          >
                            {entityResults.missing.length
                              ? ` (${
                                  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                                  (entityResults as any).missing.join(', ')
                                })`
                              : ''}
                          </Typography>
                        </Box>
                      )
                    )
                  : ''}
              </Box>
            )
          )}
          <Divider sx={{ my: 1 }} />
        </Box>
      )}

      {Object.keys(grids).length > 0 && (
        <Box sx={{ mt: 1 }}>
          <Typography>Parsed input</Typography>
          <Typography variant="body2" sx={{ ml: 2 }}>
            Comp grids ({Object.keys(grids).length})
          </Typography>
          {Object.entries(grids).map(([gridName, gridLevels]) => {
            const levels = Object.keys(gridLevels ?? {});
            return (
              <Typography variant="body2" sx={{ ml: 4 }} key={gridName}>
                {gridName}: Levels ({levels.length}):{' '}
                {Object.entries(gridLevels ?? {})
                  .map(([level, rows]) => `${level} (${rows.length - 1})`)
                  .join(', ')}
              </Typography>
            );
          })}
          <EnhancedSelect
            label="Grids"
            options={['', ...Object.keys(grids)]}
            value={selectedPreviewGrid}
            onChange={setSelectedPreviewGrid}
            sx={{ mt: 1 }}
            enableSearch
          />{' '}
          <EnhancedSelect
            label="Grid levels"
            options={['', ...Object.keys(grids[selectedPreviewGrid] ?? {})]}
            value={selectedPreviewGridLevel}
            onChange={setSelectedPreviewGridLevel}
            sx={{ mt: 1 }}
            enableSearch
          />
          {selectedPreviewGrid && selectedPreviewGridLevel && (
            <Box sx={{ mt: 2 }}>
              <BasicTable
                headers={
                  grids[selectedPreviewGrid][selectedPreviewGridLevel][0]
                }
                rows={grids[selectedPreviewGrid][
                  selectedPreviewGridLevel
                ].slice(1)}
                nowrap
              />
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

export default DataBulkAdd;
