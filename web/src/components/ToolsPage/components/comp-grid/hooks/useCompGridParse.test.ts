import { useSearchParams as _useSearchParams } from 'react-router-dom';
import { renderHook, act } from '@testing-library/react';
import { AccountIds } from 'common/constants';

import { useAccountStore } from '@/store';
import type { Mock } from 'vitest';
import { useCompGridParse } from './useCompGridParse';
import type { ParsersType } from '../CompGridImportTool';

vi.mock('react-router-dom', () => ({
  useSearchParams: vi.fn(),
}));

vi.mock('@/store', () => ({
  useAccountStore: vi.fn(),
}));

describe('useCompGridParse', () => {
  const parsers: ParsersType = {
    Fintary: vi.fn(),
    TransGlobal: vi.fn(),
  };

  let searchParamsMock: Map<string, string>;
  let setSearchParamsMock: Mock;

  const renderUseCompGridParse = () => {
    return renderHook(() => useCompGridParse({ parsers })).result.current;
  };

  const setAccountStoreMock = ({ accountId }: { accountId: string }) => {
    (useAccountStore as unknown as Mock).mockReturnValue({
      selectedAccount: { accountId },
    });
  };

  beforeEach(() => {
    searchParamsMock = new Map();
    setSearchParamsMock = vi.fn((updater) => {
      if (typeof updater === 'function') {
        searchParamsMock = updater(searchParamsMock);
      }
      return searchParamsMock;
    });

    (_useSearchParams as unknown as Mock).mockImplementation(() => [
      {
        get: (key: string) => searchParamsMock.get(key),
        set: (key: string, value: string) => searchParamsMock.set(key, value),
        delete: (key: string) => searchParamsMock.delete(key),
      },
      setSearchParamsMock,
    ]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('selectedParser', () => {
    it('should return the selected parser from search params', () => {
      searchParamsMock.set('parser', 'Fintary');
      setAccountStoreMock({ accountId: 'some-id' });

      const { selectedParser } = renderUseCompGridParse();

      expect(selectedParser).toBe('Fintary');
    });

    it('should default to TransGlobal if account is TRANSGLOBAL and no parser is set', () => {
      setAccountStoreMock({ accountId: AccountIds.TRANSGLOBAL });

      renderUseCompGridParse();

      expect(setSearchParamsMock).toHaveBeenCalled();
      expect(searchParamsMock.get('parser')).toBe('TransGlobal');
    });

    it('should default to Fintary if account is not TRANSGLOBAL and no parser is set', () => {
      setAccountStoreMock({ accountId: 'other-id' });

      renderUseCompGridParse();

      expect(setSearchParamsMock).toHaveBeenCalled();
      expect(searchParamsMock.get('parser')).toBe('Fintary');
    });
  });

  describe('availableParsers', () => {
    it('should return both Fintary and TransGlobal if account is TRANSGLOBAL', () => {
      setAccountStoreMock({ accountId: AccountIds.TRANSGLOBAL });
      searchParamsMock.set('parser', 'TransGlobal');

      const { availableParsers } = renderUseCompGridParse();

      expect(Object.keys(availableParsers)).toContain('Fintary');
      expect(Object.keys(availableParsers)).toContain('TransGlobal');
    });

    it('should return only Fintary if account is not TRANSGLOBAL', () => {
      setAccountStoreMock({ accountId: 'other-id' });
      searchParamsMock.set('parser', 'Fintary');

      const { availableParsers } = renderUseCompGridParse();

      expect(Object.keys(availableParsers)).toContain('Fintary');
      expect(Object.keys(availableParsers)).not.toContain('TransGlobal');
    });
  });

  describe('handleSelectParser', () => {
    it('should set the parser in search params when called', () => {
      setAccountStoreMock({ accountId: 'other-id' });

      const { handleSelectParser } = renderUseCompGridParse();

      act(() => handleSelectParser('Fintary'));

      expect(setSearchParamsMock).toHaveBeenCalled();
      expect(searchParamsMock.get('parser')).toBe('Fintary');
    });

    it('should remove the parser from search params if called with empty string', () => {
      setAccountStoreMock({ accountId: 'other-id' });
      searchParamsMock.set('parser', 'Fintary');

      const { handleSelectParser } = renderUseCompGridParse();

      act(() => handleSelectParser(''));

      expect(setSearchParamsMock).toHaveBeenCalled();
      expect(searchParamsMock.get('parser')).toBeUndefined();
    });
  });
});
