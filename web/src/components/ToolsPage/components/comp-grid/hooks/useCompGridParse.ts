import { AccountIds } from 'common/constants';
import { useCallback, useEffect, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

import { useAccountStore } from '@/store';
import type { ParsersType } from '../CompGridImportTool';

type Props = {
  parsers: ParsersType;
};

const URL_QUERY_PARSER = 'parser';

export const useCompGridParse = (props: Props) => {
  const [searchParams, setSearchParams] = useSearchParams({});
  const { selectedAccount } = useAccountStore();

  const selectedParser = searchParams.get(URL_QUERY_PARSER);
  const availableParsers = useMemo(() => {
    const isTransGlobal = selectedAccount?.accountId === AccountIds.TRANSGLOBAL;

    return {
      Fintary: props.parsers.Fintary,
      ...(isTransGlobal ? { TransGlobal: props.parsers.TransGlobal } : {}),
    };
  }, [selectedAccount?.accountId, props.parsers]);

  const handleSelectParser = useCallback(
    (newParser: string) => {
      setSearchParams((prev) => {
        if (newParser) {
          prev.set(URL_QUERY_PARSER, newParser);
        } else {
          prev.delete(URL_QUERY_PARSER);
        }
        return prev;
      });
    },
    [setSearchParams]
  );

  useEffect(() => {
    if (!selectedParser) {
      if (selectedAccount?.accountId === AccountIds.TRANSGLOBAL) {
        handleSelectParser('TransGlobal');
      } else {
        handleSelectParser('Fintary');
      }
    }
  }, [selectedAccount?.accountId, handleSelectParser, selectedParser]);

  return {
    selectedParser,
    availableParsers,
    handleSelectParser,
  };
};
