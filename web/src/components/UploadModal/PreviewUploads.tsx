import { Box, Typography } from '@mui/material';
import * as Sentry from '@sentry/react';
import { DocumentStatuses, UploadSource } from 'common/globalTypes';
import { sha256 } from 'crypto-hash';
import { nanoid } from 'nanoid';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useBeforeUnload } from 'react-use';
// biome-ignore lint/correctness/noUndeclaredDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { useOriginalFile } from 'store/excelStore';
import { DocumentTypeLabels } from 'common/constants/documents';

import FilePreview from '@/common/preview';
import { type SpreadsheetProps, XLS_CSV_TYPES } from '@/common/preview/model';
import useBeforeUnloadPage from '@/contexts/useBeforeunloadPage';
import useSnackbar from '@/contexts/useSnackbar';
import { useUploadStorageFileV2 } from '@/contexts/useUploadStorageFileV2';
import API from '@/services/API';
import { normalizeCurrency } from '@/services/DataTransformation/normalizer';
import Spreadsheet from '@/services/Spreadsheet';
import { readFile } from '@/services/helpers';
import { useAccountStore } from '@/store';
import useUploadStore from '@/store/uploadStore';
import type { DocumentProfileModel, IDocumentModel } from './processFlow/model';
import { EnhancedSelect } from '../molecules/EnhancedSelect';
import { SelectFileItem } from './components/SelectFileItem';

interface SelectItem {
  label: string;
  value: string;
  data: File & { path?: string };
  docType: string;
}

interface PreviewUploadsProps {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  formModel: any;
  onFinishUpload: (err: boolean) => void;
  onFileUploadSuccess: (result: {
    document: IDocumentModel;
    profile: DocumentProfileModel;
  }) => Promise<void>;
}

const PreviewUploads = forwardRef(
  ({ formModel, onFinishUpload }: PreviewUploadsProps, ref) => {
    const refSelectFile = useRef<HTMLDivElement>(null);
    const [currentPreviewFile, setCurrentPreviewFile] = useState<File>();
    const [selectOptions, setSelectOptions] = useState<SelectItem[]>([]);
    const [spreadsheet, setSpreadsheet] = useState<SpreadsheetProps>();
    const [fileHashes, setFileHashes] = useState<SpreadsheetProps>();
    const [hasDuplicateFile, setHasDuplicateFile] = useState<boolean>(false);

    const originalFile = useOriginalFile();
    const { selectedAccount } = useAccountStore();
    const { showSnackbar } = useSnackbar();

    const documentsPoster = API.getMutation('documents', 'POST');
    const documentsPatcher = API.getMutation('documents', 'PATCH');

    const { uploadFile } = useUploadStorageFileV2();
    const uploadProgresses = useUploadStore((s) => s.uploadProgresses);
    const setUploadProgress = useUploadStore((s) => s.setUploadProgress);
    const resetProgresses = useUploadStore((s) => s.resetProgresses);

    const getCompanyAndDocTypeLabel = (formModel, fileName) => {
      const company =
        formModel.companies?.[fileName]?.label ||
        formModel.companies?.label ||
        'No company';

      const docTypeValue =
        formModel.document_type?.[`./${fileName}`] ||
        formModel.document_type?.[fileName] ||
        formModel.document_type ||
        '';
      const docTypeLabel = DocumentTypeLabels[docTypeValue] || 'No type';

      return { company, docTypeLabel };
    };

    const uploading = Object.values(uploadProgresses).some(
      (item) => item === 'uploading'
    );

    useEffect(() => {
      const fetchFileHashes = async () => {
        const hashes = {};
        await Promise.all(
          selectOptions.map(async (option) => {
            const file = option.data;
            const fileName = file.name;
            const fileContent = await readFile(file);
            // @ts-expect-error - we should ensure file content is string or ArrayBuffer
            const fileHash = await sha256(fileContent);
            hashes[fileName] = fileHash;
          })
        );
        setFileHashes(hashes);
      };
      fetchFileHashes();
    }, [selectOptions]);

    useEffect(() => {
      const values = Object.values(uploadProgresses);
      const finished = values.length && !uploading;
      console.log('values', values);
      if (finished) {
        console.log('finishing');
        onFinishUpload(values.some((item) => item === 'failed'));
        resetProgresses();
      }
    }, [onFinishUpload, resetProgresses, uploadProgresses, uploading]);

    useBeforeUnload(
      uploading,
      'You have unsaved files, are you sure you want to leave?'
    );
    useBeforeUnloadPage(
      uploading,
      'You have unsaved files, are you sure you want to leave?'
    );

    const loadFilePath = (file: File) => {
      if (!selectedAccount?.accountId || !file) {
        return '';
      }
      const filename = `${nanoid()}-${file.name}`;
      const accountId = selectedAccount?.accountId;
      let filePath = 'uploads/';
      if (accountId) {
        filePath += `${accountId}/`;
      }
      filePath += filename;
      return filePath;
    };

    const loadUploadBody = async () => {
      // Loop selectOption
      const promises = selectOptions.map(async (option) => {
        const file = option.data;
        const filePath = loadFilePath(file);
        const fileContent = await readFile(file);
        // @ts-expect-error - we should ensure file content is string or ArrayBuffer
        const fileHash = await sha256(fileContent);

        const statementKey = file?.path || '';
        const statementAmount = formModel.statement_amounts?.[statementKey];
        const checkDate = formModel.check_dates?.[statementKey];
        const depositDate = formModel.deposit_dates?.[statementKey];
        const company_str_id =
          formModel.companies?.[file.name]?.value || formModel.companies?.value;
        const document_type =
          formModel.document_type?.[`./${file.name}`] ||
          formModel.document_type;
        const predicted_result =
          formModel.originalPredictedResults?.[`./${file.name}`] || null;

        let paymentMethod = '';
        if (formModel.paymentMethod) {
          if (typeof formModel.paymentMethod === 'object' && file.path) {
            paymentMethod = formModel.paymentMethod?.[`./${file.name}`] || '';
          } else {
            paymentMethod = formModel.paymentMethod;
          }
        }

        const body = {
          filename: file.name,
          file_path: filePath,
          type: document_type,
          method: '',
          processor: '',
          file_hash: fileHash,
          file,
          status: DocumentStatuses.PENDING_UPLOAD,
          company_str_id: company_str_id,
          tag: formModel.tag,
          docType: option.docType,
          statement_amount: statementAmount
            ? normalizeCurrency(statementAmount)
            : null,
          check_date: checkDate,
          deposit_date: depositDate,
          predicted_result,
          payment_method: paymentMethod,
          upload_source: UploadSource.WEB,
        };
        return body;
      });

      const bodys = await Promise.all(promises);

      return bodys;
    };

    const getFailedFileNames = () => {
      const fileNames: string[] = [];
      for (const key in uploadProgresses) {
        if (uploadProgresses[key] === 'failed') fileNames.push(key);
      }
      return fileNames;
    };

    const postUploadsInfo = async (params?: { tryAgain: boolean }) => {
      let bodys = await loadUploadBody();

      if (params?.tryAgain) {
        const failedFileNames = getFailedFileNames();
        bodys = bodys.filter((item) => failedFileNames.includes(item.filename));
      }

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      bodys.forEach(async ({ file, docType: _docType, ...rest }) => {
        try {
          const docPoster = {
            ...rest,
          };

          setUploadProgress({ filename: file.name, state: 'uploading' });
          const uploadInfo = await documentsPoster.mutateAsync(docPoster);
          const uploadResult = await uploadFile({
            file,
            action: 'write',
            endpoint: 'documents',
            endpoint_str_id: uploadInfo.str_id,
            file_preview_type: uploadInfo.override_file_path
              ? 'override'
              : 'original',
            document_id: uploadInfo.id,
          });

          if (uploadResult?.success) {
            const docPatcher = {
              id: uploadInfo.id,
              status: DocumentStatuses.NEW,
              processing: true,
              statement_amount: docPoster.statement_amount,
            };
            await documentsPatcher.mutateAsync(docPatcher);

            setUploadProgress({ filename: file.name, state: 'done' });
          }
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } catch (error: any) {
          setUploadProgress({ filename: file.name, state: 'failed' });
          if (error.message?.includes('Updates are disabled')) {
            showSnackbar(error.message, 'error');
            return;
          }
          Sentry.captureException(error);
          showSnackbar('File has failed to upload, please try again.', 'error');
        }
      });
    };

    const doChange = useCallback(async (file) => {
      if (!file) {
        return;
      }
      setCurrentPreviewFile(file);
    }, []);

    const onChange = async (filename: string) => {
      const file = selectOptions.find((item) => item.value === filename)?.data;
      doChange(file);
    };

    useEffect(() => {
      // Excel, csv need to load the raw data
      const setExcelData = async () => {
        if (
          currentPreviewFile &&
          XLS_CSV_TYPES.includes(currentPreviewFile.type)
        ) {
          const res = (await Spreadsheet.loadSpreadsheet(
            currentPreviewFile
          )) as SpreadsheetProps;
          setSpreadsheet(res);
        }
      };
      setExcelData();
    }, [currentPreviewFile]);

    useEffect(() => {
      if (originalFile) {
        setSelectOptions((prev) => {
          const newOptions = originalFile.map((file) => ({
            label: file.name,
            value: file.name,
            data: file,
            docType: 'original',
          }));

          const uniqueOptions = newOptions.filter(
            (option) => !prev.some((item) => item.label === option.label)
          );

          return [...prev, ...uniqueOptions];
        });
      }
    }, [originalFile]);

    // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    useEffect(() => {
      const selectOne = async () => {
        if (selectOptions.length > 0) {
          await doChange(selectOptions[0].data);
        }
      };
      selectOne();
    }, [doChange, originalFile, selectOptions, selectOptions.length]);

    useImperativeHandle(ref, () => ({
      submit: postUploadsInfo,
    }));

    return (
      <Box
        sx={{
          width: '100%',
          height: '100%',
          maxHeight: 'calc(100vh - 240px)',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}
      >
        <Box sx={{ mb: 1 }} ref={refSelectFile}>
          {currentPreviewFile && (
            <EnhancedSelect
              value={selectOptions.find(
                (item) => item.value === currentPreviewFile.name
              )}
              onChange={(v) => {
                onChange(v.value);
              }}
              options={selectOptions}
              label="Files"
              valueKey="value"
              sx={{ width: '100%', mt: 1 }}
              listContainerSx={{ maxWidth: '100%' }}
              containerWidth={refSelectFile.current?.clientWidth}
              renderValue={(v) => {
                if (v) {
                  const { company, docTypeLabel } = getCompanyAndDocTypeLabel(
                    formModel,
                    v.value
                  );

                  return (
                    <SelectFileItem
                      fileHashes={fileHashes}
                      hasDuplicateFile={hasDuplicateFile}
                      setHasDuplicateFile={setHasDuplicateFile}
                      filename={`${v.value} | ${company} | ${docTypeLabel}`}
                    />
                  );
                }
              }}
              renderLabel={(v) => {
                // biome-ignore lint/style/noNonNullAssertion: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                const opt = selectOptions.find((item) => item.value === v.key)!;
                const { company, docTypeLabel } = getCompanyAndDocTypeLabel(
                  formModel,
                  opt.value
                );

                return (
                  <SelectFileItem
                    sx={{ px: 2 }}
                    fileHashes={fileHashes}
                    hasDuplicateFile={hasDuplicateFile}
                    setHasDuplicateFile={setHasDuplicateFile}
                    filename={`${opt.value} | ${company} | ${docTypeLabel}`}
                  />
                );
              }}
            />
          )}
        </Box>
        {hasDuplicateFile && (
          <Typography
            variant="body2"
            color="error"
            sx={{
              marginLeft: 'auto',
              marginRight: '4px',
              textAlign: 'right',
              mb: 1,
            }}
          >
            This upload includes duplicates of existing documents (marked with a
            ➊).
          </Typography>
        )}
        <FilePreview
          previewFile={currentPreviewFile}
          previewWidth={window.innerWidth * 0.8}
          /**
           * TODO: https://linear.app/fintary/issue/PLT-2188/fix-debt-tech-for-previewuploadstsx
           *
           *      Fix this logic where we might have a bug, the following prop spreadsheet contains a field with the same name inside,
           *      exe: spreadsheet={spreadsheet?.spreadsheet}
           */

          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          spreadsheet={spreadsheet as any}
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          setSpreadsheet={setSpreadsheet as any}
        />
      </Box>
    );
  }
);
PreviewUploads.displayName = 'PreviewUploads';

export default PreviewUploads;
