import { Add, Delete, Edit, LaunchOutlined } from '@mui/icons-material';
import { Box, Button, Chip, IconButton, Tooltip } from '@mui/material';
import { useContext, useState } from 'react';
import {
  ProcessorReviewStatusesLabels,
  SystemRoles,
  StabilityLevels,
  StabilityLevelsLabels,
} from 'common/globalTypes';
import { Link } from 'react-router-dom';

import { BasicDialog } from '@/common';
import FintaryDataView from '@/components/DataView';
import { LoadingContext } from '@/contexts/LoadingContext';
import API from '@/services/API';
import ProcessorModal from '@/views/ProcessorPlayground/ProcessorModal';
import Formatter from '@/services/Formatter';
import {
  headerSx,
  cellSx,
} from '@/components/HoverActionButtonContainer/styles';
import { HoverActionButtonContainer } from '@/components/HoverActionButtonContainer';
import type { IProcessorType } from '@/views/ProcessorPlayground';

const ProcessorsView = ({ variant = '' }) => {
  const [showModal, setShowModal] = useState(false);

  const [currentRow, setCurrentRow] = useState<IProcessorType | null>(null);
  const [refresh, setRefresh] = useState(0);

  // Show comfirm dialog when deleting
  const [showConfirm, setShowConfirm] = useState(false);

  const deleter = API.getMutation('admin/processors', 'DELETE');
  const { data: owners = [] } = API.getBasicQuery('users/get_fintary_admins');

  const { setLoadingConfig } = useContext(LoadingContext);

  const statusFormatter = (s, row) => {
    const statusChip = Formatter.statusChip(ProcessorReviewStatusesLabels[s], {
      mapping: {
        [ProcessorReviewStatusesLabels.approved]: 'green',
        [ProcessorReviewStatusesLabels.in_review]: 'yellow',
        [ProcessorReviewStatusesLabels.needs_update]: 'red',
      },
    });
    return (
      <>
        {statusChip}
        {row.processor_status === 'processed' && (
          <Tooltip title="This processor is already used to process a document. If you update it now, we will create a new processor for you so that it will not affect the already processed document.">
            <Chip
              sx={{
                color: '#014361',
                backgroundColor: '#e5f1fd',
                borderRadius: 1,
                m: 0.25,
              }}
              size="small"
              label="In use"
            />
          </Tooltip>
        )}
      </>
    );
  };

  const extraActions = (
    <Button
      variant="contained"
      startIcon={<Add />}
      onClick={() => {
        setCurrentRow(null);
        setShowModal(true);
      }}
    >
      Add
    </Button>
  );

  const dataDesc = {
    label: 'Processors',
    table: 'admin/processors',
    editable: false,
    filters: [
      {
        label: 'Type',
        type: 'select',
        apiParamKey: 'type',
        defaultValue: 'statement,report',
        options: [],
      },
    ],
    fields: [
      {
        id: 'type',
        label: 'Type',
        type: 'select',
        options: ['statement', 'report'],
      },
      {
        id: 'name',
        label: 'Name',
      },
      {
        id: 'company_id',
        label: 'Company',
        formatter: (_s, row) => {
          if (row.companies_processors && row.companies_processors.length > 0) {
            const companyItems = row.companies_processors
              .map((cp) => cp.company?.company_name)
              .filter(Boolean);

            if (companyItems.length > 0) {
              return (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      flex: 1,
                      display: 'flex',
                      flexWrap: 'nowrap',
                      overflow: 'hidden',
                    }}
                  >
                    {companyItems.slice(0, 1).map((name, index) => (
                      <Chip
                        // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                        key={index}
                        label={name}
                        size="small"
                        sx={{ m: 0.1 }}
                      />
                    ))}
                    {companyItems.length > 1 && (
                      <Chip
                        size="small"
                        label={`+${companyItems.length - 1}`}
                        sx={{ m: 0.1, bgcolor: 'grey.300' }}
                      />
                    )}
                  </Box>
                  <IconButton
                    component={Link}
                    to={`/admin/companies?q=${row.str_id}`}
                    target="_blank"
                    sx={{
                      opacity: 0.5,
                      '&:hover': { opacity: 1 },
                      color: '#2196f3',
                      p: 0.5,
                      ml: 0.5,
                    }}
                  >
                    <LaunchOutlined fontSize="small" />
                  </IconButton>
                </Box>
              );
            }
          }
          return '';
        },
      },
      {
        id: 'documents',
        label: 'Documents',
        formatter: (count, row) => (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ flex: 1, px: 2 }}>
              <span>{count || 0}</span>
            </Box>
            <Box>
              {count > 0 && (
                <IconButton
                  component={Link}
                  to={`/admin/documents/documents?q=${row.str_id}`}
                  target="_blank"
                  sx={{
                    opacity: 0.5,
                    '&:hover': { opacity: 1 },
                    color: '#2196f3',
                  }}
                >
                  <LaunchOutlined />
                </IconButton>
              )}
            </Box>
          </Box>
        ),
      },
      {
        id: 'method',
        label: 'Method',
      },
      {
        id: 'stability_level',
        label: 'Stability Level',
        access: SystemRoles.ADMIN,
        formatter: (level) => {
          if (!level) return '';
          return Formatter.statusChip(StabilityLevelsLabels[level], {
            mapping: {
              [StabilityLevelsLabels[StabilityLevels.AUTO_READY]]: 'green',
              [StabilityLevelsLabels[StabilityLevels.MANUAL_REVIEW]]: 'blue',
              [StabilityLevelsLabels[StabilityLevels.MANUAL_PROCESS]]: 'orange',
              [StabilityLevelsLabels[StabilityLevels.IMPROVEMENT_NEEDED]]:
                'yellow',
              [StabilityLevelsLabels[StabilityLevels.REDESIGN_REQUIRED]]: 'red',
            },
          });
        },
      },
      {
        id: 'access',
        label: 'Access',
        access: SystemRoles.ADMIN,
      },
      {
        id: 'owner',
        label: 'Owner',
        formatter: (s) => {
          const target = owners?.find((c) => c.uid === s);
          return Formatter.contact(target);
        },
      },
      {
        id: 'status',
        label: 'Status',
        access: SystemRoles.ADMIN,
        formatter: statusFormatter,
      },
      {
        id: 'reviewer_str_id',
        label: 'Reviewer',
        access: SystemRoles.ADMIN,
        formatter: (s, row) =>
          s ? Formatter.contact(row.users_processors_reviewed_byTousers) : '',
      },
      {
        id: 'updated_at',
        label: 'Last updated',
        formatter: (s) => Formatter.date(s, { format: 'MM/DD/YY hh:mmA' }),
        readOnly: true,
      },
      {
        id: 'id',
        label: 'Actions',
        access: SystemRoles.ADMIN,
        sx: cellSx,
        headerSx,

        /**
         *
         * @param {number} _id
         * @param {*} row
         * @returns
         */
        formatter: (_id, row) => (
          <HoverActionButtonContainer>
            <IconButton
              aria-label="edit"
              onClick={() => {
                setCurrentRow(row);
                setShowModal(true);
              }}
            >
              <Edit />
            </IconButton>
            <IconButton
              aria-label="delete"
              onClick={async () => {
                setCurrentRow(row);
                setShowConfirm(true);
              }}
            >
              <Delete />
            </IconButton>
          </HoverActionButtonContainer>
        ),
      },
    ],
  };

  const closeModal = (_e) => {
    setShowModal(false);
    setRefresh(refresh + 1);
  };

  return (
    <>
      <FintaryDataView
        dataDesc={dataDesc}
        viewOnly
        extraActions={extraActions}
        refresh={refresh}
        enableAutoRefetch={true}
        variant={variant}
      />
      {showModal && (
        <ProcessorModal
          handleCancel={closeModal}
          open={showModal}
          rowData={currentRow}
        />
      )}
      {showConfirm && (
        <BasicDialog
          open={showConfirm}
          bodyComponent={
            <div>
              <h4>
                Are you sure you want to delete the processor code{' '}
                <span className="text-blue-500 pr-1">
                  {currentRow?.name || ''}
                </span>
                ?
              </h4>
              <p className="text-sm">❗❗❗ This action cannot be undone.</p>
            </div>
          }
          title="Processor delete"
          onClose={async (val) => {
            setShowConfirm(false);
            if (val) {
              setLoadingConfig({
                loading: true,
                message: 'Deleting...',
              });
              await deleter.mutateAsync({ ids: [currentRow?.id] });
              setRefresh(refresh + 1);
              setLoadingConfig({
                loading: false,
                delayToClose: 1000,
              });
            }
          }}
        />
      )}
    </>
  );
};

export default ProcessorsView;
