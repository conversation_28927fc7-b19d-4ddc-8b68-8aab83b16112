import { javascript } from '@codemirror/lang-javascript';
import { Close } from '@mui/icons-material';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  ToggleButtonGroup,
  ToggleButton,
} from '@mui/material';
import ReactCodeMirror from '@uiw/react-codemirror';
import { Allotment } from 'allotment';
import 'allotment/dist/style.css';
import Formatter from 'common/Formatter';
import { useContext, useEffect, useState } from 'react';
import { DEFAULT_PROMPT } from 'common/constants/prompt';

import { LoadingContext } from '@/contexts/LoadingContext';
import useStorageSignedUrl from '@/contexts/useStorageSignedUrl';
import useWindowSize from '@/contexts/useWindowSize';
import API from '@/services/API';
import { DocumentPreviewKeys } from '@/types';
import DocPreview from './DocPreview';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { modelOptions } from '@/constants/prompt';
import {
  TestResultParser,
  type ParsedResult,
  type TestResult,
} from '@/utils/TestResultParser';
import useSnackbar from '@/contexts/useSnackbar';

const PromptsAction = ({ rowData, handleCancel, open, owners }) => {
  const [formData, setFormData] = useState({
    name: '',
    method: '',
    fileName: '',
    fileType: '',
    selectedSheet: '',
    document: '',
    prompt: '',
    owner: '',
    company_str_id: '',
    models: [],
  });

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [curDocument, setCurDocument] = useState<any>();
  const [uploading, setUploading] = useState(false);
  const [previewFile, setPreviewFile] = useState<File | null>(null);

  const [aiLoading, setAiLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [parsedResults, setParsedResults] = useState<{
    [key: string]: ParsedResult[] | { error: string };
  }>({});
  const { showSnackbar } = useSnackbar();

  const { width: winWidth } = useWindowSize();
  const [previewWidth, setPreviewWidth] = useState(winWidth * 0.35);

  const [documentList, setDocumentList] = useState<
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    { label: string; value: string; data?: any }[]
  >([]);
  const { setLoadingConfig } = useContext(LoadingContext);
  const { getSignedUrl } = useStorageSignedUrl();

  const { data: documents, isLoading } = API.getBasicQuery('documents');
  const vertexPoster = API.getMutation('gpt/parser', 'POST');
  const promptPoster = API.getMutation('prompts', 'POST');
  const promptPatcher = API.getMutation('prompts', 'PATCH');

  useEffect(() => {
    setLoadingConfig({
      loading: isLoading,
      message: 'Loading...',
    });
  }, [isLoading, setLoadingConfig]);

  useEffect(() => {
    if (!isLoading && documents?.data.length) {
      const list = documents.data.map((item) => {
        return {
          label: item.filename,
          value: item.file_path,
          data: item,
        };
      });
      setDocumentList(list);
    }
  }, [documents, isLoading]);

  useEffect(() => {
    if (rowData && documents) {
      const { document_str_id } = rowData;
      const target = documents.data.find(
        (item) => item?.str_id === document_str_id
      );
      if (target) {
        setFormData((prev) => ({
          ...prev,
          document: target.file_path,
          name: rowData.name,
          prompt: rowData.prompt,
          owner: rowData.owner,
          models: rowData.models || [],
          company_str_id: rowData.company_str_id || target.company_str_id,
        }));
      }
    }
  }, [rowData, documents]);

  useEffect(() => {
    if (formData.document && documentList.length) {
      const target = documentList.find(
        (item) => item.value === formData.document
      );
      if (target) {
        setCurDocument(target.data);
      }
    }
  }, [formData.document, documentList]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (curDocument) {
      onFormChange('prompt', DEFAULT_PROMPT);
      onFormChange('company_str_id', curDocument.company_str_id);
      if (
        !formData.name &&
        curDocument?.companies?.company_name &&
        curDocument?.file_type
      ) {
        const _name = `${curDocument?.companies?.company_name} ${curDocument.file_type} prompt`;
        onFormChange('name', _name);
      }
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  May/2024
     * MISSED REFs: 'formData.name'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [curDocument]);

  const submit = async () => {
    setUploading(true);
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const params: any = {
      name: formData.name,
      prompt: formData.prompt,
      document_str_id: curDocument.str_id,
      status: 'draft',
      owner: formData.owner,
      access: 'global',
      company_str_id: formData.company_str_id,
      models: formData.models,
    };
    try {
      const api = rowData ? promptPatcher : promptPoster;
      if (rowData) {
        params.id = rowData.id;
      }
      const res = await api.mutateAsync(params);
      setUploading(false);
      if (res.error) {
        console.log(res.error);
        return;
      }
      handleCancel(true);
    } catch (error) {
      setUploading(false);
      console.log(error);
    }
  };

  const onDragFinished = (size) => {
    setPreviewWidth(size[0]);
  };
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const onFormChange = (key: string, value: any) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
  };

  const runVertex = async () => {
    if (!formData.document) {
      showSnackbar('Please select a document', 'error');
      return;
    }
    if (formData.models.length === 0) {
      showSnackbar('Please select at least one model', 'error');
      return;
    }
    setAiLoading(true);
    const _url = await getSignedUrl({
      endpoint_str_id: curDocument.str_id,
      file_preview_type: curDocument.override_file_path
        ? DocumentPreviewKeys.OVERRIDE
        : DocumentPreviewKeys.ORIGINAL,
      endpoint: 'documents',
      action: 'read',
    });
    const params = {
      url: _url,
      type: previewFile?.type,
      document_id: curDocument.id,
      force_run: false,
      prompt: formData.prompt,
      result_id: rowData?.result_id,
      prompt_id: rowData?.id,
      models: formData.models,
    };

    try {
      const resp = (await vertexPoster.mutateAsync(params)) as TestResult[];
      setAiLoading(false);

      const results = TestResultParser.parseResults(resp);
      setParsedResults(results);

      // Set first model as selected by default
      if (resp.length > 0) {
        setSelectedModel(resp[0].model);
      }
    } catch (error) {
      setAiLoading(false);
      console.log(error);
    }
  };

  return (
    <Dialog
      open={open}
      fullScreen
      sx={{ background: 'transparent', p: 1 }}
      onClose={handleCancel}
      disableEscapeKeyDown
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', pl: 2 }}>
          Prompt Editor
        </Box>
      </DialogTitle>
      <IconButton
        onClick={handleCancel}
        sx={{
          position: 'absolute',
          p: 4,
          right: 0,
          top: 0,
          cursor: 'pointer',
          transition: 'color 0.2s',
          '&:hover': {
            color: 'primary.main',
          },
        }}
      >
        <Close
          sx={{
            transition: 'transform 0.3s',
            transformOrigin: 'center',
            '.MuiIconButton-root:hover &': {
              transform: 'rotate(180deg)',
            },
          }}
        />
      </IconButton>
      <Divider />

      <DialogContent
        sx={{
          p: 0,
          backgroundColor: '#fff',
          borderRadius: '4px',
        }}
      >
        <section className="flex justify-between px-2 relative h-full">
          <Allotment defaultSizes={[35, 65]} onDragEnd={onDragFinished}>
            <Allotment.Pane>
              <Box className="h-full pr-3">
                {previewWidth && formData.document ? (
                  <DocPreview
                    path={formData.document}
                    previewWidth={previewWidth}
                    setPreviewFile={setPreviewFile}
                    previewFile={previewFile}
                    rowData={curDocument}
                  />
                ) : null}
              </Box>
            </Allotment.Pane>
            <Allotment.Pane>
              <Box className="flex-1 flex flex-col h-full overflow-auto pl-3">
                <Box
                  sx={{
                    flex: 1,
                    overflow: 'auto',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  <Box sx={{ p: 1, pt: 2, display: 'flex', gap: 2 }}>
                    <FormControl fullWidth sx={{ maxWidth: 250, mb: 2 }}>
                      <TextField
                        label="Name"
                        variant="outlined"
                        value={formData?.name}
                        onChange={(e) => {
                          onFormChange('name', e.target.value);
                        }}
                      />
                    </FormControl>

                    <FormControl fullWidth sx={{ maxWidth: 250, mb: 2 }}>
                      <InputLabel>Owner</InputLabel>
                      <Select
                        id="select-owner"
                        value={formData?.owner}
                        label="Owner"
                        onChange={(e) => onFormChange('owner', e.target.value)}
                      >
                        {(owners || []).map((c) => (
                          <MenuItem value={c.uid} key={c.uid}>
                            <Box
                              sx={{
                                display: 'flex',
                                width: '100%',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                gap: 1,
                              }}
                            >
                              <Box sx={{ flex: 1 }}>{Formatter.contact(c)}</Box>
                              <Box sx={{ color: '#666' }}>{c.email}</Box>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>
                  <Box sx={{ flex: 1, p: 1 }}>
                    <FormControl sx={{ mb: 2, width: '100%' }}>
                      <TextField
                        label="Prompt"
                        variant="outlined"
                        multiline
                        rows={16}
                        value={formData?.prompt}
                        onChange={(e) => {
                          onFormChange('prompt', e.target.value);
                        }}
                      />
                    </FormControl>
                  </Box>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: 2,
                      flexWrap: 'wrap',
                    }}
                  >
                    <FormControl fullWidth sx={{ maxWidth: 250, mb: 2 }}>
                      <InputLabel>Documents</InputLabel>
                      <Select
                        id="select-document"
                        value={formData?.document}
                        label="Documents"
                        onChange={(e) => {
                          onFormChange('document', e.target.value);
                        }}
                      >
                        {documentList.map((item) => (
                          <MenuItem value={item.value} key={item.value}>
                            {item.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                    <FormControl fullWidth sx={{ maxWidth: 250, mb: 2 }}>
                      <EnhancedSelect
                        valueKey="value"
                        labelKey="label"
                        options={modelOptions}
                        onChange={(value) => {
                          onFormChange(
                            'models',
                            value.map((v) => v.value)
                          );
                        }}
                        multiple
                        label="Models"
                        value={formData.models.map((m) => ({
                          value: m,
                          label:
                            modelOptions.find((o) => o.value === m)?.label ||
                            '',
                        }))}
                      />
                    </FormControl>
                    <Button
                      onClick={runVertex}
                      loading={aiLoading}
                      variant="contained"
                    >
                      Run
                    </Button>
                  </Box>

                  <Box
                    sx={{
                      width: '100%',
                      height: '100%',
                      overflow: 'auto',
                      border: '1px solid #60a5fa',
                      fontSize: '0.875rem',
                      position: 'relative',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        width: '100%',
                        height: '100%',
                        overflow: 'hidden',
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          width: '100%',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          borderBottom: '1px solid #60a5fa',
                        }}
                      >
                        <ToggleButtonGroup
                          color="primary"
                          value={selectedModel}
                          exclusive
                          onChange={(_e, value) => {
                            if (value) {
                              setSelectedModel(value);
                            }
                          }}
                        >
                          {Object.keys(parsedResults ?? {})?.length > 0 &&
                            Object.keys(parsedResults)?.map((model) => (
                              <ToggleButton
                                value={model}
                                key={model}
                                sx={{
                                  borderRadius: 1,
                                }}
                              >
                                {
                                  modelOptions.find((o) => o.value === model)
                                    ?.label
                                }
                              </ToggleButton>
                            ))}
                        </ToggleButtonGroup>
                      </Box>
                      <Box sx={{ flex: 1, overflow: 'auto' }}>
                        <ReactCodeMirror
                          value={
                            selectedModel && parsedResults[selectedModel]
                              ? JSON.stringify(
                                  parsedResults[selectedModel],
                                  null,
                                  2
                                )
                              : ''
                          }
                          width="100%"
                          height="100%"
                          readOnly={true}
                          editable={false}
                          basicSetup={{
                            lineNumbers: true,
                            foldGutter: true,
                          }}
                          extensions={[javascript({ jsx: true })]}
                        />
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Allotment.Pane>
          </Allotment>
        </section>
      </DialogContent>

      <DialogActions sx={{ pb: 2, px: 2 }}>
        <Button onClick={handleCancel}>Cancel</Button>
        <Button
          onClick={submit}
          loading={uploading}
          variant="contained"
          sx={{ width: '100px' }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

PromptsAction.displayName = 'PromptsAction';

export default PromptsAction;
