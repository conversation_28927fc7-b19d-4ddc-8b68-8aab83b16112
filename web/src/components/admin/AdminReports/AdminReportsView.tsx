import { Add, Delete, Edit } from '@mui/icons-material';
import { <PERSON>ton, Chip, IconButton, Tooltip, Typography } from '@mui/material';
import { ProcessorReviewStatusesLabels, SystemRoles } from 'common/globalTypes';
import { useContext, useState } from 'react';

// biome-ignore lint/suspicious/noShadowRestrictedNames: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import DataView from '@/components/DataView';
import { HoverActionButtonContainer } from '@/components/HoverActionButtonContainer';
import {
  cellSx,
  headerSx,
} from '@/components/HoverActionButtonContainer/styles';
import API from '@/services/API';
import Formatter from '@/services/Formatter';
import { LoadingContext } from '@/contexts/LoadingContext';
import { BasicDialog } from '@/common';
import ProcessorModal from '@/views/ProcessorPlayground/ProcessorModal';

const ReportsView = () => {
  const [showConfirm, setShowConfirm] = useState(false);
  const deleter = API.getMutation('admin/processors', 'DELETE');
  const [showModal, setShowModal] = useState(false);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [currentRow, setCurrentRow] = useState<any | null>(null);
  const [refresh, setRefresh] = useState(0);
  const { setLoadingConfig } = useContext(LoadingContext);

  const handleDeleteConfirm = async (value?: string | boolean) => {
    setShowConfirm(false);
    if (value === true) {
      setLoadingConfig({
        loading: true,
        message: 'Deleting...',
      });
      await deleter.mutateAsync({ ids: [currentRow.id] });
      setRefresh(refresh + 1);
      setLoadingConfig({
        loading: false,
        delayToClose: 1000,
      });
    }
  };

  const statusFormatter = (s, row) => {
    const statusChip = Formatter.statusChip(ProcessorReviewStatusesLabels[s], {
      mapping: {
        [ProcessorReviewStatusesLabels.approved]: 'green',
        [ProcessorReviewStatusesLabels.in_review]: 'yellow',
        [ProcessorReviewStatusesLabels.needs_update]: 'red',
      },
    });
    return (
      <>
        {statusChip}
        {row.processor_status === 'processed' && (
          <Tooltip title="This processor is already used to process a document. If you update it now, we will create a new processor for you so that it will not affect the already processed document.">
            <Chip
              sx={{
                color: '#014361',
                backgroundColor: '#e5f1fd',
                borderRadius: 1,
                m: 0.25,
              }}
              size="small"
              label="In use"
            />
          </Tooltip>
        )}
      </>
    );
  };

  const dataDesc = {
    label: 'Report processors',
    table: 'admin/processors',
    editable: false,
    fields: [
      {
        id: 'name',
        label: 'Name',
      },
      {
        id: 'type',
        label: 'Report type',
        type: 'select',
        options: [
          { id: 'policies-report', label: 'Policies' },
          { id: 'commissions-report', label: 'Commissions' },
        ],
      },
      {
        id: 'access',
        label: 'Access',
        type: 'select',
        options: [
          { id: 'global', label: 'Global' },
          { id: 'account', label: 'Account' },
        ],
      },
      {
        id: 'account',
        label: 'Account',
        formatter: (value) => {
          return <Chip label={value.name} />;
        },
      },
      {
        id: 'status',
        label: 'Status',
        access: SystemRoles.ADMIN,
        options: [
          { id: 'draft', label: 'Draft' },
          { id: 'approved', label: 'Approved' },
        ],
        formatter: statusFormatter,
      },
      {
        id: 'id',
        label: 'Actions',
        access: SystemRoles.ADMIN,
        sx: cellSx,
        headerSx,

        /**
         *
         * @param {number} id
         * @param {*} row
         * @returns
         */
        formatter: (_, row) => (
          <HoverActionButtonContainer>
            <IconButton
              aria-label="edit"
              onClick={() => {
                setCurrentRow(row);
                setShowModal(true);
              }}
            >
              <Edit />
            </IconButton>
            <IconButton
              aria-label="delete"
              onClick={async () => {
                setCurrentRow(row);
                setShowConfirm(true);
              }}
            >
              <Delete />
            </IconButton>
          </HoverActionButtonContainer>
        ),
      },
    ],
    filters: [
      {
        label: 'type',
        type: 'select',
        apiParamKey: 'type',
        defaultValue: 'policies-report,commissions-report',
        options: [],
      },
    ],
    formatters: {},
  };

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const closeModal = (_e: any) => {
    setShowModal(false);
    setRefresh(refresh + 1);
  };

  const extraActions = (
    <Button
      variant="contained"
      startIcon={<Add />}
      onClick={() => {
        setCurrentRow(null);
        setShowModal(true);
      }}
    >
      Add
    </Button>
  );

  return (
    <>
      <DataView
        viewOnly
        dataDesc={dataDesc}
        hideExport
        saveEditableFieldsOnly
        refresh={refresh}
        extraActions={extraActions}
      />
      {showModal && (
        <ProcessorModal
          handleCancel={closeModal}
          open={showModal}
          rowData={currentRow}
          isReportProcessor={true}
        />
      )}
      {showConfirm && (
        <BasicDialog
          open={showConfirm}
          bodyComponent={
            <div>
              <h4>
                Are you sure you want to delete the processor code{' '}
                <Typography
                  sx={{
                    color: '#3b82f6',
                    pr: 1,
                  }}
                >
                  {currentRow?.name || ''}
                </Typography>
                ?
              </h4>
              <Typography component="span" variant="body2">
                ❗❗❗ This action cannot be undone.
              </Typography>
            </div>
          }
          title="Processor delete"
          onClose={handleDeleteConfirm}
        />
      )}
    </>
  );
};

export default ReportsView;
