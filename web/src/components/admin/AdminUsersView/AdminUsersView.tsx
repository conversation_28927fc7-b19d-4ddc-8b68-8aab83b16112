import { getEnvVariable } from '@/env';
import { Lo<PERSON>, PersonAdd } from '@mui/icons-material';
import {
  Box,
  Button,
  Checkbox,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  TextField,
  Typography,
} from '@mui/material';
import { zodResolver } from '@hookform/resolvers/zod';
import axios from 'axios';
import * as z from 'zod';
import { useTheme } from '@mui/material/styles';
import { useState } from 'react';
import { Controller, type SubmitHandler, useForm } from 'react-hook-form';

import { useErrorResponse } from '@/hooks/useErrorResponse';
import EnhancedDataView from '@/components/organisms/EnhancedDataView';
import API from '@/services/API';

const dataSpec = {
  label: 'Users',
  table: 'admin/users',
  fields: {
    uid: {
      label: 'Uid',
      id: 'uid',
      copyable: true,
      enabled: true,
      visible: true,
    },
    email: {
      label: 'Email',
      enabled: true,
      visible: true,
    },
    first_name: {
      label: 'First name',
      enabled: true,
      visible: true,
    },
    last_name: {
      label: 'Last name',
      enabled: true,
      visible: true,
    },
    type: {
      label: 'Type',
      enabled: true,
      visible: true,
      formatter: (val?: string | null) =>
        val === 'fintary' ? (
          <img
            src="/logo192.png"
            alt="Fintary"
            style={{ width: '1.5em', height: '1.5em', verticalAlign: 'middle' }}
          />
        ) : (
          val
        ),
    },
    sub_type: {
      label: 'Sub type',
      enabled: true,
      visible: true,
    },
    account_user_roles: {
      label: 'Log in as user/account',
      enabled: true,
      visible: true,
      // TODO: identify type sharing system and use Account Type here
      formatter: (
        fieldData?: {
          account: {
            name: string;
            str_id: string;
            mode: string;
            state: string;
          };
          role: { name: string };
          id: string;
        },
        rowData?: Record<PropertyKey, unknown>
      ) => {
        if (!fieldData || !rowData) {
          return null;
        }
        return (
          <Chip
            key={fieldData?.id}
            label={`${fieldData?.account?.name} (${fieldData?.role.name})`}
            onClick={() => {
              localStorage.clear();
              localStorage.setItem('customLoginUser', JSON.stringify(rowData));
              localStorage.setItem(
                'selectedAccount',
                JSON.stringify({
                  accountId: fieldData?.account.str_id,
                  accountName: fieldData?.account.name,
                  accountMode: fieldData?.account.mode,
                  state: fieldData?.account.state,
                })
              );
              window.location.pathname = '/';
            }}
            clickable
            icon={<Login />}
            sx={{ m: 0.25 }}
          />
        );
      },
    },
  },
} as const;

const actions = [
  {
    id: 'delete_user',
    label: 'Delete user',
    onClick: async (row: { id: string | number }) => {
      const headers = await API.getHeaders();
      await axios.delete(`${getEnvVariable('API')}/api/admin/users`, {
        headers,
        params: {
          id: row.id,
        },
      });
    },
  },
];

const inviteNewUserFormSchema = z.object({
  emailAddress: z.string().email({ message: 'Invalid email address' }),
  sendEmail: z.boolean().optional(),
});

type InviteNewUserForm = z.infer<typeof inviteNewUserFormSchema>;

const AdminUsersView = () => {
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const theme = useTheme();
  const [errorMessage, setErrorMessage] = useErrorResponse({
    logLevel: 'error',
  });
  const {
    mutateAsync,
    isPending,
    isSuccess,
    reset: resetApi,
  } = API.getMutation(`users/invite_new_unassociated_user`, 'POST');
  const {
    handleSubmit,
    formState: { errors },
    control,
    watch,
    reset: resetForm,
  } = useForm<InviteNewUserForm>({
    resolver: zodResolver(inviteNewUserFormSchema),
    defaultValues: {
      emailAddress: '',
      sendEmail: true,
    },
  });

  const emailAddress = watch('emailAddress');

  const submitHandler: SubmitHandler<InviteNewUserForm> = async (data) => {
    setErrorMessage('');
    try {
      await mutateAsync(data);
    } catch (error: unknown) {
      setErrorMessage(error);
    }
  };

  const handleClose = () => {
    resetForm();
    resetApi();
    setAddUserDialogOpen(false);
  };

  return (
    <>
      <EnhancedDataView
        dataSpec={dataSpec}
        actions={actions}
        actionsEnabled={() => true}
        extraActions={[
          {
            type: 'button',
            label: 'Invite new user',
            onClick: () => setAddUserDialogOpen(true),
            icon: <PersonAdd />,
          },
        ]}
        hideAdd
        hideSelectedCount
        enableMultiSelect={false}
        enableEdit={false}
        hideExport
      />
      <Dialog
        sx={{ '& .MuiDialog-paper': { width: '80%', maxHeight: 435 } }}
        maxWidth="xs"
        open={addUserDialogOpen}
        onClose={handleClose}
        aria-labelledby="form-dialog-title"
      >
        <form onSubmit={handleSubmit(submitHandler)}>
          <DialogTitle>Invite a new user</DialogTitle>
          <DialogContent>
            <Box display="flex" flexDirection="column" gap={2}>
              <Typography variant="body2">
                A new user will be created and will not be associated with an
                existing account. A new account will be created at first sign
                in.
              </Typography>
              <Controller
                name="emailAddress"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <TextField
                    label="Email address"
                    margin="dense"
                    error={!!errors.emailAddress?.message}
                    fullWidth
                    helperText={errors?.emailAddress?.message}
                    {...field}
                  />
                )}
              />
              <Controller
                name="sendEmail"
                control={control}
                defaultValue={false}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Checkbox {...field} checked={field.value} />}
                    label="Send the user an invitation email"
                    sx={{ ml: 1 }}
                  />
                )}
              />
              {!!errorMessage && (
                <Typography color="error" align="center">
                  {errorMessage}
                </Typography>
              )}
              {isSuccess && (
                <Typography color={theme.palette.success.main} align="center">
                  Invite sent successfully to {emailAddress}
                </Typography>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            {!isSuccess && (
              <>
                <Button onClick={handleClose}>Cancel</Button>
                <Button type="submit" loading={isPending} variant="contained">
                  Submit
                </Button>
              </>
            )}
            {isSuccess && (
              <Button variant="contained" onClick={handleClose}>
                Close
              </Button>
            )}
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
};

export default AdminUsersView;
