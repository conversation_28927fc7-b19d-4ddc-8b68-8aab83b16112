import { Chip, TextField } from '@mui/material';
import { AccountIds } from 'common/constants';
import { Link, Navigate } from 'react-router-dom';

import { SyncedEntity } from '@/common/SyncedEntity';
import { SyncEndAdornment } from '@/common/SyncEndAdornment';
// biome-ignore lint/suspicious/noShadowRestrictedNames: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import DataView from '@/components/DataView';
import { useSyncedFieldsNew } from '@/contexts/useSyncedFields';
import API from '@/services/API';
import { useAccountStore, useRoleStore } from '@/store';
import { FieldTypes, Roles } from '@/types';
import { useUserInfo } from '@/hooks/useUserInfo';
import Formatter from '@/services/Formatter';
import { useIsBulkAddMode } from '@/hooks/useIsBulkAddMode';
import { useCompanies } from '@/api/companies';
import { useIsEditMode } from '@/hooks/useIsEditMode';

const View = () => {
  const { isEdit } = useIsEditMode();
  const { data: { fintaryAdmin } = {} } = useUserInfo();
  const { selectedAccount } = useAccountStore();
  const { userRole } = useRoleStore();
  const { data: accountSettings, isFetched: isFetchedAccountSettings } =
    API.getBasicQuery(`accounts/settings`);

  const { data } = useCompanies();
  const { isBulkAddMode } = useIsBulkAddMode();

  const viewSettings = accountSettings?.pages_settings?.products;

  const viewOnly = viewSettings?.read_only ?? false;

  const dataDesc = {
    label: 'Products',
    table: 'companies/products',
    editable: true,
    copyable: true,
    bulkAdd: true,
    filterConfigs: {
      cids: {
        type: 'select',
        label: 'Companies',
        options: data?.data.map((item) => ({
          id: item.str_id,
          str_id: item.str_id,
          label: item.company_name,
        })),
      },
    },
    fields: [
      {
        id: 'company_id',
        label: 'Company',
        type: 'dynamic-select',
        table: 'companies',
        field: 'id',
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        formatter: (val, collectionVals: any = []) =>
          (Array.isArray(collectionVals) &&
            collectionVals?.find((company) => company.id === val)
              ?.company_name) ||
          '',
        optionFormatter: (option) => option.company_name,
        optionValuer: (option) => option.id,
        required: true,
        bulkAddSelect: true,
      },
      {
        id: 'product_type',
        label: 'Product type',
        type: 'select',
        options: [
          'Life',
          'IUL',
          'UL',
          'Term',
          'Annuity',
          'Indexed Annuity',
          'Fixed Annuity',
          'Health',
          'Dental',
          'Vision',
          'Disability',
          'Long Term Care',
          'Other',
        ],
      },
      {
        id: 'product_name',
        label: 'Product name',
        required: true,
      },
      {
        id: 'comp_grid_products',
        type: FieldTypes.CUSTOM,
        render: (field, newData) => {
          const data = newData[field.id];
          const missingChip = Formatter.statusChip('Missing products', {
            mapping: {
              'Missing products': 'yellow',
            },
          });
          return (
            <TextField
              fullWidth
              disabled
              label={field.label}
              InputProps={{
                startAdornment:
                  data?.length > 0
                    ? data.map((compGridProduct) => (
                        <Chip
                          key={compGridProduct?.id}
                          label={compGridProduct?.name}
                          component={Link}
                          to={`/schedules/comp-grids/products?id=${compGridProduct?.str_id}`}
                          clickable
                          size="small"
                          sx={{ m: 0.25 }}
                        />
                      ))
                    : isEdit
                      ? missingChip
                      : '',
              }}
            />
          );
        },
        label: 'Comp grid products 🔒',
        formatter: (val) =>
          val ? (
            <Chip
              label={val?.name}
              component={Link}
              to={`/schedules/comp-grids/products?id=${val?.str_id}`}
              clickable
            />
          ) : (
            ''
          ),
        tableFormatter: (val) =>
          val?.length > 0
            ? val.map((compGridProduct) => (
                // biome-ignore lint/correctness/useJsxKeyInIterable: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                <Chip
                  label={compGridProduct?.name}
                  component={Link}
                  to={`/schedules/comp-grids/products?id=${compGridProduct?.str_id}`}
                  clickable
                  sx={{ m: 0.25 }}
                />
              ))
            : Formatter.statusChip('Missing products', {
                mapping: {
                  'Missing products': 'yellow',
                },
              }),
        readOnly: true,
        enabled:
          !!fintaryAdmin ||
          (userRole === Roles.ACCOUNT_ADMIN &&
            selectedAccount?.accountId === AccountIds.TRANSGLOBAL),
      },
      {
        id: 'notes',
        label: 'Notes',
      },
      {
        id: 'sync_id',
        label: 'Sync id',
        type: FieldTypes.TEXT,
      },
    ],
    actions: [
      {
        id: 'sync',
        label: 'Sync',
        type: 'custom',
        getComponent: (row) => (
          <SyncedEntity isSynced={!!row.sync_id} disabled={true} />
        ),
      },
    ],
    queryChips: {
      all: {
        id: 'all',
        label: 'All',
        query: {},
      },
      contains_products: {
        id: 'contains_products',
        label: 'Contains products',
        query: {
          comp_grid_products: 'contains_products',
        },
      },
      missing_products: {
        id: 'missing_products',
        label: 'Missing products',
        query: {
          comp_grid_products: 'missing_products',
        },
      },
    },
  };

  dataDesc.fields = dataDesc.fields.filter((field) => field.enabled !== false);

  const { workerSyncedFields, isSyncedField } = useSyncedFieldsNew();
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  dataDesc.fields.forEach((field: any) => {
    // Store static readOnly if specified, since new one overwritten below
    if (field.readOnly === true) field._readOnly = true;
    field.readOnly =
      dataDesc.bulkAdd && isBulkAddMode
        ? field.readOnly
        : (data) => {
            const syncedFields =
              workerSyncedFields?.[data?.sync_worker]?.company_products;
            if (syncedFields?.includes(field.id)) {
              return isSyncedField(data, syncedFields, field.id, data.config);
            }
            return !!field._readOnly;
          };
    field.endAdornment = (data, field, setNewData) => (
      <SyncEndAdornment
        syncedFields={workerSyncedFields?.[data?.sync_worker]?.company_products}
        syncId={data?.sync_id}
        fieldId={field?.id}
        fieldType={field.type}
        data={data}
        onChange={(newOverrideFields) => {
          setNewData({
            ...data,
            config: {
              ...(data.config || {}),
              overrideFields: newOverrideFields,
            },
          });
        }}
      />
    );
  });

  if (isFetchedAccountSettings && viewSettings?.show_page === false) {
    return (
      // TODO: Remove navigate after figuring out how to handle this in router
      <Navigate to="/settings" />
    );
  }

  if (viewSettings?.page_label) {
    dataDesc.label = viewSettings?.page_label;
  }

  return (
    <DataView
      dataDesc={dataDesc}
      viewOnly={viewOnly}
      readOnly={viewOnly}
      enablePagination
    />
  );
};

export default View;
