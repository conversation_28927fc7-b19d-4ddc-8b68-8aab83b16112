import { Delete<PERSON><PERSON><PERSON>, GroupAdd } from '@mui/icons-material';
import {
  Alert,
  Box,
  Button,
  Checkbox,
  Chip,
  FormControlLabel,
  Skeleton,
  Tooltip,
} from '@mui/material';
import CommonFormatter from 'common/Formatter';
import { AccountIds } from 'common/constants';
import { getContactFieldConfig } from 'common/field-config/contact';
import { ContactPayableStatuses } from 'common/globalTypes';
import { hasInvalidDateRange } from 'common/validators/agents/validator';
import capitalize from 'lodash-es/capitalize';
import { useContext, useEffect, useState } from 'react';
import { Link, Navigate, useSearchParams } from 'react-router-dom';
import { isEmail, isMobilePhone } from 'validator';

import { BasicDialog } from '@/common';
import { SyncEndAdornment } from '@/common/SyncEndAdornment';
import { SyncedEntity } from '@/common/SyncedEntity';
import { FieldTypes } from '@/types';
// biome-ignore lint/suspicious/noShadowRestrictedNames: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import DataView from '@/components/DataView';
import AgentsAdd from '@/components/contacts/ContactsView/AgentsAdd';
import CompProfilesAdd from '@/components/contacts/ContactsView/CompProfilesAdd';
import ContactMemosAdd from '@/components/contacts/ContactsView/ContactMemosAdd';
import ContactsLevelsAdd from '@/components/contacts/ContactsView/ContactsLevelsAdd';
import ContactsReferralsAdd from '@/components/contacts/ContactsView/ContactsReferralsAdd';
import ParentsHierarchyAdd from '@/components/contacts/ContactsView/ParentsHierarchyAdd';
import { LoadingContext } from '@/contexts/LoadingContext';
import useSnackbar from '@/contexts/useSnackbar';
import { useSyncedFieldsNew } from '@/contexts/useSyncedFields';
import { useIsBulkAddMode } from '@/hooks/useIsBulkAddMode';
import API from '@/services/API';
import DataTransformation from '@/services/DataTransformation';
import Formatter from '@/services/Formatter';
import { useAccountStore, useRoleStore } from '@/store';
import { ContactsTransactions } from './ContactsTransactions';

const AgentChip = ({ contact, parent }) => {
  const { selectedAccount } = useAccountStore();
  const startDate = contact.start_date ? new Date(contact.start_date) : null;
  const endDate = contact.end_date ? new Date(contact.end_date) : null;
  const currentDate = new Date();
  const isCurrentDateInRange =
    (startDate === null || currentDate >= startDate) &&
    (endDate === null || currentDate <= endDate);
  const agentName = CommonFormatter.contact(parent, {
    account_id: selectedAccount?.accountId,
  });
  const dateRange = CommonFormatter.dateRange(
    contact.start_date,
    contact.end_date
  );

  return (
    <Tooltip title={dateRange}>
      <Chip
        label={agentName}
        sx={{
          color: isCurrentDateInRange ? 'inherit' : '#757575',
          m: 0.2,
        }}
        clickable
        component={Link}
        to={`/agents/list?id=${parent.str_id}`}
      />
    </Tooltip>
  );
};

const ContactsView = () => {
  const Normalizer = DataTransformation;

  const { userRole } = useRoleStore();
  const { isBulkAddMode } = useIsBulkAddMode();
  const [showDelete, setShowDelete] = useState(false);
  const [currentRow, setCurrentRow] = useState(null);
  const [showConfig, setShowConfig] = useState({
    open: false,
    refresh: 0,
  });
  const [showModal, setShowModal] = useState(false);
  const [refresh, setRefresh] = useState(0);
  const [searchParams, setSearchParams] = useSearchParams({});
  const { setLoadingConfig } = useContext(LoadingContext);
  const { selectedAccount } = useAccountStore();
  const { showSnackbar } = useSnackbar();
  const { workerSyncedFields, isSyncedField } = useSyncedFieldsNew();

  const { data: accountSettings, isFetched: isFetchedAccountSettings } =
    API.getBasicQuery(`accounts/settings`);
  const viewSettings = accountSettings?.pages_settings?.agents;
  const viewOnly = viewSettings?.read_only ?? false;
  const { data: contactGroups } = API.getBasicQuery('contacts/groups');
  const { data: contactOptions, isFetched: isFetchedContactOptions } =
    API.getBasicQuery('contacts/options');
  const contactsDeleter = API.getMutation('report_data/agents', 'DELETE');
  const isAccountTransactionsEnabled =
    selectedAccount?.accountingTransactionsEnabled;

  const formValidator = (data) => {
    const invalidDateRangeKey = hasInvalidDateRange(data);
    // Labels for fields that are not defined in the dataDesc.fields
    const labelList = {
      contact_level: 'Grid levels',
    };

    let label = dataDesc.fields.find(
      (field) => field.id === invalidDateRangeKey
    )?.label;

    if (!label) {
      label = labelList[invalidDateRangeKey];
    }

    const message = 'start date must be before end date';

    return invalidDateRangeKey ? `${label}: ${message}` : undefined;
  };

  const config = getContactFieldConfig();

  const dataDesc = {
    ...config,
    formConfig: {
      validateData: formValidator,
    },
    fields: [
      [
        {
          id: 'first_name',
          ...config.fields.first_name,
        },
        {
          id: 'last_name',
          ...config.fields.last_name,
        },
        {
          id: 'nickname',
          ...config.fields.nickname,
        },
      ],
      [
        {
          id: 'email',
          validator: (val) => val === '' || isEmail(val),
          ...config.fields.email,
        },
        {
          id: 'phone',
          validator: (val) => val === '' || isMobilePhone(val, 'en-US'),
          ...config.fields.phone,
        },
        { id: 'agent_code', ...config.fields.agent_code },
        { id: 'bank_info', ...config.fields.bank_info },
      ],
      [
        {
          id: 'contact_group_id',
          ...config.fields.contact_group_id,
          formatter: (val, collectionVals = []) => {
            if (val === '') return '';
            if (Array.isArray(collectionVals) && collectionVals.length > 0) {
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              const record: any = collectionVals?.filter(
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                (datum: any) => datum?.id === val
              )?.[0];
              return record?.name ?? '';
            }
            return val; // Not formatting when collectionVals is not available
          },
          optionFormatter: (option) => option?.name,
          optionValuer: (option) => option?.id,
          readOnly: (row) => ![null, undefined].includes(row?.parent_id),
        },
        {
          id: 'company_name',
          ...config.fields.company_name,
        },
      ],
      [
        {
          id: 'type',
          ...config.fields.type,
        },
        {
          id: 'start_date',
          label: 'Start date',
          type: FieldTypes.DATE,
          normalizer: Normalizer.normalizeDate,
          formatter: Normalizer.formatDate,
        },
        {
          id: 'status',
          ...config.fields.status,
          options: [
            ...new Set([
              '',
              'active',
              'archived',
              ...(contactOptions?.status ?? []),
            ]),
          ],
          condition: () => isFetchedContactOptions,
        },
        {
          id: 'payable_status',
          ...config.fields.payable_status,
          options: [
            ...new Set([
              '',
              ContactPayableStatuses.PAYABLE,
              ContactPayableStatuses.NON_PAYABLE,
              ...(contactOptions?.agent_payable_status ?? []),
            ]),
          ],
          condition: () => isFetchedContactOptions,
          formatter: (val) => capitalize(val),
        },
      ],
      {
        id: 'contact_level',
        ...config.fields.contact_level,
        tableFormatter: (_field, row) => {
          if (Array.isArray(row.contact_level)) {
            return row.contact_level.map((level) => (
              <Box key={level.id}>
                {level.level_label && (
                  <Chip label={level.level_label} sx={{ m: 0.5 }} />
                )}
                {level.product_type && (
                  <Chip label={level.product_type} sx={{ m: 0.5 }} />
                )}
                {level.loa && <Chip label="LOA" sx={{ m: 0.5 }} />}
              </Box>
            ));
          }
        },
        render: (field, row, setter, dynamicSelects) => (
          <ContactsLevelsAdd
            key="contact_levels_add"
            data={row}
            setter={setter}
            field={field}
            isSyncedField={isSyncedField}
            syncedFields={
              workerSyncedFields?.[row?.sync_worker]?.contact_levels
            }
            dynamicSelects={dynamicSelects}
          />
        ),
      },
      {
        id: 'agency_contact_levels',
        ...config.fields.agency_contact_levels,
        tableFormatter: (_field, row) => {
          if (Array.isArray(row.agency_contact_levels)) {
            return row.agency_contact_levels.map((level) => (
              <Box key={level.id}>
                {level.level_label && (
                  <Chip label={level.level_label} sx={{ m: 0.5 }} />
                )}
                {level.product_type && (
                  <Chip label={level.product_type} sx={{ m: 0.5 }} />
                )}
                {level.loa && <Chip label="LOA" sx={{ m: 0.5 }} />}
              </Box>
            ));
          }
        },
        render: (field, row, setter, dynamicSelects) => (
          <ContactsLevelsAdd
            key="agency_contact_levels_add"
            data={row}
            setter={setter}
            field={field}
            isSyncedField={isSyncedField}
            syncedFields={
              workerSyncedFields?.[row?.sync_worker]?.agency_contact_levels
            }
            dynamicSelects={dynamicSelects}
          />
        ),
      },
      {
        id: 'parent_relationships',
        ...config.fields.parent_relationships,
        formatter: (val) => {
          if (typeof val === 'object') {
            if (val.parent) {
              return <AgentChip contact={val} parent={val.parent} />;
            } else return null;
          }
          return val;
        },
        render: (field, row, setter, dynamicSelects) => (
          <ParentsHierarchyAdd
            key="parent_relationships_add"
            data={row}
            setter={setter}
            field={field}
            syncedFields={
              workerSyncedFields?.[row?.sync_worker]?.contact_hierarchy
            }
            isSyncedField={isSyncedField}
            dynamicSelects={dynamicSelects}
          />
        ),
      },
      {
        id: 'child_relationships',
        ...config.fields.child_relationships,
        linker: (val) => `/agents/list?id=${val?.contact?.str_id}`,
        formatter: (val) =>
          Formatter.contact(val?.contact, {
            account_id: selectedAccount?.accountId,
          }),
        optionFormatter: (val) =>
          Formatter.contact(val?.contact, {
            account_id: selectedAccount?.accountId,
          }),
        optionValuer: (val) => val?.id, // Not used to save
      },
      {
        id: 'contacts_agent_commission_schedule_profiles_sets',
        ...config.fields.contacts_agent_commission_schedule_profiles_sets,
        formatter: (val, collectionVals = []) => {
          const _val =
            typeof val === 'object'
              ? val.agent_commission_schedule_profile_set_id
              : val;
          if (!Array.isArray(collectionVals))
            return (
              <Skeleton
                variant="rounded"
                width={160}
                height={24}
                sx={{ m: 0.25 }}
              />
            );
          if (collectionVals.length > 0) {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const record: any = collectionVals?.find(
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              (datum: any) => datum?.id === _val
            );
            const name = record?.name;
            return name ?? 'Not found';
          } else return _val?.str_id ?? _val?.id ?? _val?.label;
        },
        optionValuer: (option) => {
          return option?.id;
        },
        render: (field, row, setter, dynamicSelects) => (
          <CompProfilesAdd
            key="contacts_agent_commission_schedule_profiles_sets"
            data={row}
            setter={setter}
            field={field}
            dynamicSelects={dynamicSelects}
            type={'set'}
          />
        ),
      },
      {
        id: 'contacts_agent_commission_schedule_profiles',
        ...config.fields.contacts_agent_commission_schedule_profiles,
        formatter: (val, collectionVals = []) => {
          const _val =
            typeof val === 'object'
              ? val.agent_commission_schedule_profile_id
              : val;
          if (!Array.isArray(collectionVals))
            return (
              <Skeleton
                variant="rounded"
                width={160}
                height={24}
                sx={{ m: 0.25 }}
              />
            );
          if (collectionVals.length > 0) {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const record: any = collectionVals?.find(
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              (datum: any) => datum?.id === _val
            );
            const name = record?.name;
            return name ?? 'Not found';
          } else return _val?.str_id ?? _val?.id ?? _val?.label;
        },
        optionValuer: (option) => {
          return option?.id;
        },
        render: (field, row, setter, dynamicSelects) => (
          <CompProfilesAdd
            key="contacts_agent_commission_schedule_profiles"
            data={row}
            setter={setter}
            field={field}
            dynamicSelects={dynamicSelects}
          />
        ),
      },
      {
        id: 'contact_memos',
        ...config.fields.contact_memos,
        tableFormatter: (field, _row) => field.length || '',
        render: (field, row, setter) => (
          <ContactMemosAdd
            key="contact_memos_add"
            data={row}
            setter={setter}
            field={field}
          />
        ),
      },
      {
        id: 'contact_referrals',
        ...config.fields.contact_referrals,
        tableFormatter: (field, _row) => field.length || '',
        render: (field, row, setter, dynamicSelects) => (
          <ContactsReferralsAdd
            key="contact_referralss_add"
            data={row}
            setter={setter}
            field={field}
            dynamicSelects={dynamicSelects}
          />
        ),
      },
      {
        id: 'notes',
        ...config.fields.notes,
      },
      {
        id: 'saved_reports',
        ...config.fields.saved_reports,
        formatter: (val) => {
          if (val)
            return (
              <Chip
                key={val.str_id}
                label={val.name}
                clickable
                component={Link}
                to={`/reports/${val.str_id}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            );
          return '';
        },
        optionFormatter: (val) => val.name,
      },
      {
        id: 'user_contact',
        ...config.fields.user_contact,
        formatter: (s, row) => {
          if (s) {
            const account = row?.user_contact?.account_user_roles?.find(
              (account) => account.account_id === selectedAccount?.accountId
            );
            if (account) {
              return account.state;
            } else {
              return 'No access';
            }
          } else {
            return 'No access';
          }
        },
      },
      {
        id: 'str_id',
        ...config.fields.str_id,
      },
      {
        id: 'sync_id',
        ...config.fields.sync_id,
      },
      {
        id: 'account_role_settings_id',
        ...config.fields.account_role_settings_id,
        formatter: (val, collectionVals = []) => {
          if (val === '') return '';
          if (Array.isArray(collectionVals) && collectionVals.length > 0) {
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            const record: any = collectionVals?.filter(
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              (datum: any) => datum?.id === val
            )?.[0];

            return record?.custom_view_name ?? '';
          }
          return val;
        },
        optionFormatter: (option) => option?.custom_view_name,
        optionValuer: (option) => option?.id,
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    ] as any,
    actions: [
      {
        label: 'Delete',
        type: 'iconButton',
        icon: <DeleteOutline />,
        onClick: async (row) => {
          setCurrentRow(row);
          setShowDelete(true);
        },
      },
      {
        label: 'Synced',
        type: 'icon',
        enabled: (row) => !!row.sync_id,
        icon: <SyncedEntity isSynced={true} disabled />,
      },
    ],
    queryChips: {},
  };

  if (viewSettings?.page_label) {
    dataDesc.label = viewSettings?.page_label;
  }
  if (isAccountTransactionsEnabled) {
    dataDesc.fields.push(
      {
        id: 'accounting_transactions',
        ...config.fields.accounting_transactions,
        tableFormatter: (_field, row) => {
          if (row && Array.isArray(row.saved_reports)) {
            return row.saved_reports.length || '';
          }
          return '';
        },
        render: (field, row, _setter) => (
          <ContactsTransactions
            field={field}
            userRole={userRole}
            agentStrId={row.str_id}
          />
        ),
      },
      {
        id: 'balance',
        ...config.fields.balance,
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
      }
    );
  }

  useEffect(() => {
    if (typeof showConfig === 'boolean') {
      setShowModal(showConfig);
    } else {
      setRefresh(showConfig.refresh);
      setShowModal(showConfig.open);
    }
  }, [showConfig]);

  // TODO: Move this to router or higher level component
  if (isFetchedAccountSettings && viewSettings?.show_page === false) {
    return <Navigate to="/settings" />;
  }

  const extraActions = [
    <Button
      variant="outlined"
      startIcon={<GroupAdd />}
      onClick={() => {
        // SetShowModal(true);
        setShowConfig({ open: true, refresh: 0 });
      }}
      sx={{ mr: 1 }}
      key="pullFromPolicies"
    >
      Create from policies
    </Button>,
  ];

  // Temporary filter until we move over to EnhancedDataView
  if (
    selectedAccount?.accountId === AccountIds.TRANSGLOBAL &&
    !searchParams.get('id')
  ) {
    extraActions.unshift(
      <FormControlLabel
        control={
          <Checkbox checked={searchParams.get('show_inactive') === 'true'} />
        }
        label="Show inactive"
        sx={{ mr: 1 }}
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        onChange={(e: any) => {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          setSearchParams((prev: any) => {
            if (e.target.checked) {
              prev.set('show_inactive', 'true');
            } else {
              prev.delete('show_inactive');
            }
            return prev;
          });
        }}
        key="inactiveFilter"
      />
    );
  }

  if (Array.isArray(contactGroups) && contactGroups.length > 0) {
    const queryChips = [
      {
        id: '0',
        name: 'All',
        query: {},
      },
      ...(contactGroups ?? []),
    ];

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    queryChips.forEach((group) => {
      if (dataDesc)
        dataDesc.queryChips[group?.id] = {
          id: String(group?.id),
          label: group.name,
          query: { contact_group_id: group?.id },
        };
    });
  }

  const DelConfirmComp = ({ row }) => {
    return (
      <BasicDialog
        title="Delete agent"
        open={showDelete}
        onClose={(val) => {
          if (val) {
            const accountUserRole = row?.user_contact?.account_user_roles?.find(
              (accountUserRole) =>
                accountUserRole.account_id === selectedAccount?.accountId
            );
            if (!row.user_contact || accountUserRole.state === 'deleted') {
              setLoadingConfig({
                loading: true,
                message: 'Deleting...',
              });
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              const params = { ids: [row.id], strIds: [row.str_id] } as any;
              contactsDeleter
                .mutateAsync(params)
                .then(() => {
                  setRefresh(refresh + 1);
                  setLoadingConfig({
                    loading: false,
                    message: '',
                    delayToClose: 1000,
                  });
                  setCurrentRow(null);
                })
                .catch((err) => {
                  showSnackbar(err, 'error');
                  setLoadingConfig({
                    loading: false,
                    message: '',
                  });
                });
            } else {
              setShowDelete(false);
              setCurrentRow(null);
            }
          } else {
            setShowDelete(false);
            setCurrentRow(null);
          }
        }}
        bodyComponent={
          row?.user_contact?.account_user_roles?.find(
            (account) => account.account_id === selectedAccount?.accountId
          ).state === 'deleted' || !row.user_contact ? (
            <Alert severity="warning">
              Are you sure you want to delete this agent?
              <br />
              <br />
              {Formatter.contact(row, {
                account_id: selectedAccount?.accountId,
              })}
            </Alert>
          ) : (
            <Alert severity="warning">
              You can't delete the contact{' '}
              {Formatter.contact(row, {
                account_id: selectedAccount?.accountId,
              })}{' '}
              because it has a user associated. Please delete the user first.
            </Alert>
          )
        }
        positiveLabel="Delete"
      />
    );
  };

  const disableSyncedFields = (field) => {
    if (dataDesc.bulkAdd && isBulkAddMode) field.readOnly = false;
    else {
      field.readOnly =
        field.readOnly ||
        ((data) => {
          const syncedFields =
            workerSyncedFields?.[data?.sync_worker]?.contacts;
          if (syncedFields?.includes(field.id)) {
            return isSyncedField(data, syncedFields, field.id, data.config);
          }
          return false;
        });
    }
    field.endAdornment = (data, field, setNewData) => (
      <SyncEndAdornment
        syncedFields={workerSyncedFields?.[data?.sync_worker]?.contacts}
        syncId={data?.sync_id}
        fieldId={field?.id}
        data={data}
        fieldType={field.type}
        onChange={(newOverrideFields) => {
          setNewData({
            ...data,
            config: {
              ...(data.config || {}),
              overrideFields: newOverrideFields,
            },
          });
        }}
      />
    );
  };
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  dataDesc.fields.forEach((field) => {
    if (Array.isArray(field)) {
      field.forEach(disableSyncedFields);
    } else {
      disableSyncedFields(field);
    }
  });

  return (
    <>
      {dataDesc && (
        <DataView
          headingOffset={104}
          dataDesc={dataDesc}
          extraActions={extraActions}
          refresh={refresh}
          viewOnly={viewOnly}
          readOnly={viewOnly}
          enablePagination
        />
      )}
      {showModal && <AgentsAdd open={showModal} setOpen={setShowConfig} />}
      {showDelete && currentRow && <DelConfirmComp row={currentRow} />}
    </>
  );
};

export default ContactsView;
