import { Close } from '@mui/icons-material';
import {
  Box,
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormHelperText,
  IconButton,
  TextField,
  Autocomplete,
  alpha,
} from '@mui/material';
import { useEffect, useState, useRef } from 'react';
import validator from 'validator';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import dayjs from 'dayjs';
import { PAYMENT_METHODS } from 'common/constants';

import BasicDatePicker from '@/components/molecules/BasicDatePicker';
import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import { useUserInfo } from '@/hooks/useUserInfo';
import { useCompanies } from '@/api/companies';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { useDocumentProfileList } from './hooks/useDocumentProfileList';
import { Status, Types } from './constants';

const ActionDialog = ({ open, setOpen, rowData }) => {
  const { data: { data: companies } = { data: [] } } = useCompanies();
  const { documentProfileOptions } = useDocumentProfileList();
  const { showSnackbar } = useSnackbar();

  const { data: { fintaryAdmin } = {} } = useUserInfo();

  const [highlight, setHighlight] = useState(null);
  const originalDataRef = useRef(null);

  useEffect(() => {
    if (open && rowData && rowData._highlightField) {
      setHighlight(rowData._highlightField);
    }
  }, [open, rowData]);

  const schema = z.object({
    statement_month: z
      .string()
      .nullable()
      .optional()
      .refine((val) => {
        if (val) return dayjs(val).isValid();
        return true;
      }),
    bank_total_amount: z
      .string()
      .transform((val) => (val === '' ? null : val))
      .nullable()
      .refine(
        (val) =>
          val === null ||
          validator.isCurrency(val, {
            allow_negatives: true,
            thousands_separator: ',',
            decimal_separator: '.',
            allow_decimal: true,
            digits_after_decimal: [1, 2],
          }),
        { message: 'Invalid currency format' }
      ),
    statement_amount: z
      .string()
      .transform((val) => (val === '' ? null : val))
      .nullable()
      .refine(
        (val) =>
          val === null ||
          validator.isCurrency(val, {
            allow_negatives: true,
            thousands_separator: ',',
            decimal_separator: '.',
            allow_decimal: true,
            digits_after_decimal: [1, 2],
          }),
        { message: 'Invalid currency format' }
      ),
    check_date: z
      .string()
      .nullable()
      .optional()
      .refine((val) => {
        if (val) return dayjs(val).isValid();
        return true;
      }),
    company_str_id: z.string({ message: 'Company is required' }),
    profile_str_id: z.string().nullable().optional(),
    deposit_date: z
      .string()
      .nullable()
      .optional()
      .refine((val) => {
        if (val) return dayjs(val).isValid();
        return true;
      }),
    notes: z.string().nullable().optional(),
    status: z.string({ message: 'Required' }),
    type: z.string({ message: 'Document type is required' }),
    payment_method: z.string().nullable().optional(),
    validations: z.any().optional(),
  });

  const {
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    register,
    reset,
  } = useForm({
    resolver: zodResolver(schema),
    shouldFocusError: false,
  });

  const type = watch('type');
  const company_str_id = watch('company_str_id');
  const profile_str_id = watch('profile_str_id');
  const status = watch('status');
  const deposit_date = watch('deposit_date');
  const statement_month = watch('statement_month');
  const check_date = watch('check_date');
  const payment_method = watch('payment_method');

  const apiPutter = API.getMutation('documents', 'PUT');

  useEffect(() => {
    if (rowData) {
      originalDataRef.current = { ...rowData };
      reset(rowData);
    }
  }, [reset, rowData]);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const onSubmit = async (data: any) => {
    const { id, str_id } = rowData;

    const fieldsToCheck = ['company_str_id', 'type', 'statement_amount'];
    const updatedValidations = { ...(data.validations || {}) };

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    fieldsToCheck.forEach((field) => {
      const originalValue = originalDataRef.current?.[field];
      const currentValue = data[field];

      const valueChanged = originalValue !== currentValue;

      if (valueChanged && field in updatedValidations) {
        updatedValidations[field] = false;
      }
    });

    const body = {
      ...data,
      str_id,
      id,
      validations: updatedValidations,
    };

    try {
      const result = await apiPutter.mutateAsync(body);
      if (result.error) {
        showSnackbar(result.error, 'error');
        return;
      }
      if (result) {
        setOpen(false, 'save');
      }
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } catch (error: any) {
      showSnackbar(error?.message || error, 'error');
    }
  };

  const isHighlighted = (fieldName) => highlight === fieldName;

  const getHighlightProps = (fieldName) => {
    if (!isHighlighted(fieldName)) return {};

    return {
      sx: {
        '& .MuiOutlinedInput-root': {
          backgroundColor: alpha('#f44336', 0.02),
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'error.main',
            borderWidth: '1px',
          },
        },
      },
    };
  };

  return (
    <Dialog
      open={open}
      maxWidth="md"
      fullWidth
      sx={{ background: 'transparent', p: 1 }}
      onClose={() => setOpen(false)}
      disableEscapeKeyDown
    >
      <DialogTitle>Edit document</DialogTitle>
      <IconButton
        onClick={() => setOpen(false)}
        sx={{
          position: 'absolute',
          right: 8,
          top: 8,
          cursor: 'pointer',
          transition: 'color 0.2s',
          '&:hover': {
            color: 'primary.main',
          },
        }}
      >
        <Close
          sx={{
            transition: 'transform 0.3s',
            transformOrigin: 'center',
            '.MuiIconButton-root:hover &': {
              transform: 'rotate(180deg)',
            },
          }}
        />
      </IconButton>
      <Divider />

      <DialogContent
        sx={{
          p: 0,
          backgroundColor: '#fff',
          borderRadius: '4px',
        }}
      >
        <Box
          sx={{
            width: '100%',
            maxHeight: '700px',
            display: 'flex',
            flexDirection: 'column',
            gap: 1.5,
            p: 2,
          }}
        >
          <FormControl fullWidth sx={{ minWidth: 100 }}>
            <EnhancedSelect
              label="Type"
              sx={{ width: '100%' }}
              value={Types.find((item) => item.id === type)}
              options={Types}
              onChange={(value) => {
                setValue('type', value.id, { shouldValidate: true });
              }}
              {...getHighlightProps('type')}
            />
            <FormHelperText error={!!errors.type?.message}>
              {errors.type?.message as string}
            </FormHelperText>
          </FormControl>
          <FormControl fullWidth sx={{ minWidth: 100 }}>
            <EnhancedSelect
              label="Company"
              sx={{ width: '100%' }}
              value={companies.find(
                (company) => company.str_id === company_str_id
              )}
              labelKey="company_name"
              valueKey="str_id"
              options={companies}
              onChange={(value) => {
                setValue('company_str_id', value.str_id, {
                  shouldValidate: true,
                });
              }}
              {...getHighlightProps('company_str_id')}
            />
            <FormHelperText error={!!errors.company_str_id?.message}>
              {errors.company_str_id?.message as string}
            </FormHelperText>
          </FormControl>
          <FormControl>
            <BasicDatePicker
              label={'Statement month'}
              openTo="month"
              views={['year', 'month']}
              value={statement_month}
              setValue={(v: string) => {
                setValue('statement_month', v, { shouldValidate: true });
              }}
            />
          </FormControl>
          <FormControl fullWidth sx={{ minWidth: 100 }}>
            <Autocomplete
              freeSolo
              options={PAYMENT_METHODS}
              value={payment_method || ''}
              onInputChange={(_event, newInputValue) => {
                setValue('payment_method', newInputValue, {
                  shouldValidate: true,
                });
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Payment Method"
                  error={!!errors.payment_method?.message}
                  helperText={errors.payment_method?.message as string}
                  sx={{
                    '& .MuiInputBase-root': {
                      height: 35,
                    },
                  }}
                />
              )}
            />
          </FormControl>
          {fintaryAdmin && (
            <FormControl fullWidth sx={{ minWidth: 100 }}>
              <EnhancedSelect
                enableSearch
                label="Document profiles 🔒"
                sx={{ width: '100%' }}
                listContainerSx={{
                  width: 800,
                }}
                value={documentProfileOptions?.find(
                  (profile) => profile.id === profile_str_id
                )}
                options={documentProfileOptions}
                onChange={(value) => {
                  setValue('profile_str_id', value.id, {
                    shouldValidate: true,
                  });
                }}
              />
              <FormHelperText error={!!errors.profile_str_id?.message}>
                {errors.profile_str_id?.message as string}
              </FormHelperText>
            </FormControl>
          )}
          {fintaryAdmin && (
            <FormControl fullWidth sx={{ minWidth: 100 }}>
              <EnhancedSelect
                label="Status 🔒"
                sx={{ width: '100%' }}
                value={Status.find((item) => item.id === status)}
                options={Status}
                onChange={(value) => {
                  setValue('status', value.id, { shouldValidate: true });
                }}
              />
              <FormHelperText error={!!errors.status?.message}>
                {errors.status?.message as string}
              </FormHelperText>
            </FormControl>
          )}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
            }}
          >
            <FormControl fullWidth sx={{ maxWidth: 250 }}>
              <TextField
                size="small"
                label="Bank total"
                variant="outlined"
                {...register('bank_total_amount')}
                error={!!errors?.bank_total_amount?.message}
                helperText={
                  (errors?.bank_total_amount?.message || '') as string
                }
                sx={{
                  '& .MuiInputBase-root': {
                    height: 35,
                  },
                }}
              />
            </FormControl>
            <BasicDatePicker
              label="Deposit date"
              value={deposit_date}
              setValue={(e) =>
                setValue('deposit_date', e, { shouldValidate: true })
              }
              sx={{ width: 300 }}
            />
          </Box>
          <TextField
            label="Statement total"
            variant="outlined"
            {...register('statement_amount')}
            error={!!errors?.statement_amount?.message}
            helperText={(errors?.statement_amount?.message || '') as string}
            sx={{
              '& .MuiInputBase-root': {
                height: 35,
                ...(highlight === 'statement_amount' && {
                  backgroundColor: alpha('#f44336', 0.02),
                }),
              },
              '& .MuiOutlinedInput-notchedOutline': {
                ...(highlight === 'statement_amount' && {
                  borderColor: 'error.main',
                  borderWidth: '1px',
                }),
              },
            }}
          />

          <Box>
            <BasicDatePicker
              label="Check date"
              value={check_date}
              setValue={(e) =>
                setValue('check_date', e, { shouldValidate: true })
              }
              sx={{ width: 250 }}
            />
          </Box>

          <FormControl fullWidth sx={{ minWidth: 100, mb: 2 }}>
            <TextField
              label="Notes"
              variant="outlined"
              multiline
              rows={2}
              {...register('notes')}
            />
          </FormControl>
        </Box>
      </DialogContent>

      <DialogActions sx={{ pt: 0, pb: 2, px: 2 }}>
        <Button onClick={() => setOpen(false)}>Cancel</Button>
        <Button
          onClick={handleSubmit(onSubmit)}
          loading={apiPutter.isPending}
          variant="contained"
          sx={{ width: '100px' }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ActionDialog;
