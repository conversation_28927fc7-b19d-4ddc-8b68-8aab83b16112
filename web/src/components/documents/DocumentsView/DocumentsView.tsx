import { Al<PERSON>, FormControlLabel, Checkbox } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useContext, useEffect, useState, useRef } from 'react';
import { Navigate } from 'react-router-dom';

import { BasicDialog, FileDialogPreview } from '@/common';
import { PDF_HTML_IMG_TYPES } from '@/common/preview/model';
import UploadOverrideFile from '@/components/documents/DocumentsView/DocumentOverrideFile';
import ExtractMethod from '@/components/documents/DocumentsView/ExtractMethod';
import UpdateProcessData from '@/components/documents/DocumentsView/UpdateProcessData';
import EnhancedDataView from '@/components/organisms/EnhancedDataView';
import { LoadingContext } from '@/contexts/LoadingContext';
import { UIStateContext } from '@/contexts/UIStateProvider';
import useDownloadStorageFile from '@/contexts/useDownloadStorageFile';
import usePreviewParams from '@/contexts/usePreviewParams';
import API from '@/services/API';
import { useSetOriginFile } from '@/store/excelStore';
import ActionDialog from './ActionDialog';
import useSnackbar from '@/contexts/useSnackbar';
import { useDocumentViewDesc } from './hooks/useDocumentViewDesc';
import { useFilters } from './hooks/useFilters';
import { useExtraActions } from './hooks/useExtraActions';
import { SyncedEntity } from '@/common/SyncedEntity';
import { useAccountStore } from '@/store';
import BulkSyncComponent from './components/BulkSyncComponent';

const DocumentsView = () => {
  const { showSnackbar } = useSnackbar();
  const [open, setOpen] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showExtract, setShowExtract] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [processLoading, setProcessLoading] = useState<number | ''>('');
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [activeRow, setActiveRow] = useState<any>();

  const [showEdit, setShowEdit] = useState<boolean | null>(null);

  const [sync, setSync] = useState({ documentId: '', show: false, count: 0 });

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [rowData, setRowData] = useState<any>({});
  const { setLoadingConfig } = useContext(LoadingContext);
  const [deleteRecords, setDeleteRecords] = useState(false);

  const setUploadedFile = useSetOriginFile();
  const { downloadFile } = useDownloadStorageFile();

  const { filtersData } = useFilters();
  const { extraActions } = useExtraActions();
  const queryClient = useQueryClient();

  const activeRowRef = useRef<boolean>(false);

  const { dataDesc } = useDocumentViewDesc({
    setProcessLoading,
    setActiveRow,
    setRowData,
    setShowUploadModal,
    setSync,
    setShowEdit,
    setShowConfirm,
    activeRowRef,
  });

  const { data: accountSettings, isFetched: isFetchedAccountSettings } =
    API.getBasicQuery(`accounts/settings`);
  const [selectedData, setSelectedData] = useState<
    { id: number; str_id: string }[]
  >([]);

  const { selectedAccount } = useAccountStore();
  const { showPreview, setShowPreview, previewId } = usePreviewParams();

  const {
    // biome-ignore lint/correctness/noEmptyPattern: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    role: [],
  } = useContext(UIStateContext);

  const syncStatement = API.getMutation(
    'data_processing/sync/benefit-point/statements',
    'POST'
  );
  const viewSettings = accountSettings?.pages_settings?.documents;

  const documentsDelete = API.getMutation('documents', 'DELETE');
  const { data: activeRowData, isLoading: isActiveRowLoading } =
    API.getBasicQuery(
      'extractions',
      `document_id=${activeRow?.id}`,
      !!activeRow?.id
    );

  const BulkSync = ({
    key,
    isWorkingToolbar,
    setIsWorkingToolbar,
    refetch,
  }) => {
    return (
      <BulkSyncComponent
        key={key}
        isWorkingToolbar={isWorkingToolbar}
        setIsWorkingToolbar={setIsWorkingToolbar}
        refetch={refetch}
        selectedData={selectedData}
        selectedAccount={selectedAccount}
        syncToBenefit={syncToBenefit}
      />
    );
  };

  // Hard coded for now, will be dynamic later
  const syncToBenefit = async (data) => {
    try {
      setLoadingConfig({
        loading: true,
        message: 'Syncing...',
      });
      const ret = await syncStatement.mutateAsync(data);
      if (ret.success === false) {
        showSnackbar(
          <Alert severity="error">
            {ret.message.split('\n').map((r, index) => (
              // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              <div key={index}>{r}</div>
            ))}
          </Alert>,
          'error'
        );
      } else {
        const { actualCreated, create, invalid, grouped } = ret;
        let tips: string[] = [];
        if (grouped === actualCreated && create.length !== grouped) {
          tips = [
            `${actualCreated} commission entries posted.`,
            '',
            grouped !== create.length
              ? `${create.length} commissions entries merged into ${actualCreated}.`
              : '',
            `Only one commission entry allowed per policy.`,
          ];
        } else {
          tips.push(
            `${actualCreated} statement ${actualCreated > 1 ? 'entries' : 'entry'} created`
          );
        }
        // if not equal, show the tips in a yellow background
        showSnackbar(
          <Alert
            severity={
              grouped !== actualCreated || invalid?.length > 0
                ? 'warning'
                : 'success'
            }
          >
            Synced statementID: {ret.statementId}
            <br />
            {tips.map((tip, index) => (
              // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              <div key={index}>{tip ? tip : <br />}</div>
            ))}
            {invalid?.length > 0 && <br />}
            {invalid?.length > 0
              ? `${invalid.length} statement ${invalid.length > 1 ? 'reports' : 'report'} invalid`
              : null}
          </Alert>
        );
      }
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } catch (error: any) {
      showSnackbar(error.message || error, 'error');
    } finally {
      setLoadingConfig({
        loading: false,
        message: '',
      });
    }
  };

  const deleteData = async (row) => {
    try {
      setLoadingConfig({
        loading: true,
        message: 'Deleting...',
      });

      await documentsDelete.mutateAsync({
        id: row.id,
        str_id: row.str_id,
        type: row.type,
        deleteRecords,
      });

      queryClient.invalidateQueries();
      setDeleteRecords(false);
      setLoadingConfig({
        loading: false,
        message: '',
      });
      showSnackbar(`Deleted ${row.filename}`, 'success');
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } catch (error: any) {
      setLoadingConfig({
        loading: false,
        message: '',
      });
      showSnackbar(error.message || error, 'error');
    }
  };

  /**
   * Extraxt data from file, if file is image or pdf, show an select option to choose which method to extract data (1. Google Document AI, 2. ExtractTable)
   * @param {object} row documents table row
   */
  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (isActiveRowLoading || !activeRow || showExtract || open) {
      return;
    }

    if (activeRow) {
      setProcessLoading(activeRow.id);
    }
    const extractData = async (_row) => {
      const filePreviewType = activeRow?.override_file_path
        ? 'override'
        : 'original';

      try {
        const file = await downloadFile({
          endpoint_str_id: activeRow?.str_id,
          file_preview_type: filePreviewType,
          endpoint: 'documents',
        });

        if (!file) {
          throw new Error('Failed to download file');
        }
        setRowData(() => {
          return {
            ...activeRow,
            extractions: activeRowData,
          };
        });
        setUploadedFile(file);
        setProcessLoading('');
        if (PDF_HTML_IMG_TYPES.includes(file.type)) {
          setShowExtract(true);
        } else {
          setOpen(true);
        }
        activeRowRef.current = false;
        setActiveRow(undefined);
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } catch (error: any) {
        activeRowRef.current = false;
        setActiveRow(undefined);
        setProcessLoading('');
        showSnackbar(error?.message || error, 'error');
      }
    };
    extractData(activeRowData);
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Oct2024
     * MISSED REFs:  'open' and 'showExtract'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    activeRowData,
    isActiveRowLoading,
    activeRow,
    processLoading,
    downloadFile,
    setUploadedFile,
    showSnackbar,
  ]);

  if (isFetchedAccountSettings && viewSettings?.show_page === false) {
    return (
      // TODO: Remove navigate after figuring out how to handle this in router
      <Navigate to="/settings" />
    );
  }

  if (viewSettings?.page_label) {
    dataDesc.label = viewSettings?.page_label;
  }

  const onConfirmMethod = (method) => {
    setRowData((prev) => ({ ...prev, method }));
    setShowExtract(false);
    setOpen(true);
  };

  const onSetShowEdit = (isShow, action = 'cancel') => {
    setShowEdit(isShow);
    if (action === 'save') {
      queryClient.invalidateQueries();
    }
  };

  const actions = [
    ...dataDesc.actions,
    {
      type: 'icon',
      label: 'Sync',
      icon: <SyncedEntity isSynced={true} disabled={true} />,
      enabled: (row) => !!row.sync_id && row.sync_worker,
      onClick: async (_row, e) => {
        e?.stopPropagation();
        e?.preventDefault();
      },
    },
  ];

  return (
    <>
      <EnhancedDataView
        dataSpec={dataDesc}
        hideAdd
        enableMultiSelect={true}
        enableEdit={false}
        actionsEnabled={() => true}
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        actions={actions as any}
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        extraActions={extraActions as any}
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        bulkActions={[BulkSync] as any}
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        filters={filtersData as any}
        enableBulkDelete={false}
        setSelectedData={setSelectedData}
      />
      {open && (
        <UpdateProcessData
          open={open}
          rowData={rowData}
          setRowData={setRowData}
          handleClose={(arg) => {
            queryClient.invalidateQueries();
            setOpen(arg);
          }}
        />
      )}
      {showUploadModal && (
        <UploadOverrideFile
          open={showUploadModal}
          setOpen={setShowUploadModal}
          uploadedRow={rowData}
        />
      )}
      {showExtract && (
        <ExtractMethod
          showExtract={showExtract}
          onClose={() => setShowExtract(false)}
          onConfirm={onConfirmMethod}
          uploadedRow={rowData}
        />
      )}
      {showPreview && (
        <FileDialogPreview
          showPreview={showPreview}
          setShowPreview={setShowPreview}
          fileId={previewId}
        />
      )}
      {showConfirm && (
        <BasicDialog
          open={showConfirm}
          title="Delete document"
          bodyComponent={
            <>
              <Alert severity="warning">
                Are you sure you want to delete {rowData.filename}?
              </Alert>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={deleteRecords}
                    onChange={(e) => setDeleteRecords(e.target.checked)}
                  />
                }
                label={`Also delete imported records (${rowData.statement_data?.total_count})`}
                sx={{ ml: 1, mt: 1 }}
              />
            </>
          }
          onClose={(isOk) => {
            if (isOk) {
              deleteData(rowData);
            }
            setShowConfirm(false);
          }}
          sxContent={{ pb: 1 }}
        />
      )}

      {showEdit && (
        <ActionDialog
          open={showEdit}
          setOpen={onSetShowEdit}
          rowData={rowData}
        />
      )}
      <BasicDialog
        open={sync.show}
        title="Sync data"
        bodyComponent={
          <Alert severity="warning">
            Are you sure you want to sync {sync.count} statement entries to
            BenefitPoint?
          </Alert>
        }
        onClose={(isOk) => {
          if (isOk) {
            syncToBenefit({ documentId: sync.documentId });
          }

          setSync({ ...sync, show: false });
        }}
        positiveLabel="Sync"
      />
    </>
  );
};

export default DocumentsView;
