import { useQuery, type UseQueryOptions } from '@tanstack/react-query';
import axios from 'axios';

import { getEnvVariable } from '@/env';

const Prefix = `${getEnvVariable('API')}/api`;

export const API_PATHS = {
  docProfiles: `${Prefix}/document_profiles`,
};

export const useFetchDocumentProfiles = <TData>(
  options: Partial<UseQueryOptions<TData>> = {}
) => {
  return useQuery({
    ...options,
    queryKey: [API_PATHS.docProfiles],
    queryFn: async () => {
      return axios.get(API_PATHS.docProfiles).then((res) => res.data);
    },
  });
};
