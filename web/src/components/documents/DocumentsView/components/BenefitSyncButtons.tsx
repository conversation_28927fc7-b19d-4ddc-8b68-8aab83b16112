import { PeopleAltOutlined, SyncOutlined } from '@mui/icons-material';
import { IconButton, Tooltip } from '@mui/material';
import { type FC, useMemo } from 'react';

interface BenefitSyncButtonsProps {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setSync: (data: any) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  syncMemberCount: (data: any) => void;
}

export const BenefitSyncButtons: FC<BenefitSyncButtonsProps> = ({
  data,
  setSync,
  syncMemberCount,
}) => {
  const title = useMemo(() => {
    if (data?.sync_id) {
      return `The document has been synced with statementId: ${data?.sync_id}, you can sync again to update`;
    }
    return `${data?.statement_data?.total_count} statement entries will be synced to BenefitPoint`;
  }, [data]);
  if (
    !data?.statement_data?.total_count ||
    data.statement_data.total_count <= 0
  ) {
    return null;
  }

  return (
    <>
      <Tooltip title={title} placement="top">
        <IconButton
          onClick={() =>
            setSync({
              documentId: data.str_id,
              show: true,
              count: data?.statement_data?.total_count || 0,
            })
          }
          size="small"
          sx={{
            marginLeft: '4px',
            opacity: 0.5,
            '&:hover': { opacity: 1 },
            color: '#2196f3',
          }}
        >
          <SyncOutlined color={data.sync_id ? 'disabled' : 'primary'} />
        </IconButton>
      </Tooltip>
      <Tooltip title={`Sync back member count to BenefitPoint`} placement="top">
        <IconButton
          onClick={() =>
            syncMemberCount({
              documentId: data.str_id,
            })
          }
          size="small"
          sx={{
            marginLeft: '4px',
            opacity: 0.5,
            '&:hover': { opacity: 1 },
            color: '#2196f3',
          }}
        >
          <PeopleAltOutlined color={data.sync_id ? 'disabled' : 'primary'} />
        </IconButton>
      </Tooltip>
    </>
  );
};
