import { render, screen } from '@testing-library/react';
import { DocumentStatuses } from 'common/globalTypes';
import type { Document } from 'common/documents/documents.types';

import { CommissionTotalCell } from './ComissionTotalCell';

describe('CommissionTotalCell', () => {
  const baseRowData = {
    status: DocumentStatuses.PROCESSED,
    bank_total_amount: 100,
    statement_amount: 100,
    statement_data: { total_commission: 100 },
  } as unknown as Document;

  it('renders green check and formatted amount when all amounts match', () => {
    render(<CommissionTotalCell rowData={baseRowData} />);
    expect(screen.getByText('✅ $100.00')).toBeInTheDocument();
  });

  it('renders green check and formatted amount when total commission and statement match, bank is NaN', () => {
    const rowData = {
      ...baseRowData,
      bank_total_amount: NaN,
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    expect(screen.getByText('✅ $100.00')).toBeInTheDocument();
  });

  it('renders green check and formatted amount when total commission and bank match, statement is NaN', () => {
    const rowData = {
      ...baseRowData,
      statement_amount: NaN,
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    expect(screen.getByText('✅ $100.00')).toBeInTheDocument();
  });

  it('renders nothing when all amounts are NaN', () => {
    const rowData = {
      status: DocumentStatuses.PROCESSED,
      bank_total_amount: NaN,
      statement_amount: NaN,
      statement_data: { total_commission: NaN },
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    // Should not render any text
    expect(screen.queryByText(/Commissions:/)).not.toBeInTheDocument();
    expect(screen.queryByText(/Statement amount:/)).not.toBeInTheDocument();
    expect(screen.queryByText(/Bank amount:/)).not.toBeInTheDocument();
    expect(screen.queryByText(/ℹ️/)).not.toBeInTheDocument();
    expect(screen.queryByText(/✅/)).not.toBeInTheDocument();
    expect(screen.queryByText(/❌/)).not.toBeInTheDocument();
  });

  it('renders cross icon and all available amounts when they do not match', () => {
    const rowData = {
      status: DocumentStatuses.PROCESSED,
      bank_total_amount: 100,
      statement_amount: 200,
      statement_data: { total_commission: 300 },
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    expect(screen.getByText('❌')).toBeInTheDocument();
    expect(screen.getByText('Commissions: $300.00')).toBeInTheDocument();
    expect(screen.getByText('Statement amount: $200.00')).toBeInTheDocument();
    expect(screen.getByText('Bank amount: $100.00')).toBeInTheDocument();
  });

  it('renders cross icon and both available amounts when statement and bank are present but total commission is missing', () => {
    const rowData = {
      status: DocumentStatuses.PROCESSED,
      bank_total_amount: 100,
      statement_amount: 200,
      statement_data: { total_commission: NaN },
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    expect(screen.getByText('❌')).toBeInTheDocument();
    expect(screen.queryByText(/Commissions:/)).not.toBeInTheDocument();
    expect(screen.getByText('Statement amount: $200.00')).toBeInTheDocument();
    expect(screen.getByText('Bank amount: $100.00')).toBeInTheDocument();
  });

  it('renders info icon and only available amount when only one amount is present', () => {
    const rowData = {
      status: DocumentStatuses.PROCESSED,
      bank_total_amount: NaN,
      statement_amount: NaN,
      statement_data: { total_commission: 123.45 },
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    expect(screen.getByText('ℹ️')).toBeInTheDocument();
    expect(screen.getByText('Commissions: $123.45')).toBeInTheDocument();
  });

  it('renders info icon and only available amount when only statement amount is present', () => {
    const rowData = {
      status: DocumentStatuses.PROCESSED,
      bank_total_amount: NaN,
      statement_amount: 55.55,
      statement_data: { total_commission: NaN },
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    expect(screen.getByText('ℹ️')).toBeInTheDocument();
    expect(screen.getByText('Statement amount: $55.55')).toBeInTheDocument();
  });

  it('renders info icon and only available amount when only bank amount is present', () => {
    const rowData = {
      status: DocumentStatuses.PROCESSED,
      bank_total_amount: 77.77,
      statement_amount: NaN,
      statement_data: { total_commission: NaN },
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    expect(screen.getByText('ℹ️')).toBeInTheDocument();
    expect(screen.getByText('Bank amount: $77.77')).toBeInTheDocument();
  });

  it('renders statement amount for non-PROCESSED status', () => {
    const rowData = {
      status: DocumentStatuses.NEW,
      bank_total_amount: 100,
      statement_amount: 200,
      statement_data: { total_commission: 300 },
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    expect(screen.getByText('Statement amount: $200.00')).toBeInTheDocument();
  });

  it('renders nothing for non-PROCESSED status and NaN statement amount', () => {
    const rowData = {
      status: DocumentStatuses.NEW,
      bank_total_amount: 100,
      statement_amount: NaN,
      statement_data: { total_commission: 300 },
    } as unknown as Document;
    render(<CommissionTotalCell rowData={rowData} />);
    // Should not render any text
    expect(screen.queryByText(/Statement amount/)).not.toBeInTheDocument();
  });
});
