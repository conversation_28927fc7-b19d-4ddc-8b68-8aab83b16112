import { Box } from '@mui/material';
import type { Document } from 'common/dist/documents/documents.types';
import { commissionTotalCellTextFormatter } from 'common/field-config/document/text-formatter';

export const CommissionTotalCell = ({ rowData }: { rowData: Document }) => {
  const { text, icon } = commissionTotalCellTextFormatter(rowData);

  if (!text) return null;

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      {icon && <Box sx={{ fontSize: 12 }}>{icon}</Box>}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {text.split('\n').map((item, idx) => (
          // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          <span key={idx}>{item}</span>
        ))}
      </Box>
    </Box>
  );
};
