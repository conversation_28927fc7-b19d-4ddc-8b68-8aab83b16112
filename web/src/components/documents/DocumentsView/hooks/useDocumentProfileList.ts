import type { UseQueryOptions } from '@tanstack/react-query';
import { useMemo } from 'react';

import { useFetchDocumentProfiles } from '../apis/useFetchDocumentProfiles';

export const useDocumentProfileList = (
  options: Partial<UseQueryOptions> = {}
) => {
  // biome-ignore lint/suspicious/noExplicitAny: We need to type out our API responses properly, but for now suppressing this error --INITIAL_FIX--
  const { data: documentProfiles } = useFetchDocumentProfiles<any>(options);

  const documentProfileOptions = useMemo<
    { id: string; label: string }[]
  >(() => {
    return documentProfiles?.data?.map((profile) => {
      const profileName = profile.name ? profile.name : '(Blank)';
      return {
        id: profile.str_id,
        label: profileName,
      };
    });
  }, [documentProfiles]);

  return { documentProfileOptions };
};
