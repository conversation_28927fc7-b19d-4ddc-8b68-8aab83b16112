import {
  ComputerOutlined,
  DeleteOutline,
  DownloadOutlined,
  Edit,
  EmailOutlined,
  LaunchOutlined,
  PlayArrow,
  WebhookOutlined,
} from '@mui/icons-material';
import {
  Box,
  Chip,
  FormControl,
  IconButton,
  TextField,
  Tooltip,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import {
  DocumentTypeLabels,
  type DocumentTypes,
} from 'common/constants/documents';
import {
  DocumentStatuses,
  DocumentStatusesLabels,
  ProcessMethod,
  SystemRoles,
  UploadSource,
  UploadSourceLabels,
} from 'common/globalTypes';
import {
  numberOrDefault,
  removeLeadingTrailingChar,
  tryDecodeURIComponent,
} from 'common/helpers';
import { formatCurrency } from 'common/helpers/formatCurrency';
import { saveAs } from 'file-saver';
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useMemo,
} from 'react';
import { Link } from 'react-router-dom';
import {
  getDocumentFieldConfig,
  mergeConfigs,
} from 'common/field-config/document';

import { useCompanies } from '@/api/companies';
import BasicDatePicker from '@/components/molecules/BasicDatePicker';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { CHAR_WIDTH } from '@/components/molecules/EnhancedTable/constants';
import { LoadingContext } from '@/contexts/LoadingContext';
import { UIStateContext } from '@/contexts/UIStateProvider';
import usePreviewParams from '@/contexts/usePreviewParams';
import useSnackbar from '@/contexts/useSnackbar';
import { useUserInfo } from '@/hooks/useUserInfo';
import API from '@/services/API';
import Formatter from '@/services/Formatter';
import useAccountStore from '@/store/accountStore';
import { DocumentPreviewKeys, FieldTypes } from '@/types';
import { Status, Types } from '../constants';
import { useDocumentProfileList } from './useDocumentProfileList';
import { useFilters } from './useFilters';
import { ValidationButtons } from '../components/ValidationButtons';
import { CommissionTotalCell } from '../components/ComissionTotalCell';
import { BenefitSyncButtons } from '../components/BenefitSyncButtons';
import { useSyncedFieldsNew } from '@/contexts/useSyncedFields';
import { SyncEndAdornment } from '@/common/SyncEndAdornment';
import { FileNameRowCell } from '../components/FileNameRowCell';

// Type definitions
interface SyncedData {
  sync_worker: string;
  sync_id: string;
  config: Record<string, string[]>;
}

interface DocumentField {
  id: string;
  type: FieldTypes;
  label?: string;
  readOnly?: boolean | ((data: SyncedData | undefined) => boolean);
  endAdornment?: (
    data: SyncedData | undefined,
    field: DocumentField,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    setNewData: Dispatch<SetStateAction<any>>
  ) => React.ReactNode;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  render?: (field: any, newData: any, setter: any) => React.ReactNode;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  formatter?: (v: any, row: any) => React.ReactNode;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  getWidth?: (args: any) => number;
}

interface UseDocumentViewDescProps {
  setProcessLoading: (v: number | '') => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setActiveRow: (row: any) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setRowData: (row: any) => void;
  setShowUploadModal: (show: boolean) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setSync: (data: any) => void;
  setShowEdit: (show: boolean) => void;
  setShowConfirm: (show: boolean) => void;
  activeRowRef: React.MutableRefObject<boolean>;
}

interface RenderConfig {
  fields: {
    [key: string]: {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      render?: (field: any, newData: any, setter: any) => React.ReactNode;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      formatter?: (v: any, row: any) => React.ReactNode;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      getWidth?: (args: any) => number;
    };
  };
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  bulkEditFields: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  actions: any[];
}

const UPLOAD_SOURCE_CONFIG = {
  [UploadSource.EMAIL]: {
    icon: <EmailOutlined sx={{ fontSize: 15, color: '#aaaaaa' }} />,
    text: UploadSourceLabels[UploadSource.EMAIL],
  },
  [UploadSource.API]: {
    icon: <WebhookOutlined sx={{ fontSize: 15, color: '#aaaaaa' }} />,
    text: UploadSourceLabels[UploadSource.API],
  },
  [UploadSource.WEB]: {
    icon: <ComputerOutlined sx={{ fontSize: 15, color: '#aaaaaa' }} />,
    text: UploadSourceLabels[UploadSource.WEB],
  },
};

export const useDocumentViewDesc = ({
  setProcessLoading,
  setActiveRow,
  setRowData,
  setShowUploadModal,
  setSync,
  setShowEdit,
  setShowConfirm,
  activeRowRef,
}: UseDocumentViewDescProps) => {
  const { filters } = useFilters();
  const { data: companiesData } = useCompanies();
  const { workerSyncedFields, isSyncedField } = useSyncedFieldsNew();

  const exportPoster = API.getMutation('statement_data/export', 'POST', {
    rawData: true,
  });

  const { setShowPreview, setPreviewPath } = usePreviewParams();

  const {
    role: [role],
  } = useContext(UIStateContext);

  const { documentProfileOptions } = useDocumentProfileList({
    enabled: role === SystemRoles.ADMIN,
  });

  const { data: { fintaryAdmin } = {} } = useUserInfo();
  const { showSnackbar } = useSnackbar();

  const { setLoadingConfig } = useContext(LoadingContext);

  const documentPutter = API.getMutation('documents', 'PUT');
  const queryClient = useQueryClient();

  const { selectedAccount } = useAccountStore();
  const mode = selectedAccount?.accountMode;

  // Hard coded for now, will be dynamic later
  const enableAccountId = ['W4kSrayZvmh26pGfYVrGE', 'fFF86XAy2Cu97xxra8lgA'];
  const isRiskTag =
    enableAccountId.includes(selectedAccount?.accountId || '') ||
    selectedAccount?.accountName?.toLowerCase().includes('risktag');

  const syncService = API.getMutation(
    'data_processing/sync/benefit-point/member-count',
    'POST'
  );

  const syncMemberCount = useCallback(
    async (data) => {
      try {
        setLoadingConfig({
          loading: true,
          message: 'Syncing member count...',
        });
        const res = await syncService.mutateAsync(data);
        const { stats } = res;

        if (!stats) {
          showSnackbar('Member count synced successfully', 'success');
          return;
        }

        let message = `Member count sync completed. Synced: ${stats.updated + stats.ignored}, Failed: ${stats.failed}`;

        if (stats.failed > 0) {
          const failedTasks = stats.failedTasks
            .map((task) => `Policy (${task.policy_id}): ${task.error}`)
            .join(', ');
          message += `. Failed tasks: ${failedTasks}`;
        }

        showSnackbar(message, stats.failed > 0 ? 'warning' : 'success');
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } catch (error: any) {
        showSnackbar(error.message || error, 'error');
      } finally {
        setLoadingConfig({
          loading: false,
          message: '',
        });
      }
    },
    [syncService, setLoadingConfig, showSnackbar]
  );

  const filePathFormatter = useCallback(
    (filename, row) => {
      if (!filename) {
        return '--';
      }

      const isAdminRole = role === SystemRoles.ADMIN;
      const hasOverrideFile = !!row.override_filename;

      const showSourceIcon = row.upload_source && isAdminRole;
      const showOverrideButton = !hasOverrideFile && isAdminRole;

      const handleAddOverrideClick = () => {
        setRowData(row);
        setShowUploadModal(true);
      };

      const handleFileNameClick = () => {
        setPreviewPath(row.str_id, DocumentPreviewKeys.ORIGINAL);
        setShowPreview(true);
      };

      const handleOverrideFileNameClick = () => {
        setPreviewPath(row.str_id, DocumentPreviewKeys.OVERRIDE);
        setShowPreview(true);
      };

      const fileNameOverride = {
        value: row.override_filename,
        handleClick: handleOverrideFileNameClick,
      };

      const addOverrideButton = { handleClick: handleAddOverrideClick };
      const uploadSource = {
        icon: UPLOAD_SOURCE_CONFIG[row.upload_source]?.icon || null,
        title: UPLOAD_SOURCE_CONFIG[row.upload_source]?.text || '',
      };

      return (
        <FileNameRowCell
          fileName={{ value: filename, handleClick: handleFileNameClick }}
          fileNameOverride={hasOverrideFile ? fileNameOverride : undefined}
          showSourceIcon={showSourceIcon ? uploadSource : undefined}
          showAddOverrideButton={
            showOverrideButton ? addOverrideButton : undefined
          }
        />
      );
    },
    [setPreviewPath, setShowPreview, setRowData, setShowUploadModal, role]
  );

  const exportData = useCallback(
    async (data) => {
      const params = {
        document_id: data.str_id,
        company_str_id: data.company_str_id,
      };
      try {
        setLoadingConfig({
          loading: true,
          message: 'Exporting...',
        });
        const res = await exportPoster.mutateAsync(params);
        setLoadingConfig({
          loading: false,
          message: '',
        });
        const blob = res.data;
        saveAs(
          blob,
          `${removeLeadingTrailingChar(tryDecodeURIComponent(data.filename), '"')}`
        );
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } catch (error: any) {
        setLoadingConfig({
          loading: false,
          message: '',
        });
        showSnackbar(error.message || error, 'error');
      }
    },
    [exportPoster, setLoadingConfig, showSnackbar]
  );

  const handleRunButtonClick = useCallback(
    async (row) => {
      setActiveRow(row);
      activeRowRef.current = true;
      // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      let interval;
      // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      let timeout;

      try {
        await new Promise<void>((resolve, reject) => {
          timeout = setTimeout(() => {
            reject(new Error('Operation timed out after 60 seconds'));
          }, 60000);

          interval = setInterval(() => {
            if (!activeRowRef.current) {
              clearInterval(interval);
              clearTimeout(timeout);
              resolve();
            }
          }, 100);
        });
      } catch (error) {
        console.error('Error during row processing:', error);
        clearInterval(interval);
        clearTimeout(timeout);
        activeRowRef.current = false;
      }
    },
    [setActiveRow, activeRowRef]
  );

  const markProcessed = useCallback(
    async (row) => {
      const { company_str_id, file_type, id } = row;
      setProcessLoading(row.id);
      const res = await documentPutter.mutateAsync({
        company_str_id,
        file_type,
        id,
        status: DocumentStatuses.PROCESSED,
      });
      setProcessLoading('');
      if (res.error) {
        showSnackbar(res.error, 'error');
      } else {
        queryClient.invalidateQueries();
      }
    },
    [documentPutter, queryClient, setProcessLoading, showSnackbar]
  );

  const dataDesc = useMemo(() => {
    const config = getDocumentFieldConfig({
      // @ts-expect-error
      mode,
      filters,
    });

    const renderConfig: RenderConfig = {
      fields: {
        filename: {
          formatter: filePathFormatter,
        },
        type: {
          render: (_field, newData, setter) => {
            return (
              <EnhancedSelect
                label="Type"
                sx={{ width: '100%' }}
                value={Types.find((item) => item.id === newData.type)}
                options={Types}
                onChange={(value) => {
                  setter((prev) => ({
                    ...prev,
                    type: value.id,
                  }));
                }}
              />
            );
          },
          getWidth: ({ allRows }) => {
            let maxWidth = 0;
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            allRows.forEach((row) => {
              if (row.type) {
                const width =
                  (DocumentTypeLabels[row.type]?.length || 0) * CHAR_WIDTH; // 20 for padding
                maxWidth = Math.max(maxWidth, width);
              }
            });

            return maxWidth + 80;
          },
          formatter: (v: DocumentTypes, row) => {
            const typeLabel = DocumentTypeLabels[v];

            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <span>{typeLabel}</span>
                <ValidationButtons
                  row={row}
                  fieldName="type"
                  setRowData={setRowData}
                  setShowEdit={setShowEdit}
                  queryClient={queryClient}
                />
              </Box>
            );
          },
        },
        companies: {
          getWidth: ({ allRows }) => {
            let maxWidth = 0;
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            allRows.forEach((row) => {
              const width =
                (row.companies?.company_name?.length || 0) * CHAR_WIDTH;
              maxWidth = Math.max(maxWidth, width);
            });

            return maxWidth + 80;
          },
          formatter: (val, row) => {
            const baseElement = Formatter.getLinkChipFormatter(
              'company_name',
              'str_id',
              '/companies?id='
            )(val, row);

            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {baseElement}
                <ValidationButtons
                  row={row}
                  fieldName="company_str_id"
                  setRowData={setRowData}
                  setShowEdit={setShowEdit}
                  queryClient={queryClient}
                />
              </Box>
            );
          },
        },

        statement_data: {
          getWidth: ({ allRows }) => {
            let maxWidth = 0;
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            allRows.forEach((row) => {
              const val = row.statement_data;
              const groupedCountInfoStrList: string[] = [];
              const groupedCommissionInfoStrList: string[] = [];
              if (val.groupedCountInfo) {
                // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                Object.entries(val.groupedCountInfo).forEach(([key, value]) => {
                  if (key !== 'NO_STATUS') {
                    groupedCountInfoStrList.push(`${key}: ${value}`);
                  }
                });
              }
              if (val.groupedCommissionInfo) {
                // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                Object.entries(val.groupedCommissionInfo).forEach(
                  ([key, value]) => {
                    if (key !== 'NO_STATUS') {
                      groupedCommissionInfoStrList.push(
                        `${key}: ${
                          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                          formatCurrency(value as any)
                        }`
                      );
                    }
                  }
                );
              }

              let width = Math.max(
                groupedCountInfoStrList.join(', ').length * CHAR_WIDTH,
                groupedCommissionInfoStrList.join(', ').length * CHAR_WIDTH
              );

              if (val?.total_commission) {
                width +=
                  formatCurrency(val.total_commission).length * CHAR_WIDTH +
                  30 +
                  30; // 30 for launch icon, 30 for padding
              }
              maxWidth = Math.max(maxWidth, width);
            });

            return maxWidth;
          },
          formatter: (val, row) => {
            const groupedCountInfoStrList: string[] = [];
            const groupedCommissionInfoStrList: string[] = [];
            if (val.groupedCountInfo) {
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.entries(val.groupedCountInfo).forEach(([key, value]) => {
                if (key !== 'NO_STATUS') {
                  groupedCountInfoStrList.push(`${key}: ${value}`);
                }
              });
            }
            if (val.groupedCommissionInfo) {
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.entries(val.groupedCommissionInfo).forEach(
                ([key, value]) => {
                  if (key !== 'NO_STATUS') {
                    groupedCommissionInfoStrList.push(
                      `${key}: ${formatCurrency(value as number)}`
                    );
                  }
                }
              );
            }
            return (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  marginLeft: '4px',
                  height: isRiskTag ? 120 : 'auto',
                  justifyContent: 'space-between',
                }}
              >
                <Box sx={{ flex: 1 }}>
                  {!!val.total_count && (
                    <Box
                      sx={{
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <span>{+val.total_count}</span>
                      {groupedCountInfoStrList.length > 0 && (
                        <span
                          style={{
                            color: '#555',
                            fontSize: 13,
                            marginLeft: 4,
                          }}
                        >
                          ({groupedCountInfoStrList.toString()})
                        </span>
                      )}
                    </Box>
                  )}
                  {!!val.total_commission && (
                    <Box
                      sx={{
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <span>{formatCurrency(val.total_commission)}</span>
                      {groupedCommissionInfoStrList.length > 0 && (
                        <span
                          style={{
                            color: '#555',
                            fontSize: 13,
                            marginLeft: 4,
                          }}
                        >
                          ({groupedCommissionInfoStrList.join(', ')})
                        </span>
                      )}
                    </Box>
                  )}
                  {!val.total_commission && !val.total_count && '0'}
                </Box>
                <Box
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    flexDirection: 'column',
                  }}
                >
                  {(val.total_commission > 0 || val.total_count > 0) && (
                    <>
                      <IconButton
                        component={Link}
                        to={`/${row.type === 'statement' ? 'commissions' : 'policies'}?document_id=${row.str_id}`}
                        target="_blank"
                        sx={{
                          opacity: 0.5,
                          '&:hover': { opacity: 1 },
                          color: '#2196f3',
                        }}
                      >
                        <LaunchOutlined />
                      </IconButton>
                      {isRiskTag && (
                        <>
                          <BenefitSyncButtons
                            data={row}
                            setSync={setSync}
                            syncMemberCount={syncMemberCount}
                          />
                          <IconButton
                            onClick={() => exportData(row)}
                            size="small"
                            sx={{
                              opacity: 0.5,
                              '&:hover': { opacity: 1 },
                              color: '#2196f3',
                            }}
                          >
                            <DownloadOutlined />
                          </IconButton>
                        </>
                      )}
                    </>
                  )}
                </Box>
              </Box>
            );
          },
        },
        statement_amount: {
          render: (field, newData, settter) => {
            return (
              <FormControl fullWidth sx={{ minWidth: 100, mb: 1.5 }}>
                <TextField
                  label={field.label}
                  variant="outlined"
                  value={newData.statement_amount}
                  onChange={(e) => {
                    settter((prev) => ({
                      ...prev,
                      statement_amount: e.target.value,
                    }));
                  }}
                />
              </FormControl>
            );
          },
          getWidth: ({ allRows }) => {
            let maxWidth = 0;
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            allRows.forEach((row) => {
              if (row.statement_data?.total_commission) {
                const text = `Commissions: ${formatCurrency(
                  row.statement_data?.total_commission
                )}`;
                const width = text.length * CHAR_WIDTH;
                maxWidth = Math.max(maxWidth, width);
              }
            });
            return maxWidth + 80;
          },
          formatter: (_v, row) => {
            return (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CommissionTotalCell rowData={row} />
                <ValidationButtons
                  row={row}
                  fieldName="statement_amount"
                  setRowData={setRowData}
                  setShowEdit={setShowEdit}
                  queryClient={queryClient}
                />
              </Box>
            );
          },
        },
        bank_total_amount: {
          render: (field, newData, settter) => {
            return (
              <FormControl fullWidth sx={{ minWidth: 100, mb: 1.5 }}>
                <TextField
                  label={field.label}
                  variant="outlined"
                  value={newData.bank_total_amount}
                  onChange={(e) => {
                    settter((prev) => ({
                      ...prev,
                      bank_total_amount: e.target.value,
                    }));
                  }}
                />
              </FormControl>
            );
          },
          getWidth: ({ allRows }) => {
            let maxWidth = 0;
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            allRows.forEach((row) => {
              if (row.statement_data?.total_commission) {
                const text = `Commissions: ${formatCurrency(
                  row.statement_data?.total_commission
                )}`;
                const width = text.length * CHAR_WIDTH;
                maxWidth = Math.max(maxWidth, width);
              }
            });
            return maxWidth;
          },
          formatter: (_v, row) => {
            const bankTotalAmount = numberOrDefault(
              row.bank_total_amount,
              null,
              { toFixed: 2 }
            );

            return (
              <Box
                sx={{
                  whiteSpace: 'nowrap',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                {bankTotalAmount && (
                  <span>{`Bank total: ${formatCurrency(bankTotalAmount)}`}</span>
                )}
              </Box>
            );
          },
        },
        created_by: {
          formatter: config.fields.created_by.textFormatter,
        },
        deposit_date: {
          formatter: config.fields.deposit_date.textFormatter,
        },
        statement_month: {
          render: (field, newData, setter) => {
            return (
              <BasicDatePicker
                label={field.label}
                openTo="month"
                views={['year', 'month']}
                value={newData.statement_month}
                setValue={(v: string) => {
                  setter((prev) => ({
                    ...prev,
                    statement_month: v,
                  }));
                }}
              />
            );
          },
          formatter: config.fields.statement_month.textFormatter,
        },
        status: {
          render: (_field, newData, setter) => {
            return (
              <EnhancedSelect
                label="Status"
                sx={{ width: '100%' }}
                value={Status.find((item) => item.id === newData.status)}
                options={Status}
                onChange={(value) => {
                  setter((prev) => ({
                    ...prev,
                    status: value.id,
                  }));
                }}
              />
            );
          },
          getWidth: ({ estimatedWidth }) => {
            return estimatedWidth + 36;
          },
          formatter: (val, row) => {
            // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            let statusChip;

            if (
              val === DocumentStatuses.PROCESSED &&
              row.process_method === ProcessMethod.AUTO
            ) {
              statusChip = Formatter.statusChip('Processed✨', {
                mapping: {
                  'Processed✨': 'green',
                },
              });
            } else if (val === DocumentStatuses.PROCESSING_FAILED) {
              statusChip = Formatter.statusChip('Processing', {
                mapping: {
                  Processing: 'yellow',
                },
              });
            } else {
              statusChip = Formatter.statusChip(DocumentStatusesLabels[val], {
                mapping: {
                  'Pending upload': 'yellow',
                  'Upload failed': 'red',
                  New: 'yellow',
                  Processing: 'blue',
                  'Pending review': 'deepBlue',
                  Processed: 'green',
                  Canceled: 'lightgrey',
                },
              });
            }

            if (row.processing_notes) {
              return (
                <Tooltip
                  title={
                    <Box>
                      <Box sx={{ whiteSpace: 'pre-wrap', maxWidth: 400 }}>
                        {row.processing_notes}
                      </Box>
                    </Box>
                  }
                  arrow
                  placement="top"
                  enterDelay={500}
                >
                  <Box sx={{ display: 'inline-block' }}>{statusChip}</Box>
                </Tooltip>
              );
            }

            return statusChip;
          },
        },
        method: {
          getWidth: ({ estimatedWidth }) => {
            return estimatedWidth + 40; // 40 for padding
          },
          formatter: (val: string, row) =>
            val ? (
              <Chip
                label={val}
                component={Link}
                to={`/documents/profiles?id=${row.profile_str_id}`}
              />
            ) : null,
        },
        notes: {
          getWidth: () => 200,
        },
        sync_id: {
          getWidth: () => 80,
        },
        imported_at: {
          getWidth: () => {
            return 'MM/DD/YYYY hh:mmA'.length * CHAR_WIDTH;
          },
          formatter: config.fields.imported_at.textFormatter,
        },
        created_at: {
          formatter: config.fields.created_at.textFormatter,
        },
      },
      bulkEditFields: [
        ...(fintaryAdmin
          ? [
              {
                id: 'profile_str_id',
                bulkEdit: !!fintaryAdmin,
                label: 'Document profiles 🔒',
                type: FieldTypes.CUSTOM,
                render: (_field, newData, setter) => {
                  return (
                    <EnhancedSelect
                      enableSearch
                      label="Document profiles 🔒"
                      sx={{ width: '100%' }}
                      listContainerSx={{
                        width: 800,
                      }}
                      value={documentProfileOptions.find(
                        (profile) => profile.id === newData.profile_str_id
                      )}
                      options={documentProfileOptions}
                      onChange={(value) => {
                        setter((prev) => ({
                          ...prev,
                          profile_str_id: value.id,
                        }));
                      }}
                    />
                  );
                },
              },
            ]
          : []),
        {
          id: 'check_date',
          label: 'Check date',
          bulkEdit: true,
          type: FieldTypes.DATE,
        },
        {
          id: 'company_str_id',
          label: 'Company',
          bulkEdit: true,
          type: FieldTypes.CUSTOM,
          render: (field, newData, settter) => {
            return (
              <EnhancedSelect
                enableSearch
                label={field.label}
                sx={{ width: '100%' }}
                value={companiesData?.data.find(
                  (company) => company.str_id === newData.company_str_id
                )}
                labelKey="company_name"
                valueKey="str_id"
                options={companiesData?.data || []}
                onChange={(value) => {
                  settter((prev) => ({
                    ...prev,
                    company_str_id: value.str_id,
                  }));
                }}
              />
            );
          },
        },
      ],

      actions:
        role === SystemRoles.ADMIN
          ? [
              {
                id: 'process',
                label: 'Process',
                type: 'iconButton',
                icon: <PlayArrow />,
                onClick: (row) => handleRunButtonClick(row),
              },
              {
                id: 'edit',
                label: 'Edit',
                type: 'iconButton',
                icon: <Edit />,
                onClick: (row) => {
                  setRowData(row);
                  setShowEdit(true);
                },
              },
              {
                id: 'delete',
                label: 'Delete',
                type: 'iconButton',
                icon: <DeleteOutline />,
                onClick: (row) => {
                  setRowData(row);
                  setShowConfirm(true);
                },
              },
              {
                id: 'mark_processed',
                label: 'Mark as processed',
                enabled: (row) => row.status === DocumentStatuses.NEW,
                onClick: async (row) => await markProcessed(row),
              },
            ]
          : [
              {
                id: 'edit',
                label: 'Edit',
                type: 'iconButton',
                icon: <Edit />,
                onClick: (row) => {
                  setRowData(row);
                  setShowEdit(true);
                },
              },
              {
                id: 'delete',
                label: 'Delete',
                type: 'iconButton',
                icon: <DeleteOutline />,
                enabled: (row) => row.status !== DocumentStatuses.PROCESSED,
                onClick: (row) => {
                  setRowData(row);
                  setShowConfirm(true);
                },
              },
            ],
    };

    const mergedConfig = mergeConfigs(config, renderConfig);

    // Add synced fields configuration
    for (const key in mergedConfig.fields) {
      const field = mergedConfig.fields[key] as DocumentField;

      field.readOnly =
        field.readOnly ||
        ((data: SyncedData | undefined) => {
          if (!data?.sync_worker) {
            return false;
          }

          const syncedFields =
            workerSyncedFields?.[data?.sync_worker]?.documents;
          if ((syncedFields || []).includes(key)) {
            return isSyncedField(data, syncedFields, key, data.config);
          }
          return false;
        });

      field.endAdornment = (
        data: SyncedData | undefined,
        field: DocumentField,
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        setNewData: Dispatch<SetStateAction<any>>
      ) => {
        if (!data?.sync_worker) {
          return null;
        }
        return (
          <SyncEndAdornment
            syncedFields={workerSyncedFields?.[data?.sync_worker]?.documents}
            syncId={data?.sync_id}
            fieldId={field?.id}
            data={data}
            fieldType={field.type}
            onChange={(newOverrideFields) => {
              setNewData({
                ...data,
                config: {
                  ...(data?.config || {}),
                  overrideFields: newOverrideFields,
                },
              });
            }}
          />
        );
      };
    }

    return mergedConfig;
  }, [
    filters,
    filePathFormatter,
    fintaryAdmin,
    role,
    isRiskTag,
    setSync,
    exportData,
    documentProfileOptions,
    companiesData?.data,
    handleRunButtonClick,
    setRowData,
    setShowEdit,
    setShowConfirm,
    markProcessed,
    mode,
    queryClient,
    syncMemberCount,
    workerSyncedFields,
    isSyncedField,
  ]);

  return { dataDesc };
};
