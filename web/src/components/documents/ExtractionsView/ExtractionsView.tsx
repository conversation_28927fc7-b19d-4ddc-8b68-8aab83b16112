import { DeleteOutline } from '@mui/icons-material';
import { <PERSON><PERSON>, <PERSON>, IconButton } from '@mui/material';
import PropTypes from 'prop-types';
import { useContext, useState } from 'react';
import { Link } from 'react-router-dom';

import { BasicDialog } from '@/common';
// biome-ignore lint/suspicious/noShadowRestrictedNames: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import DataView from '@/components/DataView';
import { LoadingContext } from '@/contexts/LoadingContext';
import API from '@/services/API';
import {
  headerSx,
  cellSx,
} from '@/components/HoverActionButtonContainer/styles';
import { HoverActionButtonContainer } from '@/components/HoverActionButtonContainer';
import useSnackbar from '@/contexts/useSnackbar';

const ExtractionsView = () => {
  const [refresh, setRefresh] = useState(0);
  const [showDelConfirm, setShowDelConfirm] = useState(false);
  const [currentRow, setCurrentRow] = useState(null);
  const { setLoadingConfig } = useContext(LoadingContext);

  const { showSnackbar } = useSnackbar();
  const extractionsDelter = API.getMutation('extractions', 'DELETE');

  const dataDesc = {
    label: 'Extractions',
    table: 'extractions',
    fields: [
      { id: 'str_id', label: 'Id', copyable: true },
      {
        id: 'created_at',
        numeric: false,
        label: 'Created at',
        formatter: (s) => new Date(s).toLocaleString(),
      },
      {
        id: 'method',
        numeric: false,
        label: 'Method',
      },
      {
        id: 'documents',
        numeric: false,
        label: 'Document name',
        tableFormatter: (_s, row) => {
          return (
            <Chip
              label={row.documents?.filename}
              clickable
              component={Link}
              to={`/documents?id=${row.documents?.str_id}`}
              target="_blank"
            />
          );
        },
        formatter: (_s, row) => {
          return row.documents?.filename;
        },
      },
      {
        id: 'result',
        label: 'Result',
        formatter: (s, row) => {
          return `${s} (${row?.status})`;
        },
      },
      // {
      //   id: 'output',
      //   numeric: false,
      //   label: 'Extracted Data Key',
      //   formatter: (s) => {
      //     // show max 600 chars
      //     return (
      //       <CustomWidthTooltip
      //         title={JSON.stringify(
      //           JSON.parse(s)?.Tables || JSON.parse(s)?.table || ''
      //         )}
      //       >
      //         <span>{Object.keys(JSON.parse(s)).toString()}</span>
      //       </CustomWidthTooltip>
      //     );
      //   },
      // },
      {
        id: 'id',
        numeric: false,
        label: 'Actions',
        sx: cellSx,
        headerSx,
        formatter: (_id, row) => {
          return (
            <HoverActionButtonContainer>
              <IconButton
                sx={{ ml: 1 }}
                onClick={async () => {
                  setCurrentRow(row);
                  setShowDelConfirm(true);
                }}
              >
                <DeleteOutline />
              </IconButton>
            </HoverActionButtonContainer>
          );
        },
      },
    ],
  };

  const DelConfirmComp = ({ row }) => {
    return (
      <BasicDialog
        title="Extraction Data Delete"
        open={showDelConfirm}
        onClose={(val) => {
          if (val) {
            setLoadingConfig({
              loading: true,
              message: 'Deleting...',
            });
            extractionsDelter
              .mutateAsync({ ids: [row.id] })
              .then(() => {
                showSnackbar('Extraction data deleted successfully', 'success');
                setRefresh(refresh + 1);
                setLoadingConfig({
                  loading: false,
                  message: '',
                  delayToClose: 1000,
                });
                setCurrentRow(null);
              })
              .catch((err) => {
                showSnackbar(err.message || err, 'error');
                setLoadingConfig({
                  loading: false,
                  message: '',
                });
              });
          } else {
            setShowDelConfirm(false);
            setCurrentRow(null);
          }
        }}
        bodyComponent={
          <Alert severity="warning">
            Are you sure you want to delete the extraction: {row.str_id}?
          </Alert>
        }
      />
    );
  };

  return (
    <>
      <DataView dataDesc={dataDesc} viewOnly refresh={refresh} hideExport />
      {showDelConfirm && currentRow && <DelConfirmComp row={currentRow} />}
    </>
  );
};

ExtractionsView.propTypes = {
  user: PropTypes.object,
};

export default ExtractionsView;
