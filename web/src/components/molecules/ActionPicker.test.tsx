import actionPicker from './ActionPicker';

describe('getSelectedOption', () => {
  const { getSelectedOption } = actionPicker;

  it('Given optionSelected is undefined, should return undefined', () => {
    const result = getSelectedOption({
      optionSelected: undefined,
      options: [],
    });
    expect(result).toBeUndefined();
  });

  it('Given optionSelected is null, should return undefined', () => {
    const result = getSelectedOption({
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      optionSelected: null as unknown as any,
      options: [],
    });
    expect(result).toBeUndefined();
  });

  it('Given options is array of objects and optionSelected matches id, should return matching object', () => {
    const options = [
      { id: 'foo', name: 'Foo' },
      { id: 'bar', name: 'Bar' },
    ];
    const result = getSelectedOption({ optionSelected: 'bar', options });
    expect(result).toEqual({ id: 'bar', name: 'Bar' });
  });

  it('Given options is array of objects and optionSelected does not match any id, should return optionSelected as string', () => {
    const options = [
      { id: 'foo', name: 'Foo' },
      { id: 'bar', name: 'Bar' },
    ];
    const result = getSelectedOption({ optionSelected: 'baz', options });
    expect(result).toBe('baz');
  });

  it('Given options is array of primitives and optionSelected matches, should return optionSelected', () => {
    const options = ['foo', 'bar', 'baz'];
    const result = getSelectedOption({ optionSelected: 'bar', options });
    expect(result).toBe('bar');
  });

  it('Given options is array of primitives and optionSelected does not match, should return optionSelected', () => {
    const options = ['foo', 'bar', 'baz'];
    const result = getSelectedOption({ optionSelected: 'qux', options });
    expect(result).toBe('qux');
  });

  it('Given options is empty array, should return undefined if optionSelected is falsy', () => {
    const result = getSelectedOption({ optionSelected: '', options: [] });
    expect(result).toBeUndefined();
  });

  it('Given options is array of objects with numeric ids and optionSelected is a number, should return matching object', () => {
    const options = [
      { id: 1, name: 'One' },
      { id: 2, name: 'Two' },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    ] as any;
    const result = getSelectedOption({ optionSelected: 2, options });
    expect(result).toEqual({ id: 2, name: 'Two' });
  });

  it('Given options is array of objects with numeric ids and optionSelected does not match, should return optionSelected', () => {
    const options = [
      { id: 1, name: 'One' },
      { id: 2, name: 'Two' },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    ] as any;
    const result = getSelectedOption({ optionSelected: 3, options });
    expect(result).toBe(3);
  });
});
