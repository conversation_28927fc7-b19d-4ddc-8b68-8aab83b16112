import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TablePagination from '@mui/material/TablePagination';
import { useState } from 'react';
import { ArrowDownward, ArrowUpward } from '@mui/icons-material';

export interface BasicTableProps {
  headers: string[];
  rows: (string | number)[][];
  format?: 'array' | 'object';
  formatters?: unknown;
  nowrap?: boolean;
  sorted?: boolean;
  pageSize?: number;
}

type SortDirection = 'asc' | 'desc' | null;
type SortConfig = {
  key: string | number;
  direction: SortDirection;
};

const extractNumericValue = (value: unknown): number | null => {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    const num = value.replace(/[^0-9.-]/g, '');
    if (num && !Number.isNaN(parseFloat(num))) {
      return parseFloat(num);
    }
  }
  return null;
};

const compareValues = (a: unknown, b: unknown, direction: SortDirection) => {
  const aNum = extractNumericValue(a);
  const bNum = extractNumericValue(b);

  if (aNum !== null && bNum !== null) {
    return direction === 'asc' ? aNum - bNum : bNum - aNum;
  }
  if (aNum !== null && bNum === null) {
    return direction === 'asc' ? -1 : 1;
  }
  if (aNum === null && bNum !== null) {
    return direction === 'asc' ? 1 : -1;
  }
  const aStr = String(a).toLowerCase();
  const bStr = String(b).toLowerCase();

  if (aStr < bStr) {
    return direction === 'asc' ? -1 : 1;
  }
  if (aStr > bStr) {
    return direction === 'asc' ? 1 : -1;
  }
  return 0;
};

/**
 * @deprecated This component is should only be used internally. Please use the `TableView` component instead.
 */
const BasicTable = ({
  headers,
  rows,
  format = 'array',
  formatters,
  nowrap = false,
  sorted = false,
  pageSize = 20,
}: BasicTableProps) => {
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
  const [page, setPage] = useState(0);

  const requestSort = (key: string | number) => {
    if (!sorted) return;

    let direction: SortDirection = 'asc';
    if (sortConfig && sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        direction = 'desc';
      } else if (sortConfig.direction === 'desc') {
        direction = null;
      }
    }
    setSortConfig(direction ? { key, direction } : null);
  };

  const sortedRows = [...(rows || [])];
  if (sorted && sortConfig && sortConfig.direction) {
    sortedRows.sort((a: unknown, b: unknown) => {
      const aValue =
        format === 'array'
          ? (a as unknown[])[sortConfig.key as number]
          : (a as Record<string, unknown>)[sortConfig.key as string];
      const bValue =
        format === 'array'
          ? (b as unknown[])[sortConfig.key as number]
          : (b as Record<string, unknown>)[sortConfig.key as string];

      return compareValues(aValue, bValue, sortConfig.direction);
    });
  }

  const paginatedRows = sortedRows.slice(
    page * pageSize,
    page * pageSize + pageSize
  );

  const getSortIcon = (headerIndex: number) => {
    if (
      !sorted ||
      !sortConfig ||
      sortConfig.key !==
        (format === 'array' ? headerIndex : headers[headerIndex])
    ) {
      return null;
    }
    return sortConfig.direction === 'asc' ? (
      <ArrowUpward fontSize="small" sx={{ ml: 0.5, verticalAlign: 'middle' }} />
    ) : (
      <ArrowDownward
        fontSize="small"
        sx={{ ml: 0.5, verticalAlign: 'middle' }}
      />
    );
  };

  const getHeaderCellStyle = () => {
    const baseStyle = {
      userSelect: 'none',
    };

    if (sorted) {
      return {
        ...baseStyle,
        cursor: 'pointer',
        '&:hover': {
          backgroundColor: 'action.hover',
        },
      };
    }
    return baseStyle;
  };

  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  return (
    <TableContainer
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-evenly',
      }}
    >
      <Table>
        <TableHead>
          <TableRow>
            {Array.isArray(headers) &&
              headers.map((header, index) => (
                <TableCell
                  key={header}
                  onClick={() =>
                    requestSort(format === 'array' ? index : header)
                  }
                  sx={getHeaderCellStyle()}
                >
                  {header}
                  {getSortIcon(index)}
                </TableCell>
              ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {format === 'array'
            ? paginatedRows?.map((row, i: number) => (
                <TableRow
                  key={`row-${
                    // biome-ignore lint/suspicious/noArrayIndexKey: Row data only contains values
                    i
                  }`}
                >
                  {Array.isArray(row) &&
                    row?.map((cell, j) =>
                      j === 0 ? (
                        <TableCell
                          component="th"
                          scope="row"
                          // biome-ignore lint/suspicious/noArrayIndexKey: Row data only contains values
                          key={j}
                          style={{ whiteSpace: nowrap ? 'nowrap' : 'normal' }}
                        >
                          {typeof formatters?.[j] === 'function'
                            ? formatters[j](cell)
                            : cell}
                        </TableCell>
                      ) : (
                        <TableCell
                          // biome-ignore lint/suspicious/noArrayIndexKey: Row data only contains values
                          key={j}
                          style={{ whiteSpace: nowrap ? 'nowrap' : 'normal' }}
                        >
                          {typeof formatters?.[j] === 'function'
                            ? formatters[j](cell)
                            : cell}
                        </TableCell>
                      )
                    )}
                </TableRow>
              ))
            : paginatedRows?.map((row, i) => (
                // biome-ignore lint/suspicious/noArrayIndexKey: Row data only contains values
                <TableRow key={`row-${i}`}>
                  {headers?.map((col, j) =>
                    j === 0 ? (
                      <TableCell
                        component="th"
                        scope="row"
                        key={col}
                        style={{ whiteSpace: nowrap ? 'nowrap' : 'normal' }}
                      >
                        {String(row[col])}
                      </TableCell>
                    ) : (
                      <TableCell key={col}>{String(row[col])}</TableCell>
                    )
                  )}
                </TableRow>
              ))}
        </TableBody>
      </Table>
      <TablePagination
        component="div"
        count={sortedRows.length}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={pageSize}
        rowsPerPageOptions={[pageSize]}
      />
    </TableContainer>
  );
};

export default BasicTable;
