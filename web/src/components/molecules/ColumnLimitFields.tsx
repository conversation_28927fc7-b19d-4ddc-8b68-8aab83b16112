import { Box, Chip, Tooltip, Menu, MenuItem } from '@mui/material';
import { HelpOutline, FilterList } from '@mui/icons-material';
import {
  filterFieldsByDataSource,
  type WidgetDataSources,
} from 'common/dto/widgets';

import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { useState } from 'react';

interface ColumnLimitFieldsProps {
  selectedDataSource: WidgetDataSources | '';
  selectedColumnValue: string;
  onColumnChange: (value: string) => void;
  columnLimit: number | null;
  onColumnLimitChange: (value: number | null) => void;
}

const COLUMN_LIMIT_OPTIONS = [
  { id: 5, label: '5' },
  { id: 10, label: '10' },
  { id: 20, label: '20' },
  { id: null, label: 'Unspecified' },
];

const ColumnLimitFields = ({
  selectedDataSource,
  selectedColumnValue,
  onColumnChange,
  columnLimit,
  onColumnLimitChange,
}: ColumnLimitFieldsProps) => {
  const [limitAnchorEl, setLimitAnchorEl] = useState<null | HTMLElement>(null);

  const handleLimitMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setLimitAnchorEl(event.currentTarget);
  };

  const handleLimitMenuClose = () => {
    setLimitAnchorEl(null);
  };

  const handleLimitChange = (id: number | null) => {
    onColumnLimitChange(id);
    handleLimitMenuClose();
  };

  const limitValue =
    COLUMN_LIMIT_OPTIONS.find((opt) => opt.id === columnLimit)?.label ||
    COLUMN_LIMIT_OPTIONS[0].label;

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <EnhancedSelect
        label="Stacked column"
        enableSearch
        sx={{ marginRight: 1 }}
        options={
          filterFieldsByDataSource[selectedDataSource]?.groupByFields.map(
            (field) => ({
              id: field.name,
              label: field.displayName,
            })
          ) || []
        }
        value={{
          id: selectedColumnValue,
          label:
            filterFieldsByDataSource[selectedDataSource]?.groupByFields.find(
              (f) => f.name === selectedColumnValue
            )?.displayName || '',
        }}
        onChange={(value) => {
          onColumnChange(value.id);
        }}
      />
      <Tooltip title="Limit">
        <Chip
          sx={{ marginRight: 1 }}
          data-name="column-limit-chip"
          label={limitValue}
          avatar={<FilterList />}
          onClick={handleLimitMenuOpen}
        />
      </Tooltip>
      <Menu
        anchorEl={limitAnchorEl}
        open={Boolean(limitAnchorEl)}
        onClose={handleLimitMenuClose}
      >
        {COLUMN_LIMIT_OPTIONS.map((option) => (
          <MenuItem
            key={option.id ?? 'unspecified'}
            selected={columnLimit === option.id}
            onClick={() => handleLimitChange(option.id)}
          >
            {option.label}
          </MenuItem>
        ))}
      </Menu>
      <Tooltip title="For stacked bar chart" arrow placement="top">
        <HelpOutline fontSize="small" />
      </Tooltip>
    </Box>
  );
};

export default ColumnLimitFields;
