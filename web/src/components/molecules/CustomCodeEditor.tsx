import { javascript } from '@codemirror/lang-javascript';
import type { Extension } from '@codemirror/state';
import { Box, FormControl, Typography } from '@mui/material';
import CodeMirror from '@uiw/react-codemirror';

import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';

interface CustomCodeEditorProps {
  code: string;
  language: 'javascript' | 'python';
  onCodeChange: (code: string) => void;
  onLanguageChange: (language: 'javascript' | 'python') => void;
  getLanguageExtension: (language: 'javascript' | 'python') => Extension;
}

const CustomCodeEditor = ({
  code,
  language,
  onCodeChange,
  onLanguageChange,
  getLanguageExtension,
}: CustomCodeEditorProps) => {
  return (
    <FormControl fullWidth margin="normal">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          gap: 2,
          border: '1px solid #e0e0e0',
          borderRadius: 1,
          padding: 2,
        }}
      >
        <Typography variant="h6">Custom code</Typography>
        <CodeMirror
          title="Custom code"
          height="350px"
          value={code}
          width="100%"
          extensions={[
            language
              ? getLanguageExtension(language)
              : javascript({ jsx: true, typescript: true }),
          ]}
          onChange={onCodeChange}
        />
        <EnhancedSelect
          label="Language"
          sx={{ minWidth: 120 }}
          options={[
            { id: 'javascript', label: 'Javascript' },
            { id: 'python', label: 'Python' },
          ]}
          value={{
            id: language,
            label: language === 'javascript' ? 'Javascript' : 'Python',
          }}
          onChange={(value) => {
            onLanguageChange(value.id as 'javascript' | 'python');
          }}
        />
      </Box>
    </FormControl>
  );
};

export default CustomCodeEditor;
