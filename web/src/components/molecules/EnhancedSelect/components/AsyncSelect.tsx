import { useQuery } from '@tanstack/react-query';
import debounce from 'lodash-es/debounce';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { axios } from '@/api/interceptor';
import type { SelectProps } from '../types';
import { ObjectSelect } from './ObjectSelect';
import { PAGINATION } from 'common/constants/pagination';
import { getEnvVariable } from '@/env';

const Prefix = `${getEnvVariable('API')}/api`;

export const AsyncSelect = <T, K extends boolean = false>({
  url,
  requestConfig = {},
  options: initialOptions,
  extractValue,
  customLabel,
  onDataFetch,
  fieldId,
  fetchOnOpen = false,
  ...rest
}: SelectProps<T, K>) => {
  const [search, setSearch] = useState('');
  const [pagination, setPagination] = useState({
    page: PAGINATION.FIRST_PAGE,
    limit: PAGINATION.DATA_FILTERS_PAGINATED.DEFAULT_ITEMS_PER_PAGE,
  });
  const [options, setOptions] = useState<T[]>([]);
  const [endOfList, setEndOfList] = useState(false);
  const refCustomLabel = useRef(customLabel);
  const refSelectedOptions = useRef<typeof options>([]);
  const [shouldFetch, setShouldFetch] = useState(!fetchOnOpen);

  const [query, setQuery] = useState('');

  const { data, isLoading } = useQuery({
    queryKey: [url, pagination, requestConfig, query],
    queryFn: () => {
      if (!requestConfig || !url) return null;
      const method = requestConfig.method || 'get';
      return axios[method](`${Prefix}${url}`, {
        data: requestConfig.data,
        params: {
          ...pagination,
          q: query.trim(),
          ...(requestConfig?.params || {}),
        },
      }).then((res) => {
        if (res.data?.data?.length < pagination.limit) {
          setEndOfList(true);
        }
        return res.data;
      });
    },
    enabled: shouldFetch && Boolean(url && requestConfig),
  });

  useEffect(() => {
    if (data?.data) {
      setOptions((prev) => [...prev, ...data.data]);
      onDataFetch?.(fieldId || 'unknown', data);
    }
  }, [data, data?.data, onDataFetch, fieldId]);

  const onReachBottom = () => {
    if (!isLoading && !endOfList) {
      setPagination((prev) => ({ ...prev, page: prev.page + 1 }));
    }
  };

  const _options = useMemo(() => {
    let _options = [
      ...initialOptions,
      ...options,
      ...refSelectedOptions.current,
    ];

    const map = new Map();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    _options.forEach((item) => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      map.set((item as any)[rest.valueKey || 'id'], item);
    });
    _options = Array.from(map.values());

    const _customLabel = refCustomLabel.current;
    if (_customLabel) {
      _options = _options.map((item) => {
        return {
          ...item,
          label: _customLabel(item),
        };
      });
    }
    return _options;
  }, [options, initialOptions, rest.valueKey]);

  let value = rest.value;
  if (extractValue) {
    value = extractValue(_options);
  }

  refSelectedOptions.current = Array.isArray(value)
    ? value
    : ([value].filter(Boolean) as T[]);

  const onSearch = useCallback((search: string) => {
    setSearch(search);
    const doSearch = debounce(() => {
      setQuery(search);
      setPagination({ page: 0, limit: 50 });
      setOptions([]);
      setEndOfList(false);
    }, 800);
    doSearch();
  }, []);

  const handleOpen = useCallback(() => {
    if (fetchOnOpen && !shouldFetch) {
      setShouldFetch(true);
    }
  }, [fetchOnOpen, shouldFetch]);

  if (!url || !requestConfig) return null;
  return (
    <ObjectSelect<T, K>
      options={_options}
      sortLabel={false}
      onReachBottom={onReachBottom}
      isLoading={isLoading}
      {...rest}
      searchKeyword={search}
      onSearch={onSearch}
      onOpen={handleOpen}
      value={value}
    />
  );
};
