import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

import { getEnvVariable } from '@/env';

const Prefix = `${getEnvVariable('API')}/api`;

export const API_PATHS = {
  dynamicSelects: `${Prefix}/dynamic_selects`,
};

export type DynamicSelectParams = {
  table: string;
  queryParamName: string;
  queryParamValue: Array<string | number>;
}[];

export type DynamicSelectResponse = {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  [table: string]: any;
}[];

export const useFetchDynamicSelects = (data?: DynamicSelectParams) => {
  return useQuery<DynamicSelectResponse>({
    queryKey: [API_PATHS.dynamicSelects, data],
    queryFn: async () => {
      return axios.post(API_PATHS.dynamicSelects, data).then((res) => res.data);
    },
    enabled: !!data?.length,
  });
};
