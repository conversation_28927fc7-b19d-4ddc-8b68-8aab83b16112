import { Box, Chip, Tooltip } from '@mui/material';
import { isValidElement, memo } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import CopyButton from '@/components/atoms/CopyButton';
import type { Column } from '../types';
import { SelectDbValsShowMore } from './SelectDbValueShowMore';

export const Cell = memo(
  ({
    column,
    dynamicData,
    rowData,
  }: {
    column: Column;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    rowData: any; // TODO: refactor type
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dynamicData?: any;
  }) => {
    const navigate = useNavigate();

    const {
      copyable,
      onClick,
      id,
      tableFormatter,
      type,
      itemOnClick,
      linker,
      delimiter,
      id2,
      options,
      dynamicFormatter,
    } = column;

    const headerKey = column.keyAs ?? column.id;

    const formatter = (x, dynamicSelects) => {
      if (dynamicFormatter instanceof Function) {
        return dynamicFormatter(x, dynamicSelects, rowData, column);
      }

      if (tableFormatter instanceof Function) {
        return tableFormatter(x, rowData, dynamicSelects, column);
      }
      if (column.formatter instanceof Function) {
        if (dynamicSelects) {
          // TODO: Allow formatters to be applied when values are null
          if (!x) return null;
          if (isValidElement(x)) return x;
          if (isValidElement(column.formatter(x, dynamicSelects)))
            return column.formatter(x, dynamicSelects);
          return (
            <Chip
              label={column.formatter(x, dynamicSelects)}
              sx={{ m: 0.25 }}
              clickable={typeof linker === 'function'}
              component={typeof linker === 'function' ? Link : 'div'}
              to={typeof linker === 'function' ? linker(x) : undefined}
            />
          );
        }
        if (x instanceof Object) {
          const res = column.formatter(x, rowData, navigate);
          if (res === null) return null;
          return typeof res === 'object' && !isValidElement(res) ? '---' : res;
        }
        return column.formatter(x, rowData, navigate);
      }
      if (x instanceof Object && !isValidElement(x)) {
        return JSON.stringify(x);
      }
      if (type === 'boolean') {
        return x ? 'Yes' : 'No';
      }
      return x;
    };
    let getter = column.getter;
    if (!getter) {
      getter =
        type === 'select' && typeof options?.[0] === 'object'
          ? (x) => options.find((option) => option.id === x[headerKey])?.label
          : (x) => x[headerKey];
    }

    return (
      <Box
        sx={{
          whiteSpace: copyable ? 'nowrap' : 'normal',
          cursor: onClick ? 'pointer' : 'default',
          width: '100%',
        }}
        onClick={(e) => {
          e.stopPropagation();
          if (onClick instanceof Function) {
            onClick(rowData);
          }
        }}
      >
        <Box
          sx={{
            display: copyable ? 'inline-flex' : 'inherit',
          }}
        >
          {rowData[headerKey] !== undefined ? (
            Array.isArray(rowData[column.id]) && !tableFormatter ? (
              id === 'notes' ? (
                <Tooltip
                  title={
                    <Box>
                      {rowData[id].map((item) => (
                        <Box key={item} sx={{ my: 0.5 }}>
                          {item}
                        </Box>
                      ))}
                    </Box>
                  }
                >
                  <span style={{ whiteSpace: 'nowrap' }}>
                    {rowData[id].length
                      ? `⚠️ ${rowData[id].length} conflicts`
                      : null}
                  </span>
                </Tooltip>
              ) : (
                <SelectDbValsShowMore
                  data={rowData[id].map((item, i) => {
                    const formattedVal = formatter?.(item, dynamicData);
                    if (formattedVal === null) return null;
                    return (
                      <span
                        key={
                          typeof item === 'object' ? JSON.stringify(item) : item
                        }
                      >
                        {/* If array, but not a dynamic select (already chipped), chipify */}
                        {type === 'dynamic-select' ? (
                          formattedVal
                        ) : isValidElement(formattedVal) ? (
                          formattedVal
                        ) : (
                          <Chip
                            key={item}
                            label={formattedVal}
                            sx={{
                              m: 0.25,
                              cursor:
                                itemOnClick || linker ? 'pointer' : 'default',
                              maxWidth: 500,
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              if (typeof itemOnClick === 'function')
                                itemOnClick(item);
                            }}
                            clickable={typeof linker === 'function'}
                            component={
                              typeof linker === 'function' ? Link : 'div'
                            }
                            to={
                              typeof linker === 'function'
                                ? linker(item)
                                : undefined
                            }
                          />
                        )}
                        {delimiter && i < rowData[id].length - 1 && delimiter}
                      </span>
                    );
                  })}
                />
              )
            ) : (
              formatter?.(
                id2 && !(rowData[headerKey] instanceof Object)
                  ? (rowData[headerKey]?.[id2]?.[headerKey] ??
                      rowData[headerKey]?.[id2]?.[id2])
                  : getter?.(rowData),
                dynamicData
              )
            )
          ) : (
            ''
          )}
        </Box>
        {copyable && (
          <CopyButton
            handleOnClick={(e) => {
              e.stopPropagation();
              const valUnformatted =
                id2 && !(rowData[headerKey] instanceof Object)
                  ? (rowData[headerKey]?.[id2]?.[headerKey] ??
                    rowData[headerKey]?.[id2]?.[id2])
                  : getter?.(rowData);
              const valFormatted = formatter?.(valUnformatted, rowData);
              if (typeof valFormatted === 'string') {
                return { valueToCopy: valFormatted };
              } else {
                return { valueToCopy: valUnformatted };
              }
            }}
            sx={{
              opacity: 0,
              '&:hover': { opacity: 1 },
            }}
          />
        )}
      </Box>
    );
  }
);
