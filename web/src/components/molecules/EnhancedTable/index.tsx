import { ContentCopy, Edit } from '@mui/icons-material';
import {
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  IconButton,
  Radio,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  Tooltip,
  useMediaQuery,
} from '@mui/material';
import { Lock } from '@mui/icons-material';
import { AccountIds, ResponseAction } from 'common/constants';
import { getFilenameFromPath } from 'common/helpers';
import copy from 'copy-to-clipboard';
import PropTypes from 'prop-types';
import {
  isValidElement,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { DataStates } from 'common/types/common';

import { HoverActionButtonContainer } from '@/components/HoverActionButtonContainer';
import { cellSx, rowSx } from '@/components/HoverActionButtonContainer/styles';
import EnhancedTableToolbar from '@/components/molecules/EnhancedTableToolbar';
import ExpandableData from '@/components/molecules/ExpandableData';
import MoreMenu from '@/components/molecules/MoreMenu';
import TableRowCard from '@/components/molecules/TableRowCard';
import { UIStateContext } from '@/contexts/UIStateProvider';
import { useDragToScroll } from '@/hooks/useDragToScroll';
import API from '@/services/API';
import Formatter from '@/services/Formatter';
import {
  filterHeadersForTable,
  requiresFintaryAdmin,
} from '@/services/helpers';
import { useAccountStore, useRoleStore } from '@/store';
import { CheckboxWidth, ColNumberWidth, Colors } from './constants';
import { NewTable, type NewTableProps } from './NewTable';
import { FieldTypes } from '@/types';

const APP_BAR_HEIGHT = 64;

function descendingComparator(a, b, orderBy) {
  // TODO: Remove this hack
  if (
    window.location.pathname.startsWith('/reports/summary') &&
    a?.totals?.agent_commissions &&
    b?.totals?.agent_commissions
  ) {
    const _a = Object.values(a.totals.agent_commissions ?? {})?.[0] ?? 0;
    const _b = Object.values(b.totals.agent_commissions ?? {})?.[0] ?? 0;
    if (_b < _a) {
      return -1;
    }
    if (_b > _a) {
      return 1;
    }
    return 0;
  }

  if (
    window.location.pathname.startsWith('/documents') &&
    orderBy === 'file_path'
  ) {
    return getFilenameFromPath(b[orderBy]).localeCompare(
      getFilenameFromPath(a[orderBy])
    );
  }

  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order, orderBy) {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

const SelectDbValsShowMore = ({ data }) => {
  const [showMore, setShowMore] = useState(false);
  return (
    <Box>
      {data.slice(0, 6).map((item, i) => (
        // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        <Box key={i}>{item}</Box>
      ))}
      {/** biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX-- */}
      {showMore && data.slice(6).map((item, i) => <Box key={i}>{item}</Box>)}
      {data.length > 6 && (
        <Button onClick={() => setShowMore(!showMore)} sx={{ color: '#555' }}>
          {showMore ? 'Show less' : `Show ${data.length - 6} more`}
        </Button>
      )}
    </Box>
  );
};

const EnhancedTableHead = ({
  onSelectAllClick,
  order,
  orderBy,
  numSelected,
  rowCount,
  onRequestSort,
  headCells,
  onDelete,
  actionsCol,
  options = {},
  onRenderColumnsWidth,
  stickyPositions,
}) => {
  const createSortHandler = (property) => (event) => {
    onRequestSort(event, property);
  };
  const { selectedAccount } = useAccountStore();
  const isTG = selectedAccount?.accountId === AccountIds.TRANSGLOBAL;

  const onNode = useCallback(
    (node: HTMLTableHeaderCellElement) => {
      if (node)
        onRenderColumnsWidth({
          columnId: node.getAttribute('data-column-id'),
          value: {
            width: node.getBoundingClientRect().width,
            offset: node.offsetLeft,
          },
        });
    },
    [onRenderColumnsWidth]
  );

  return (
    <TableHead>
      <TableRow>
        {onDelete && (
          <TableCell
            sx={{
              position: 'sticky',
              left: 0,
              zIndex: 10,
            }}
            padding="checkbox"
          >
            <Checkbox
              color="primary"
              indeterminate={numSelected > 0 && numSelected < rowCount}
              checked={rowCount > 0 && numSelected === rowCount}
              onChange={onSelectAllClick}
              inputProps={{
                'aria-label': 'Select all',
              }}
            />
          </TableCell>
        )}
        {/** biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX-- */}
        {(options as any)?.radio && <TableCell />}
        {isTG && (
          <TableCell
            sx={{
              position: onDelete ? 'sticky' : undefined,
              paddingLeft: onDelete ? 0 : 2,
              left: onDelete ? CheckboxWidth : undefined,
              zIndex: 10,
              textAlign: 'center',
              top: 0,
            }}
          >
            #
          </TableCell>
        )}

        {headCells.map((headCell) => {
          const stickyStyle =
            stickyPositions[headCell.id] >= 0
              ? {
                  position: 'sticky',
                  left: stickyPositions[headCell.id],
                  zIndex: 10,
                  backgroundColor: 'white',
                }
              : {};

          return (
            <TableCell
              ref={onNode}
              data-column-id={headCell.id}
              key={`${headCell.source ?? ''}${headCell.id}${headCell.id2 ?? ''}`}
              align={headCell.numeric ? 'right' : 'left'}
              padding="none"
              sx={{
                p: 1,
                whiteSpace: 'nowrap',
                display:
                  headCell.visible === false || headCell.hidden === true
                    ? 'none'
                    : 'table-cell',
                ...headCell.sx,
                ...headCell.headerSx,
                ...stickyStyle,
              }}
              sortDirection={orderBy === headCell.id ? order : false}
            >
              {headCell.disableSort ? (
                <Tooltip
                  title={
                    <Box>
                      <div>{headCell.description}</div>
                      <div>Sorting not supported for this field</div>
                    </Box>
                  }
                  placement="top"
                >
                  <span>
                    {headCell.label}
                    {headCell.infoIcon && ' ⓘ'}
                    {requiresFintaryAdmin(headCell.access) && ' 🔒'}
                  </span>
                </Tooltip>
              ) : (
                <TableSortLabel
                  active={orderBy === headCell.id}
                  direction={orderBy === headCell.id ? order : 'asc'}
                  key={headCell.id}
                  onClick={(evt) => {
                    if (headCell.disableSort) return;
                    createSortHandler(headCell.id)(evt);
                  }}
                >
                  <Tooltip title={headCell.description} placement="top">
                    <span>
                      {headCell.label}
                      {headCell.infoIcon && ' ⓘ'}
                      {requiresFintaryAdmin(headCell.access) && ' 🔒'}
                    </span>
                  </Tooltip>
                </TableSortLabel>
              )}
            </TableCell>
          );
        })}
        {actionsCol && <TableCell />}
      </TableRow>
    </TableHead>
  );
};

EnhancedTableHead.propTypes = {
  numSelected: PropTypes.number.isRequired,
  onRequestSort: PropTypes.func.isRequired,
  onSelectAllClick: PropTypes.func.isRequired,
  order: PropTypes.oneOf(['asc', 'desc']).isRequired,
  orderBy: PropTypes.string.isRequired,
  rowCount: PropTypes.number.isRequired,
};

export type EnhancedTableHeadProps = React.ComponentProps<typeof EnhancedTable>;

/**
 * @deprecated This component is deprecated. Please use the `TableView` component instead.
 */
export type EnhancedTableProps = {
  title?: string;
  outstandingFieldsInMobileView?: string[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  headers: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  rows: any[];
  rowKey?: string;
  dense?: boolean;
  readOnly?: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onEdit?: (row: any) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onBulkEdit?: (selected: any[], updateData: any) => void | Promise<void>;
  onBulkSync?: (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    selected: any[],
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    updateData?: any
  ) => void | Promise<void> | undefined;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onDelete?: (selected: any[]) => void | Promise<void>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onClick?: (row: any) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  actions?: any[];
  stickyHeader?: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  actionsEnabled?: (row: any) => boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  lockRow?: (row: any) => boolean;
  paginated?: boolean;
  controlledOrdering?: {
    order: 'asc' | 'desc';
    orderBy: string;
    setOrder: (order: 'asc' | 'desc') => void;
    setOrderBy: (orderBy: string) => void;
  };
  controlledPagination?: {
    count: number;
    rowsPerPage: number;
    page: number;
    onPageChange: (event: unknown, newPage: number) => void;
    onRowsPerPageChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  };
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setSelectedData?: (data: any[]) => void;
  options?: {
    radio?: boolean;
    hideSelectedCount?: boolean;
  };
  refresh?: number;
  refetch?: () => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  bulkActions?: any[];
  showTotals?: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  totals?: Record<string, any>;
  customHeaderActions?: boolean;
  nonSelectableOnMobile?: boolean;
  headingOffset?: number;
  stickyColumns?: string[];
  useNewTable?: boolean;
  enableBulkDelete?: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  bulkEditFields?: any[];
  dynamicSelectsConfig?: NewTableProps['dynamicSelectsConfig'];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  [key: string]: any;
};

/**
 * @deprecated This component is deprecated. Please use the `TableView` component instead.
 */
const EnhancedTable = ({
  title,
  outstandingFieldsInMobileView,
  headers: _headers,
  rows,
  rowKey,
  dense,
  readOnly = false,
  onEdit,
  // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onBulkEdit = (_selected: Array<any>, _updateData: any) => {},
  onBulkSync = undefined,
  onDelete, // Original behavior is only onDelete
  onClick, // Adding onClick while preserving default onDelete
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  actions = [] as any[],
  stickyHeader,
  actionsEnabled = () => false,
  lockRow = () => false,
  paginated = false,
  controlledOrdering,
  controlledPagination,
  // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setSelectedData = () => {}, // TODO: Should be controlled selection...hack for now
  options = {},
  refresh = 0,
  // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  refetch = () => {},
  bulkActions = [],
  showTotals = false,
  totals = {},
  customHeaderActions = false,
  nonSelectableOnMobile = false,
  headingOffset = 109,
  stickyColumns = [],
  useNewTable = false,
  enableBulkDelete = true,
  bulkEditFields = [],
  dynamicSelectsConfig,
}: EnhancedTableProps) => {
  const { selectedAccount } = useAccountStore();
  const isTG = selectedAccount?.accountId === AccountIds.TRANSGLOBAL;

  const { refContainer } = useDragToScroll();
  const navigate = useNavigate();
  const [dynamicSelects, setDynamicSelects] = useState({});
  const [selected, setSelected] = useState<string[]>([]);
  const [actionLoading, setActionLoading] = useState({});
  const getSetActionLoadingByRowId = (id) => (state) => {
    setActionLoading({
      ...actionLoading,
      [id]: state,
    });
    if (!state) setTimeout(refetch, 250);
  };
  const [sums, setSums] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(paginated ? 50 : 999999);
  const [order, setOrder] = useState('desc');
  const [orderBy, setOrderBy] = useState('created_at');
  const [innerOrderControl, setInnerOrderControl] = useState({
    order,
    orderBy,
    setOrder,
    setOrderBy,
  });

  const { userRole } = useRoleStore();
  const {
    role: [role],
  } = useContext(UIStateContext);

  const headers = useMemo(() => {
    return _headers?.flat() ?? [];
  }, [_headers]);

  const dynamicSelectsPoster = API.getMutation('dynamic_selects', 'POST', {
    gcTime: 1,
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    return () => {
      dynamicSelectsPoster.abort();
    };
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Jun/2024
     * MISSED REFs: dynamicSelectsPoster
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const _order = order || controlledOrdering?.order;
    if (_order) {
      setInnerOrderControl((pre) => {
        return {
          ...pre,
          order: _order,
        };
      });
    }
    const _orderBy = orderBy || controlledOrdering?.orderBy;
    if (_orderBy) {
      setInnerOrderControl((pre) => {
        return {
          ...pre,
          orderBy: _orderBy,
        };
      });
    }
  }, [order, orderBy, controlledOrdering]);

  // Reset list of selected items if the underlying dataset changes. Using length as proxy for now.
  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    setSelected([]);
    setSelectedData([]);
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  July/2023
     * MISSED REFs: setSelectedData
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rows?.length, refresh]); // If setSelectedData is added as a dependency, it will cause an infinite loop.

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const dynamicSelectVals = headers
      .filter((field) => field.table)
      .map((field) => ({
        table: field.table,
        queryParamTable: field.queryParamTable,
        queryParamNameTable: field.queryParamNameTable,
        field: field.id,
      }));

    const uniqueDynamicSelectVals = Array.from(
      new Set(dynamicSelectVals.map((item) => JSON.stringify(item)))
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    ).map((item: any) => JSON.parse(item));

    if (!useNewTable) getDynamicSelects(uniqueDynamicSelectVals);
    /*
     *
     *
     * https://linear.app/fintary/issue/PLT-2287
     *
     * MISSED REFs:  'getDynamicSelects' and 'headers'
     * WARN SINCE:  April/2025 ****
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [useNewTable]);

  const getDynamicSelects = async (dynamicSelectVals) => {
    try {
      const data = await dynamicSelectsPoster.mutateAsync(dynamicSelectVals);
      if (data.status === 401 && data.action === ResponseAction.LOG_OUT) {
        API.handleSessionExpired();
      }
      if (Array.isArray(data) && data.length > 0) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        data.forEach((field) => {
          setDynamicSelects((prev) => ({
            ...prev,
            ...field,
          }));
        });
      }
    } catch (e) {
      console.error(e);
    }
  };

  const headersFiltered = filterHeadersForTable(headers, userRole, role);

  // Logic for calculating totals for certain columns based on the headers
  const columns = headersFiltered.map((header) => header.id);
  useEffect(() => {
    if (totals) {
      const newSums = columns.map((col) => {
        return totals[col] ? totals[col] : undefined;
      });
      if (JSON.stringify(newSums) !== JSON.stringify(sums)) {
        setSums(newSums);
      }
    }
  }, [columns, sums, totals]);

  const handleRequestSort = (_event, property) => {
    const isAsc =
      innerOrderControl.orderBy === property &&
      innerOrderControl.order === 'asc';
    innerOrderControl.setOrder(isAsc ? 'desc' : 'asc');
    if (
      controlledOrdering?.setOrder &&
      typeof controlledOrdering?.setOrder === 'function'
    ) {
      controlledOrdering.setOrder(isAsc ? 'desc' : 'asc');
    }
    innerOrderControl.setOrderBy(property);
    if (
      controlledOrdering?.setOrderBy &&
      typeof controlledOrdering?.setOrderBy === 'function'
    ) {
      controlledOrdering.setOrderBy(property);
    }
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = rows.map((n) => n.id);
      setSelected(newSelected);
      setSelectedData(rows);
      return;
    }
    setSelected([]);
    setSelectedData([]);
  };

  const handleClick = (_event, name) => {
    const selectedIndex = selected.indexOf(name as never);
    let newSelected: string[] = [];
    if (options.radio) {
      newSelected = [name as never];
    } else if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, name);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }
    setSelected(newSelected);
    setSelectedData(
      rows.filter((row) => newSelected.includes(row.id as never))
    );
  };

  const handleChangePage = (_event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const isSelected = useCallback(
    (name) => {
      return selected.indexOf(name as never) !== -1;
    },
    [selected]
  );

  // Avoid a layout jump when reaching the last page with empty rows.
  const emptyRows =
    page > 0 ? Math.max(0, (1 + page) * rowsPerPage - rows.length) : 0;

  const rowsPerPageToUse = controlledPagination
    ? controlledPagination.rowsPerPage
    : rowsPerPage;

  const orderToUse = innerOrderControl.order; // ControlledOrdering ? controlledOrdering.order : order;
  // const orderByToUse = controlledOrdering
  //   ? controlledOrdering.orderBy
  //   : orderBy;
  const orderByToUse = innerOrderControl.orderBy;

  const rowsSorted = controlledPagination
    ? rows
    : rows.sort(getComparator(orderToUse, orderByToUse));

  const deleteResetSelected = async (selected) => {
    onDelete && (await onDelete(selected));
    setSelected([]);
    setSelectedData([]);
  };

  const isMobile = useMediaQuery('(max-width:600px)');

  const [columnsWidth, setColumnsWidth] = useState<{
    [key: string]: {
      width: number;
      offset: number;
    };
  }>({});

  const onRenderColumnsWidth = useCallback(({ columnId, value }) => {
    setColumnsWidth((prev) => ({
      ...prev,
      [columnId]: value,
    }));
  }, []);

  const _colNumberWidth = useMemo(() => {
    if (isTG) return rows?.length > 10 ? ColNumberWidth : 24;
    return 0;
  }, [isTG, rows?.length]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const stickyPositions = useMemo(() => {
    const result: { [column: string]: number } = {};

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    stickyColumns.forEach((column) => {
      let leftPosition = onDelete ? CheckboxWidth + _colNumberWidth : 0;
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.keys(result).forEach((key) => {
        const columnWidth = columnsWidth[key];
        leftPosition += columnWidth?.width || 0;
      });
      result[column] = leftPosition;
    });
    return result;
    /*
     *
     *
     * https://linear.app/fintary/issue/PLT-2287
     *
     * MISSED REFs:  onDelete
     * WARN SINCE:  April/2025 ****
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [columnsWidth, stickyColumns, _colNumberWidth]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const onScroll = () => {
      const scrollLeft = Math.ceil(refContainer.current?.scrollLeft || 0);
      let lastColumnCells: HTMLCollectionOf<Element>[] = [];
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      stickyColumns.forEach((column) => {
        const cells = document.getElementsByClassName(`cell-${column}`);
        let offsetLeft =
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          ((cells[0] as any)?.offsetLeft || 0) -
          CheckboxWidth -
          _colNumberWidth;

        for (let i = 0; i < headers.length; i++) {
          if (headers[i].id === column) {
            break;
          }
          if (stickyColumns.includes(headers[i].id)) {
            offsetLeft -= columnsWidth[headers[i].id]?.width || 0;
          }
        }

        for (let i = 0; i < cells.length; i++) {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (cells[i] as any).style.boxShadow = '';
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (cells[i] as any).style.backgroundColor =
            scrollLeft >= Math.floor(offsetLeft) ? 'white' : '';
        }

        if (scrollLeft >= Math.floor(offsetLeft)) {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          lastColumnCells = cells as any;
        }
      });

      for (let i = 0; i < lastColumnCells.length; i++) {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        (lastColumnCells[i] as any).style.boxShadow =
          '4px 0 8px rgba(0, 0, 0, 0.1)';
      }
    };

    const container = refContainer.current;
    container?.addEventListener('scroll', onScroll);
    onScroll();
    return () => {
      container?.removeEventListener('scroll', onScroll);
    };
    /*
     *
     *
     * https://linear.app/fintary/issue/PLT-2287
     *
     * MISSED REFs:  headers
     * WARN SINCE:  March/2025 ****
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [columnsWidth, stickyColumns, _colNumberWidth, refContainer]);

  const _columns = [
    ...headersFiltered,
    typeof onEdit === 'function' || actions?.length > 0
      ? {
          id: 'actions',
          disablePadding: false,
          label: '',
          disableSort: true,
          visible: true,
          sx: { minWidth: 80 },
        }
      : undefined,
  ].filter((header) => header !== undefined);

  if (!Array.isArray(rows)) {
    return null;
  }

  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let onBulkDelete;

  if (enableBulkDelete) {
    onBulkDelete = !options.radio
      ? (selected) => {
          deleteResetSelected(selected);
        }
      : undefined;
  }

  return (
    <Box sx={{ width: '100%' }}>
      {!options.hideSelectedCount && (
        <EnhancedTableToolbar
          bulkEditFields={bulkEditFields}
          title={title || ''}
          selected={selected}
          setSelected={setSelected}
          onDelete={onBulkDelete}
          headers={headersFiltered}
          onEdit={
            !options.radio
              ? async (selected, updateData) => {
                  await onBulkEdit(selected, updateData);
                }
              : undefined
          }
          onSync={
            onBulkSync
              ? async (selected, updateData) =>
                  await onBulkSync(selected, updateData)
              : undefined
          }
          bulkActions={bulkActions}
          customActions={customHeaderActions}
          refetch={refetch}
        />
      )}
      {useNewTable ? (
        <NewTable
          pagination={controlledPagination as NewTableProps['pagination']}
          columns={headersFiltered}
          data={rows}
          rowKey={rowKey}
          selectedRows={selected}
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          setSelected={setSelected as any}
          setSelectedData={setSelectedData}
          sorting={controlledOrdering}
          actionLoading={actionLoading}
          actions={actions}
          onEdit={onEdit}
          actionsEnabled={actionsEnabled}
          getSetActionLoadingByRowId={getSetActionLoadingByRowId}
          sums={sums}
          showTotals={showTotals}
          dynamicSelectsConfig={dynamicSelectsConfig}
          totals={totals}
          isLoading={false}
        />
      ) : (
        <>
          <TableContainer
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            ref={refContainer as any}
            sx={{
              height: `calc(100vh - ${headingOffset + APP_BAR_HEIGHT}px - ${
                !options.hideSelectedCount && selected.length > 0 ? 48 : 0
              }px)`,
              overflowX: isMobile ? 'clip' : 'auto',
              overflowY: 'auto',
              pb: '42px',
            }}
          >
            <Table
              sx={
                isMobile
                  ? {
                      tableLayout: 'fixed',
                      '.MuiTableCell-root': {
                        borderBottom: 'none',
                      },
                    }
                  : { tableLayout: 'auto' }
              }
              aria-labelledby="tableTitle"
              size={dense ? 'small' : 'medium'}
              stickyHeader={stickyHeader}
            >
              {!isMobile && (
                <EnhancedTableHead
                  actionsCol={undefined}
                  stickyPositions={stickyPositions}
                  onRenderColumnsWidth={onRenderColumnsWidth}
                  numSelected={selected.length}
                  order={orderToUse}
                  orderBy={orderByToUse}
                  onSelectAllClick={handleSelectAllClick}
                  onRequestSort={handleRequestSort}
                  rowCount={rows.length}
                  headCells={[
                    ...headersFiltered,
                    typeof onEdit === 'function' || actions?.length > 0
                      ? {
                          id: 'actions',
                          disablePadding: false,
                          label: '',
                          disableSort: true,
                          visible: true,
                          sx: { minWidth: 80 },
                        }
                      : undefined,
                  ].filter((header) => header !== undefined)}
                  onDelete={!options.radio ? onDelete : undefined}
                  options={options}
                />
              )}
              <TableBody>
                {rowsSorted
                  .slice(
                    page * rowsPerPageToUse,
                    page * rowsPerPageToUse + rowsPerPageToUse
                  )
                  .map((row, index) => {
                    const isItemSelected = row.id
                      ? isSelected(row.id)
                      : // TODO: Refactor, prone to error, please use id only
                        isSelected(Object.values(row).join('-'));
                    const labelId = `enhanced-table-checkbox-${index}`;

                    return isMobile ? (
                      <TableRowCard
                        row={row}
                        headersFiltered={headersFiltered}
                        outstandingFieldsInMobileView={
                          outstandingFieldsInMobileView
                        }
                        isItemSelected={isItemSelected}
                        handleClick={handleClick}
                        onDelete={onDelete}
                        onClick={onClick}
                        onEdit={onEdit}
                        readOnly={readOnly}
                        dynamicSelects={dynamicSelects}
                        navigate={navigate}
                        nonSelectable={nonSelectableOnMobile}
                        // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                        key={index}
                      />
                    ) : (
                      // biome-ignore lint/a11y/useSemanticElements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      <TableRow
                        hover
                        role="checkbox"
                        aria-checked={isItemSelected}
                        tabIndex={-1}
                        key={
                          row.rowKey ??
                          `${row.str_id ?? ''}_${row.uid ?? ''}_${row.id}`
                        }
                        selected={isItemSelected}
                        sx={{
                          ...rowSx,
                          '& .action-buttons-visible': {
                            visibility: 'visible',
                          },
                          position: 'relative',
                          ...(row.state === DataStates.GROUPED ||
                          row.state === DataStates.ALLOCATED
                            ? {
                                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                                opacity: 0.65,
                              }
                            : {}),
                          '&:hover': {
                            '& .cell-checkbox': {
                              backgroundColor: isItemSelected
                                ? Colors.SelectedHover
                                : Colors.Hover,
                            },
                            ...(rowSx ? rowSx['&:hover'] : {}),
                          },
                        }}
                      >
                        {onDelete && (
                          <TableCell
                            onClick={(event) => {
                              if (onDelete instanceof Function) {
                                handleClick(
                                  event,
                                  // TODO: Refactor, prone to error, please use id only
                                  row.id || Object.values(row).join('-')
                                );
                              } else if (
                                onClick instanceof Function &&
                                !onDelete
                              ) {
                                onClick(row);
                              }
                            }}
                            sx={{
                              position: 'sticky',
                              left: 0,
                              zIndex: 9,
                              background: 'white',
                            }}
                            padding="checkbox"
                          >
                            <Box
                              className="cell-checkbox"
                              sx={{
                                backgroundColor: isItemSelected
                                  ? Colors.Selected
                                  : '',
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                bottom: 0,
                                right: 0,
                              }}
                            />
                            {options.radio ? (
                              <Radio checked={isItemSelected} />
                            ) : (
                              <Checkbox
                                color="primary"
                                checked={isItemSelected}
                                inputProps={{
                                  'aria-labelledby': labelId,
                                }}
                              />
                            )}
                          </TableCell>
                        )}

                        {isTG && (
                          <TableCell
                            sx={{
                              position: onDelete ? 'sticky' : '',
                              paddingLeft: onDelete ? 0 : 2,
                              left: CheckboxWidth,
                              zIndex: 9,
                              background: onDelete ? 'white' : '',
                              textAlign: 'center',
                            }}
                          >
                            {onDelete && (
                              <Box
                                className="cell-checkbox"
                                sx={{
                                  backgroundColor: isItemSelected
                                    ? Colors.Selected
                                    : '',
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  bottom: 0,
                                  right: 0,
                                }}
                              />
                            )}
                            {index + 1}
                          </TableCell>
                        )}
                        {headersFiltered.map((header) => {
                          const headerKey = header.keyAs ?? header.id;
                          const formatter = (x, dynamicSelects) => {
                            if (header.tableFormatter instanceof Function) {
                              return header.tableFormatter(
                                x,
                                row,
                                dynamicSelects,
                                header
                              );
                            }
                            if (header.formatter instanceof Function) {
                              if (dynamicSelects) {
                                // TODO: Allow formatters to be applied when values are null
                                if (!x) return null;
                                if (isValidElement(x)) return x;
                                if (
                                  isValidElement(
                                    header.formatter(x, dynamicSelects)
                                  )
                                )
                                  return header.formatter(x, dynamicSelects);
                                return (
                                  <Chip
                                    label={header.formatter(x, dynamicSelects)}
                                    sx={{ m: 0.25 }}
                                    clickable={
                                      typeof header.linker === 'function'
                                    }
                                    component={
                                      typeof header.linker === 'function'
                                        ? Link
                                        : 'div'
                                    }
                                    to={
                                      typeof header.linker === 'function'
                                        ? header.linker(x)
                                        : undefined
                                    }
                                  />
                                );
                              }
                              if (x instanceof Object) {
                                const res = header.formatter(x, row, navigate);
                                if (res === null) return null;
                                return typeof res === 'object' &&
                                  !isValidElement(res)
                                  ? '---'
                                  : res;
                              }
                              return header.formatter(x, row, navigate);
                            }
                            if (x instanceof Object && !isValidElement(x)) {
                              return JSON.stringify(x);
                            }
                            if (header.type === 'boolean') {
                              return x ? 'Yes' : 'No';
                            }
                            return x;
                          };
                          let getter = header.getter;
                          if (!getter) {
                            getter =
                              header.type === 'select' &&
                              typeof header.options?.[0] === 'object'
                                ? (x) =>
                                    header.options.find(
                                      (option) => option.id === x[headerKey]
                                    )?.label
                                : (x) => x[headerKey];
                          }

                          const stickyStyle =
                            stickyPositions[header.id] >= 0
                              ? {
                                  position: 'sticky',
                                  left: stickyPositions[header.id],
                                  zIndex: 9,
                                  backgroundColor: 'white',
                                }
                              : null;

                          return (
                            <TableCell
                              align="left"
                              className={`cell-${header.id}`}
                              key={`${header.source ?? ''}${header.id}${
                                header.id2 ?? ''
                              }`}
                              sx={{
                                p: 1,
                                minWidth: row?.[header.id] ? 100 : 0,
                                display:
                                  header.visible === false ||
                                  header.hidden === true
                                    ? 'none'
                                    : 'table-cell',
                                ...header.sx,
                                ...stickyStyle,
                              }}
                            >
                              {stickyStyle && (
                                <Box
                                  className="cell-checkbox"
                                  sx={{
                                    backgroundColor: isItemSelected
                                      ? Colors.Selected
                                      : '',
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    bottom: 0,
                                    right: 0,
                                  }}
                                />
                              )}
                              <Box
                                sx={{
                                  whiteSpace: header.copyable
                                    ? 'nowrap'
                                    : 'normal',
                                  cursor: header.onClick
                                    ? 'pointer'
                                    : 'default',
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (header.onClick instanceof Function) {
                                    header.onClick(row);
                                  }
                                }}
                              >
                                <Box
                                  sx={{
                                    display: header.copyable
                                      ? 'inline-flex'
                                      : 'inherit',
                                  }}
                                >
                                  {row[headerKey] !== undefined ? (
                                    Array.isArray(row[header.id]) &&
                                    !header.tableFormatter ? (
                                      header.id === 'notes' ? (
                                        <Tooltip
                                          title={
                                            <Box>
                                              {row[header.id].map((item) => (
                                                <Box
                                                  key={item}
                                                  sx={{ my: 0.5 }}
                                                >
                                                  {item}
                                                </Box>
                                              ))}
                                            </Box>
                                          }
                                        >
                                          <span
                                            style={{ whiteSpace: 'nowrap' }}
                                          >
                                            {row[header.id].length
                                              ? `⚠️ ${
                                                  row[header.id].length
                                                } conflicts`
                                              : null}
                                          </span>
                                        </Tooltip>
                                      ) : (
                                        <SelectDbValsShowMore
                                          data={row[header.id].map(
                                            (item, i) => {
                                              const formattedVal = formatter(
                                                item,
                                                dynamicSelects[header.table]
                                                  ?.data ??
                                                  dynamicSelects[header.table]
                                                /**
                                                 * TODO: https://linear.app/fintary/issue/PLT-2188/fix-debt-tech-for-wront-objects-being-passedtsx
                                                 *
                                                 *      Tag @alanng2050 Alan which have more context (work done) in this file
                                                 *      1. the formatter only accept 2 arguments, but we are passing 3
                                                 *      I have deleted the 3rd one
                                                 */
                                              );
                                              if (formattedVal === null)
                                                return null;
                                              return (
                                                <span
                                                  key={
                                                    typeof item === 'object'
                                                      ? JSON.stringify(item)
                                                      : item
                                                  }
                                                >
                                                  {/* If array, but not a dynamic select (already chipped), chipify */}
                                                  {header.type ===
                                                  'dynamic-select' ? (
                                                    formattedVal
                                                  ) : isValidElement(
                                                      formattedVal
                                                    ) ? (
                                                    formattedVal
                                                  ) : (
                                                    <Chip
                                                      key={item}
                                                      label={formattedVal}
                                                      sx={{
                                                        m: 0.25,
                                                        cursor:
                                                          header.itemOnClick ||
                                                          header.linker
                                                            ? 'pointer'
                                                            : 'default',
                                                        maxWidth: 500,
                                                      }}
                                                      onClick={(e) => {
                                                        e.stopPropagation();
                                                        if (
                                                          typeof header.itemOnClick ===
                                                          'function'
                                                        )
                                                          header.itemOnClick(
                                                            item
                                                          );
                                                      }}
                                                      clickable={
                                                        typeof header.linker ===
                                                        'function'
                                                      }
                                                      component={
                                                        typeof header.linker ===
                                                        'function'
                                                          ? Link
                                                          : 'div'
                                                      }
                                                      to={
                                                        typeof header.linker ===
                                                        'function'
                                                          ? header.linker(item)
                                                          : undefined
                                                      }
                                                    />
                                                  )}
                                                  {header.delimiter &&
                                                    i <
                                                      row[header.id].length -
                                                        1 &&
                                                    header.delimiter}
                                                </span>
                                              );
                                            }
                                          )}
                                        />
                                      )
                                    ) : (
                                      formatter(
                                        header.id2 &&
                                          !(row[headerKey] instanceof Object)
                                          ? (row[headerKey]?.[header.id2]?.[
                                              headerKey
                                            ] ??
                                              row[headerKey]?.[header.id2]?.[
                                                header.id2
                                              ])
                                          : getter(row),
                                        header.table
                                          ? (dynamicSelects[header.table]
                                              ?.data ??
                                              dynamicSelects[header.table])
                                          : undefined
                                        /**
                                         * TODO: https://linear.app/fintary/issue/PLT-2188/fix-debt-tech-for-wront-objects-being-passedtsx
                                         *
                                         *      Tag @alanng2050 Alan which have more context (work done) in this file
                                         *      1. the formatter only accept 2 arguments, but we are passing 3
                                         *      I have deleted the 3rd one
                                         */
                                      )
                                    )
                                  ) : (
                                    ''
                                  )}
                                  {header.type ===
                                    FieldTypes.TABLE_CELL_CUSTOM &&
                                    header.formatter?.(row)}
                                </Box>
                                {header.copyable && (
                                  <IconButton
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const valUnformatted =
                                        header.id2 &&
                                        !(row[headerKey] instanceof Object)
                                          ? (row[headerKey]?.[header.id2]?.[
                                              headerKey
                                            ] ??
                                            row[headerKey]?.[header.id2]?.[
                                              header.id2
                                            ])
                                          : getter(row);
                                      const valFormatted =
                                        /**
                                         * TODO: https://linear.app/fintary/issue/PLT-2188/fix-debt-tech-for-wront-objects-being-passedtsx
                                         *
                                         *      Tag @alanng2050 Alan which have more context (work done) in this file
                                         *      1. this function have 2 parameters, but we are passing 1
                                         *      passing a empty object for now
                                         */
                                        formatter(valUnformatted, {});
                                      if (typeof valFormatted === 'string') {
                                        copy(valFormatted);
                                      } else {
                                        copy(valUnformatted);
                                      }
                                    }}
                                    sx={{
                                      opacity: 0,
                                      '&:hover': { opacity: 1 },
                                    }}
                                  >
                                    <ContentCopy />
                                  </IconButton>
                                )}
                              </Box>
                            </TableCell>
                          );
                        })}
                        {!readOnly && (
                          <TableCell sx={cellSx}>
                            {actionLoading[row.id] ? (
                              <Box
                                sx={{
                                  display: 'flex',
                                  justifyContent: 'center',
                                }}
                              >
                                <Box
                                  sx={{
                                    borderRadius: 8,
                                    backdropFilter: 'blur(8px)',
                                    display: 'flex',
                                  }}
                                >
                                  <CircularProgress
                                    disableShrink
                                    size={22}
                                    thickness={4}
                                  />
                                </Box>
                              </Box>
                            ) : (
                              <HoverActionButtonContainer
                                className={
                                  actionLoading[row.id]
                                    ? 'action-buttons-visible'
                                    : ''
                                }
                              >
                                {typeof onEdit === 'function' &&
                                  !lockRow(row) && (
                                    <IconButton
                                      onClick={() => {
                                        onEdit(row);
                                      }}
                                    >
                                      <Edit />
                                    </IconButton>
                                  )}
                                {lockRow(row) && (
                                  <IconButton disabled>
                                    <Lock />
                                  </IconButton>
                                )}
                                {actions?.length > 0 && actionsEnabled(row) && (
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                    }}
                                  >
                                    {actions
                                      ?.filter(
                                        (action) =>
                                          [
                                            'button',
                                            'iconButton',
                                            'icon',
                                            'custom',
                                          ].includes(action.type) &&
                                          (!action.enabled ||
                                            (typeof action.enabled ===
                                            'function'
                                              ? action.enabled(row)
                                              : action.enabled))
                                      )
                                      ?.map((action) => {
                                        const setRowActionLoading =
                                          getSetActionLoadingByRowId(row.id);
                                        switch (action.type) {
                                          case 'button':
                                            return (
                                              <Button
                                                variant="text"
                                                key={action.label}
                                                onClick={async (e) => {
                                                  setRowActionLoading(true);
                                                  await action.onClick(row, e);
                                                  setRowActionLoading(false);
                                                }}
                                              >
                                                {action.label}
                                              </Button>
                                            );
                                          case 'iconButton':
                                            return (
                                              <IconButton
                                                key={action.label}
                                                onClick={async (e) => {
                                                  setRowActionLoading(true);
                                                  await action.onClick(row, e);
                                                  setRowActionLoading(false);
                                                }}
                                              >
                                                {action.icon}
                                              </IconButton>
                                            );
                                          case 'icon':
                                            return (
                                              <Box
                                                key={action.label}
                                                sx={{
                                                  height: 32,
                                                  display: 'flex',
                                                }}
                                              >
                                                {action.icon}
                                              </Box>
                                            );
                                          case 'custom':
                                            return (
                                              <Box
                                                key={action.id}
                                                sx={{
                                                  height: 32,
                                                  display: 'flex',
                                                }}
                                              >
                                                {action.getComponent(row)}
                                              </Box>
                                            );
                                          default:
                                            return null;
                                        }
                                      })}
                                    {actions?.filter(
                                      (action) =>
                                        ![
                                          'button',
                                          'iconButton',
                                          'icon',
                                          'custom',
                                        ].includes(action.type) &&
                                        (!action.enabled ||
                                          (typeof action.enabled === 'function'
                                            ? action.enabled(row)
                                            : action.enabled))
                                    ).length > 0 && (
                                      <MoreMenu
                                        actions={actions.filter(
                                          (action) =>
                                            ![
                                              'button',
                                              'iconButton',
                                              'icon',
                                              'custom',
                                            ].includes(action.type) &&
                                            (!action.enabled ||
                                              (typeof action.enabled ===
                                              'function'
                                                ? action.enabled(row)
                                                : action.enabled))
                                        )}
                                        setActionLoading={getSetActionLoadingByRowId(
                                          row.id
                                        )}
                                        data={row}
                                      />
                                    )}
                                  </Box>
                                )}
                              </HoverActionButtonContainer>
                            )}
                          </TableCell>
                        )}
                      </TableRow>
                    );
                  })}
                {/* Totals row */}
                {showTotals && !isMobile && (
                  <TableRow>
                    <TableCell sx={{ pr: 0.5 }}>
                      <strong>Totals</strong>
                    </TableCell>
                    {columns.map((column, index) => (
                      <TableCell
                        key={`Sum-${column}-${
                          // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                          index
                        }`}
                        sx={{ px: 1 }}
                      >
                        {sums[index] ? (
                          typeof sums[index] === 'object' ? (
                            <Box sx={{ minWidth: 250 }}>
                              <ExpandableData
                                data={Object.entries(sums[index]).map(
                                  ([k, v]) =>
                                    `${Formatter.contact(
                                      /**
                                       * TODO: https://linear.app/fintary/issue/PLT-2188/fix-debt-tech-for-wront-objects-being-passedtsx
                                       *
                                       *      Tag @alanng2050 Alan which have more context (work done) in this file
                                       *      1. The object dynamicSelects doesnt contains object
                                       *      adding any for now
                                       */

                                      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                                      (dynamicSelects as any)?.contacts?.find(
                                        (c) => c.str_id === k
                                      ) ?? {},
                                      { account_id: selectedAccount?.accountId }
                                    )}: ${Formatter.currency(v)}`
                                )}
                                header={headersFiltered[index]}
                              />
                            </Box>
                          ) : (
                            Formatter.currency(sums[index])
                          )
                        ) : null}
                      </TableCell>
                    ))}
                    {(typeof onEdit === 'function' || actions?.length > 0) &&
                      !readOnly && <TableCell sx={{ padding: 0, width: 0 }} />}
                  </TableRow>
                )}
                {emptyRows > 0 && !isMobile && (
                  <TableRow
                    style={{
                      height: (dense ? 33 : 53) * emptyRows,
                    }}
                  >
                    <TableCell colSpan={6} />
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          {paginated &&
            (controlledPagination ? (
              <TablePagination
                rowsPerPageOptions={[50, 250, 500, 1000]}
                component="div"
                count={controlledPagination.count}
                rowsPerPage={controlledPagination.rowsPerPage}
                page={controlledPagination.page}
                onPageChange={controlledPagination.onPageChange}
                onRowsPerPageChange={controlledPagination.onRowsPerPageChange}
                sx={{
                  '&.MuiTablePagination-root': {
                    position: 'fixed',
                    bottom: 0,
                    right: 0,
                    background: 'white',
                  },
                  '& .MuiTablePagination-toolbar': {
                    minHeight: 42,
                    height: 42,
                    pl: 2,
                  },
                }}
              />
            ) : (
              <TablePagination
                rowsPerPageOptions={[50, 250, 500, 1000]}
                component="div"
                count={rows.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                sx={{
                  '&.MuiTablePagination-root': {
                    position: isMobile ? 'fixed' : 'static',
                    bottom: 0,
                    background: 'white',
                  },
                  '& .MuiTablePagination-toolbar': {
                    minHeight: 42,
                    height: 42,
                    pl: 2,
                  },
                }}
              />
            ))}
        </>
      )}
    </Box>
  );
};

export default EnhancedTable;
