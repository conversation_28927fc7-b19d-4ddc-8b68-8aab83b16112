import { <PERSON>renew, Delete, Edit } from '@mui/icons-material';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Too<PERSON><PERSON> } from '@mui/material';
import Box from '@mui/material/Box';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import { alpha } from '@mui/material/styles';
import { useCallback, useMemo, useState } from 'react';

import DataForm from '@/components/DataForm';
import { EnhancedSelect } from '../EnhancedSelect';
import useEnhancedTableToolbarStore from './store';

const FieldSelector = ({
  fields: originalFields,
  onFieldsChange,
  selectedFields,
}: {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  fields: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onFieldsChange: (selectedFields: any[]) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  selectedFields: any[];
}) => {
  const foundFields = useMemo(() => {
    return originalFields.filter((field) => field.bulkEdit);
  }, [originalFields]);

  const handleFieldsChange = useCallback(
    (ids: string[]) => {
      const selectedFields = originalFields.filter((field) =>
        ids.includes(field.id)
      );
      onFieldsChange(selectedFields);
    },
    [originalFields, onFieldsChange]
  );

  return (
    <Box sx={{ mt: 2 }}>
      <EnhancedSelect
        enableSearch
        multiple
        label="Fields to edit"
        options={foundFields}
        value={selectedFields}
        onChange={(v) => handleFieldsChange(v.map((v) => v.id))}
        labelKey="label"
        sx={{ width: '100%' }}
      />
    </Box>
  );
};

interface EnhancedTableToolbarProps {
  title: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  selected: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setSelected: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onDelete: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onSync: any;
  refetch?: () => void;
  bulkActions?: (({
    selected,
    isWorkingToolbar,
    setIsWorkingToolbar,
    refetch,
  }) => React.ReactElement)[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  headers: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onEdit: any;
  customActions?: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  bulkEditFields?: any[];
}

interface Field {
  id: string;
  enableNullCheckbox?: boolean;
  isBulkUpdate?: boolean;
}

interface DataDesc {
  fields: Field[];
}

const EnhancedTableToolbar: React.FC<EnhancedTableToolbarProps> = ({
  title,
  selected,
  setSelected,
  onDelete,
  onSync,
  bulkActions = [],
  headers,
  refetch,
  onEdit,
  customActions = false,
  bulkEditFields = [],
}) => {
  const fields = [...bulkEditFields, ...headers];
  const setIsBulkEdit = useEnhancedTableToolbarStore(
    (state) => state.setIsBulkEdit
  );
  const [isWorkingToolbar, setIsWorkingToolbar] = useState(false);
  const [open, setOpen] = useState(false);
  const [newDatum, setNewDatum] = useState({});
  const [dataDesc, setDataDesc] = useState<DataDesc>({ fields: [] });
  const numSelected = selected.length;

  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 600,
    bgcolor: 'background.paper',
    borderRadius: 2,
    pt: 1,
    px: 2,
    maxHeight: '90vh',
    overflowY: 'auto',
  };

  const handleClose = (_event, reason) => {
    if (reason && reason === 'backdropClick') return;
    setOpen(false);
    setIsWorkingToolbar(false);
    setNewDatum({});
    setIsBulkEdit(false);
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const handleFieldsChange = useCallback(
    (selectedFields) => {
      setDataDesc({
        fields: selectedFields.map((header) => ({
          enableNullCheckbox: true,
          ...header,
          isBulkUpdate: true,
        })),
      });
    },
    [setDataDesc]
  );

  return numSelected > 0 ? (
    <>
      <Toolbar
        sx={{
          pl: { sm: 2 },
          pr: { xs: 1, sm: 1 },
          ...(numSelected > 0 && {
            bgcolor: (theme) =>
              alpha(
                theme.palette.primary.main,
                theme.palette.action.activatedOpacity
              ),
          }),
        }}
        variant="dense"
      >
        {numSelected > 0 ? (
          <Typography
            sx={{ flex: '1 1 100%' }}
            color="inherit"
            variant="subtitle1"
            component="div"
          >
            {numSelected} selected
          </Typography>
        ) : (
          <Typography
            sx={{ flex: '1 1 100%' }}
            variant="h6"
            id="tableTitle"
            component="div"
          >
            {title}
          </Typography>
        )}
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          {numSelected > 0 && !customActions && onEdit instanceof Function && (
            <Tooltip title="Edit">
              <Button
                loading={isWorkingToolbar}
                onClick={async () => {
                  setIsWorkingToolbar(true);
                  setOpen(true);
                  setIsBulkEdit(true);
                }}
              >
                <Edit />
              </Button>
            </Tooltip>
          )}
          {numSelected > 0 &&
            !customActions &&
            onDelete instanceof Function && (
              <Tooltip title="Delete">
                <Button
                  loading={isWorkingToolbar}
                  onClick={async () => {
                    if (
                      confirm(
                        'Are you sure you want to delete the selected items?'
                      )
                    ) {
                      setIsWorkingToolbar(true);
                      await onDelete(selected);
                      setIsWorkingToolbar(false);
                    }
                  }}
                >
                  <Delete />
                </Button>
              </Tooltip>
            )}
          {numSelected > 0 && !customActions && onSync instanceof Function && (
            <Tooltip title="Sync">
              <Button
                loading={isWorkingToolbar}
                onClick={async () => {
                  if (
                    confirm('Are you sure you want to sync the selected items?')
                  ) {
                    setIsWorkingToolbar(true);
                    await onSync(selected);
                    setIsWorkingToolbar(false);
                  }
                }}
              >
                <Autorenew />
              </Button>
            </Tooltip>
          )}
          {numSelected > 0 && bulkActions?.length > 0
            ? // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              bulkActions.map((action: any, index) => {
                return action({
                  selected,
                  key: index,
                  isWorkingToolbar,
                  setIsWorkingToolbar,
                  refetch,
                });
              })
            : null}
        </Box>
      </Toolbar>

      <Modal open={open} onClose={handleClose} sx={{ overflowY: 'scroll' }}>
        <Box sx={style}>
          <Typography variant="h6" component="h2">
            Bulk editor
          </Typography>
          <FieldSelector
            fields={fields}
            selectedFields={dataDesc.fields}
            onFieldsChange={handleFieldsChange}
          />
          <Divider sx={{ my: 1 }} />
          <DataForm
            // @ts-ignore
            dataDesc={{ saveOnly: true }}
            fields={dataDesc.fields}
            newData={newDatum}
            setNewData={setNewDatum}
            onCancel={() => {
              setIsWorkingToolbar(false);
              setOpen(false);
            }}
            onSave={async () => {
              // Filter newDatum to only include keys that exist in dataDesc.fields[i].id and are not null
              // @deprecated
              // should use the clearDataFields for easier handling
              const filteredNewDatum = Object.keys(newDatum).reduce(
                (acc, key) => {
                  const [fieldId, suffix] = key.split('-');
                  const field = dataDesc.fields.find(
                    (field) => field.id === fieldId
                  );
                  if (field && (newDatum[key] !== null || suffix !== 'null')) {
                    acc[key] = newDatum[key];
                  }
                  return acc;
                },
                {}
              );

              // Collect all fields with clear data flag
              const clearDataFields = Object.keys(newDatum).reduce(
                (acc, key) => {
                  if (key.endsWith('-null')) {
                    const fieldId = key.split('-')[0];
                    acc.push(fieldId);
                  }
                  return acc;
                },
                [] as string[]
              );

              await onEdit(selected, { ...filteredNewDatum, clearDataFields });
              setIsWorkingToolbar(false);
              setOpen(false);
              setSelected([]);
              setNewDatum({});
            }}
            formModeOnly={false}
            embed={true}
            oldData={undefined}
            onDelete={undefined}
          />
        </Box>
      </Modal>
    </>
  ) : (
    // biome-ignore lint/complexity/noUselessFragments: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    <></>
  );
};

export default EnhancedTableToolbar;
