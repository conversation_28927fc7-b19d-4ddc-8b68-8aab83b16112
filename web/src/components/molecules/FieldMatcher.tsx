import { RemoveCircleOutline } from '@mui/icons-material';
import { Box, Button, IconButton } from '@mui/material';

import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import FieldMatcherComponent from '@/components/molecules/FieldMatcherComponent';
import FieldMatcherOperatorSelector, {
  isString,
} from '@/components/molecules/FieldMatcherOperatorSelector';

const FieldMatcher = ({
  fields,
  value: fieldsMatchers = [],
  setValue,
  addLabel = 'Add',
  hideUsePolicyData = false,
  sx = {},
}: {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  fields: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  value: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setValue: (val: any) => void;
  addLabel?: string;
  hideUsePolicyData?: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  sx?: any;
}) => {
  const getSelectedField = (fieldId) => {
    return fields.find((f) => f.id === fieldId);
  };

  const addtionalOptions = [
    { name: 'Case sensitive ', id: 'caseSensitive', validate: [isString] },
    { name: 'Skip empty', id: 'skipEmpty' },
  ];

  if (!hideUsePolicyData) {
    addtionalOptions.push({
      name: 'Use policy data',
      id: 'shouldUsePolicyData',
    });
  }

  return (
    <Box
      sx={{
        ...sx,
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        alignItems: 'flex-start',
      }}
    >
      {fieldsMatchers?.map(
        (fieldMatcher, i) =>
          fieldMatcher.type !== 'Action' && (
            <Box
              // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              key={i}
              sx={{
                mt: 1,
                display: 'flex',
                flexWrap: 'wrap',
                width: '100%',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <EnhancedSelect
                enableSearch
                label="Field"
                options={fields?.sort((a, b) => (b.label > a.label ? -1 : 1))}
                labelKey="label"
                value={fields.find((item) => item.id === fieldMatcher.field)}
                onChange={(item) => {
                  const newFieldMatchers = [...fieldsMatchers];
                  newFieldMatchers[i] = { field: item.id };
                  setValue(newFieldMatchers);
                }}
                sx={{ marginRight: 0 }}
              />

              {/* If a field is selected, show the operator select options */}
              {fieldMatcher?.field ? (
                <>
                  <FieldMatcherOperatorSelector
                    fieldMatcher={fieldMatcher}
                    getSelectedField={getSelectedField}
                    onOperatorChange={(key, item) => {
                      const newFieldMatchers = [...fieldsMatchers];
                      newFieldMatchers[i][key] = item;
                      setValue(newFieldMatchers);
                    }}
                  />

                  {/* If an operator is selected, show the filter values */}
                  {fieldMatcher?.op ? (
                    <>
                      <FieldMatcherComponent
                        // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                        key={i}
                        fields={fields}
                        fieldMatcher={fieldMatcher}
                        fieldsMatchers={fieldsMatchers}
                        i={i}
                        setValue={setValue}
                      />

                      {fieldMatcher?.value ? (
                        <EnhancedSelect
                          multiple
                          label="Options"
                          options={addtionalOptions.filter((op) => {
                            const selectedField = getSelectedField(
                              fieldMatcher?.field
                            );
                            if (
                              op.validate &&
                              selectedField &&
                              !op.validate.some((validateFn) =>
                                validateFn(
                                  selectedField?.fieldMatcherType ??
                                    selectedField?.type
                                )
                              )
                            ) {
                              return false;
                            }
                            return true;
                          })}
                          value={addtionalOptions.filter((item) => {
                            return fieldsMatchers[i][item.id];
                          })}
                          onChange={(v) => {
                            const newFieldMatchers = [...fieldsMatchers];

                            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                            addtionalOptions.forEach((item) => {
                              newFieldMatchers[i][item.id] = false;
                            });

                            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                            v.forEach((item) => {
                              newFieldMatchers[i][item.id] = true;
                            });
                            setValue(newFieldMatchers);
                          }}
                        />
                      ) : null}
                    </>
                  ) : null}
                </>
              ) : null}
              <Box>
                <IconButton
                  onClick={() => {
                    const newFieldMatchers = [...fieldsMatchers];
                    newFieldMatchers.splice(i, 1);
                    setValue(newFieldMatchers);
                  }}
                >
                  <RemoveCircleOutline />
                </IconButton>
              </Box>
            </Box>
          )
      )}
      <Button
        onClick={() => setValue([...fieldsMatchers, {}])}
        sx={{ mt: 0.5 }}
      >
        {addLabel}
      </Button>
    </Box>
  );
};

export default FieldMatcher;
