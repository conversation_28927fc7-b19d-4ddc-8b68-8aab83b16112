import { Button } from '@mui/material';

import GroupBySelector from './GroupBySelector';
import type { GroupBy, GroupByFieldDefinition } from 'common/dto/widgets';

interface GroupByManagerProps {
  fields: GroupByFieldDefinition[];
  groupBys: GroupBy[];
  addGroupBy: () => void;
  removeGroupBy: (id: number) => void;
  updateGroupBy: (id: number, updatedField: Partial<{ field: string }>) => void;
}

const GroupByManager = ({
  fields,
  groupBys,
  addGroupBy,
  removeGroupBy,
  updateGroupBy,
}: GroupByManagerProps) => {
  return (
    <div style={{ width: '100%' }}>
      {groupBys.map((groupBy) => (
        <GroupBySelector
          key={groupBy.id}
          fields={fields}
          selectedGroupBy={groupBy}
          onRemove={() => removeGroupBy(groupBy.id)}
          onUpdate={(updatedField) => updateGroupBy(groupBy.id, updatedField)}
        />
      ))}
      <Button variant="text" onClick={addGroupBy} data-name="add-group-by">
        Add
      </Button>
    </div>
  );
};

export default GroupByManager;
