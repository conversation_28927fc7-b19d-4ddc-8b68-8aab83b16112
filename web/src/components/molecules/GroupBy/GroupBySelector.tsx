import { Box, IconButton, Chip, Menu, MenuItem } from '@mui/material';
import { useMemo, useState } from 'react';
import type { GroupBy, GroupByFieldDefinition } from 'common/dto/widgets';
import { WidgetTimePeriodLabels } from 'common/dto/widgets';
import { EnhancedSelect } from '../EnhancedSelect';
import {
  RemoveCircleOutline,
  CalendarMonthOutlined,
} from '@mui/icons-material';

interface GroupBySelectorProps {
  fields: GroupByFieldDefinition[];
  selectedGroupBy: GroupBy;
  onRemove: () => void;
  onUpdate: (updatedGroupBy: Partial<GroupBy>) => void;
}

const TIME_PERIOD_OPTIONS = [
  { id: null, label: 'Unspecified' },
  ...Object.keys(WidgetTimePeriodLabels).map((id) => ({
    id,
    label: WidgetTimePeriodLabels[id],
  })),
];

const GroupBySelector = ({
  fields,
  selectedGroupBy,
  onRemove,
  onUpdate,
}: GroupBySelectorProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const options = useMemo(
    () =>
      fields.map((field) => ({
        id: field.name,
        label: field.displayName,
        type: field.fieldMatcherType,
      })),
    [fields]
  );

  const selectedGroupByDef = fields.find(
    (field) => field.name === selectedGroupBy.field
  );

  const isDateField =
    selectedGroupByDef?.fieldMatcherType === 'date' ||
    selectedGroupBy.field?.toLowerCase().includes('date');

  const getFieldLabel = () => {
    return selectedGroupByDef?.displayName || selectedGroupBy.field;
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleTimePeriodChange = (id: string | null) => {
    onUpdate({ timePeriod: id });
    handleMenuClose();
  };

  const timePeriodLabel =
    selectedGroupBy.timePeriod === null ||
    selectedGroupBy.timePeriod === undefined
      ? 'Unspecified'
      : (WidgetTimePeriodLabels[selectedGroupBy.timePeriod] ??
        selectedGroupBy.timePeriod.charAt(0).toUpperCase() +
          selectedGroupBy.timePeriod.slice(1));

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
      className="mt-3"
    >
      <Box data-name="group-by-field" className="w-1/2">
        <EnhancedSelect
          enableSearch
          label="Field"
          options={options}
          value={{
            id: selectedGroupBy.field,
            label: getFieldLabel(),
            type: selectedGroupByDef?.fieldMatcherType,
          }}
          onChange={(value) => {
            onUpdate({ field: value.id });
          }}
          sx={{ width: '100%' }}
        />
      </Box>

      {isDateField && (
        <Box ml={1} display={'flex'} alignItems={'center'}>
          <Chip
            sx={{ marginRight: 1 }}
            data-name="group-by-time-period"
            onClick={handleMenuOpen}
            label={timePeriodLabel}
            avatar={<CalendarMonthOutlined />}
          />
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            {TIME_PERIOD_OPTIONS.map((option) => (
              <MenuItem
                key={option.id ?? 'unspecified'}
                selected={selectedGroupBy.timePeriod === option.id}
                onClick={() => handleTimePeriodChange(option.id)}
              >
                {option.label}
              </MenuItem>
            ))}
          </Menu>
        </Box>
      )}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          gap: 1,
          width: '100%',
        }}
      >
        <IconButton onClick={onRemove}>
          <RemoveCircleOutline />
        </IconButton>
      </div>
    </div>
  );
};

export default GroupBySelector;
