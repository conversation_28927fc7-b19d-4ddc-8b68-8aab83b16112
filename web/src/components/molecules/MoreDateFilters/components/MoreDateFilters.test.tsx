import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';

import { MoreDateFilters } from './MoreDateFilters';
import { FieldTypes } from '@/types';
import type { MoreDateFilter } from '../types';

const mockOnSetValue = vi.fn();

const filters: MoreDateFilter[] = [
  {
    filterFieldName: 'Date Range 1',
    filterFieldId: 'date-range-1',
    filters: [
      { filterKey: 'startDate', label: 'start date', type: FieldTypes.DATE },
      { filterKey: 'endDate', label: 'end date', type: FieldTypes.DATE },
      {
        filterKey: 'includeBlanks',
        label: 'Include blanks',
        type: FieldTypes.BOOLEAN,
      },
    ],
    dateRangeProps: { openTo: 'day', views: ['day', 'month', 'year'] },
  },
];

const params = new URLSearchParams({
  startDate: '2024-01-01',
  endDate: '2024-01-31',
  includeBlanks: 'true',
});
const values = params;

describe('MoreDateFilters', () => {
  beforeEach(() => {
    mockOnSetValue.mockClear();
  });

  it('Given MoreDateFilters component, should render IconButton and title', () => {
    render(
      <MoreDateFilters
        title="Test Title"
        filters={filters}
        values={values}
        onSetValue={mockOnSetValue}
      />
    );
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('Given IconButton click, should open Popover', () => {
    render(
      <MoreDateFilters
        title="Test Title"
        filters={filters}
        values={values}
        onSetValue={mockOnSetValue}
      />
    );
    fireEvent.click(screen.getByRole('button'));
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Include blanks')).toBeInTheDocument();
  });

  it('Given value is "true", should render checked Checkbox', () => {
    render(
      <MoreDateFilters
        title="Test Title"
        filters={filters}
        values={values}
        onSetValue={mockOnSetValue}
      />
    );
    fireEvent.click(screen.getByRole('button'));
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeChecked();
  });

  it('Given Checkbox is toggled, should call onSetValue', () => {
    render(
      <MoreDateFilters
        title="Test Title"
        filters={filters}
        values={values}
        onSetValue={mockOnSetValue}
      />
    );
    fireEvent.click(screen.getByRole('button'));
    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);
    expect(mockOnSetValue).toHaveBeenCalledWith('includeBlanks', '');
  });

  it('Given missing checkbox filter, should handle gracefully', () => {
    const filtersNoCheckbox: MoreDateFilter[] = [
      {
        filterFieldName: 'Date Range 2',
        filterFieldId: 'date-range-2',
        filters: [
          {
            filterKey: 'startDate',
            label: 'start date',
            type: FieldTypes.DATE,
          },
          { filterKey: 'endDate', label: 'end date', type: FieldTypes.DATE },
        ],
        dateRangeProps: {},
      },
    ];
    render(
      <MoreDateFilters
        title="No Checkbox"
        filters={filtersNoCheckbox}
        values={values}
        onSetValue={mockOnSetValue}
      />
    );
    fireEvent.click(screen.getByRole('button'));
    expect(screen.queryByRole('checkbox')).not.toBeInTheDocument();
  });

  it('Given missing date fields, should handle gracefully', () => {
    const filtersNoDate = [
      {
        filterFieldId: 'no-date',
        filters: [
          {
            filterKey: 'includeBlanks',
            label: 'Include blanks',
            type: FieldTypes.BOOLEAN,
          },
        ],
        dateRangeProps: {},
      },
    ] as MoreDateFilter[];
    render(
      <MoreDateFilters
        title="No Date"
        filters={filtersNoDate}
        values={values}
        onSetValue={mockOnSetValue}
      />
    );
    fireEvent.click(screen.getByRole('button'));
    expect(screen.getByRole('checkbox')).toBeInTheDocument();
  });
});
