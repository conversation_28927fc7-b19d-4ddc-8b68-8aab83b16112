import { isProductionApp } from '@/env';
import { MoreVert } from '@mui/icons-material';
import { Box, Checkbox, IconButton, Popover, Typography } from '@mui/material';
import * as React from 'react';

import BasicDateRangePicker from '@/common/BasicDateRangePicker';
import { FieldTypes } from '@/types';
import type { MoreDateFilter, MoreDateFiltersProps } from '../types';

export const MoreDateFilters = ({
  title,
  filters,
  values,
  onSetValue,
}: MoreDateFiltersProps) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const openMore = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const checkBoxComponent = (filters: MoreDateFilter['filters']) => {
    const checkBox = filters.find(
      (filter) => filter.type === FieldTypes.BOOLEAN
    );

    if (!checkBox) {
      console.error('Filters are missing required fields: ', { filters });
      return null;
    }

    return (
      <Checkbox
        checked={values.get(checkBox.filterKey) === 'true'}
        onChange={(e) => {
          onSetValue(checkBox.filterKey, e.target.checked ? 'true' : '');
        }}
        key={checkBox.filterKey}
      />
    );
  };

  const dateRangeComponent = ({ filters, dateRangeProps }: MoreDateFilter) => {
    const startDate = filters.find((filter) => filter.label?.includes('start'));
    const endDate = filters.find((filter) => filter.label?.includes('end'));

    if (!startDate || !endDate) {
      if (!isProductionApp()) {
        console.error('Filters are missing required fields: ', { filters });
      }
      return null;
    }

    return (
      <BasicDateRangePicker
        openTo={dateRangeProps?.openTo}
        views={dateRangeProps?.views}
        range={{
          startDate: values.get(startDate.filterKey) || null,
          endDate: values.get(endDate.filterKey) || null,
          startDateLabel: startDate.label || 'Start Date',
          endDateLabel: endDate.label || 'End Date',
        }}
        onChange={(range) => {
          onSetValue(startDate.filterKey, range.startDate as string);
          onSetValue(endDate.filterKey, range.endDate as string);
        }}
        key={`${startDate.filterKey}-to-${endDate.filterKey}`}
        width={210}
        mt={1}
        my={1}
      />
    );
  };

  return (
    <Box>
      <IconButton id="more-date-filters-btn" onClick={handleClick}>
        <MoreVert />
      </IconButton>
      <Popover
        id="more-date-filter-view"
        open={openMore}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        sx={{ ml: -36, mt: 1 }}
      >
        <Box sx={{ p: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="subtitle2">{title}</Typography>
            <Typography
              variant="caption"
              sx={{ maxWidth: 60, textAlign: 'right', lineHeight: 1.2 }}
            >
              Include blanks
            </Typography>
          </Box>
          <Box>
            {filters.map((filter) => (
              <Box
                key={filter.filterFieldId}
                sx={{ display: 'flex', justifyContent: 'space-between' }}
              >
                {dateRangeComponent(filter)}
                {checkBoxComponent(filter.filters)}
              </Box>
            ))}
          </Box>
        </Box>
      </Popover>
    </Box>
  );
};
