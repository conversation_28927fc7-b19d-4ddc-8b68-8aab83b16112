import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';

import MultiSelect from './MultiSelect';

describe('MultiSelect Component', () => {
  const defaultProps = {
    label: 'Test Label',
    values: ['Option 1', 'Option 2', 'Option 3'],
    selectedValues: [],
    setSelectedValues: vi.fn(),
  };

  it('should render without crashing', () => {
    render(<MultiSelect {...defaultProps} />);
    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
  });

  it('should render the label correctly', () => {
    render(<MultiSelect {...defaultProps} />);
    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
  });

  it('should handle selection of items', () => {
    render(<MultiSelect {...defaultProps} />);
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    const option = screen.getByText('Option 1');
    fireEvent.click(option);
    expect(defaultProps.setSelectedValues).toHaveBeenCalledWith(['Option 1']);
  });

  it('should filter items based on search query', () => {
    render(<MultiSelect {...defaultProps} enableSearch />);
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    const searchInput = screen.getByPlaceholderText('Search');
    fireEvent.change(searchInput, { target: { value: 'Option 2' } });
    expect(screen.queryByText('Option 1')).not.toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
  });

  it('should handle "All" selection', () => {
    render(<MultiSelect {...defaultProps} />);
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    const allOption = screen.getByText('All');
    fireEvent.click(allOption);
    expect(defaultProps.setSelectedValues).toHaveBeenCalledWith([
      'Option 1',
      'Option 2',
      'Option 3',
    ]);
  });

  it('should handle pagination', () => {
    const paginatedProps = {
      ...defaultProps,
      values: Array.from({ length: 200 }, (_, i) => `Option ${i + 1}`),
      paginate: true,
      paginateStep: 50,
    };
    render(<MultiSelect {...paginatedProps} />);
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    expect(screen.getByText('Show more (150)')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Show more (150)'));
    expect(screen.getByText('Show more (100)')).toBeInTheDocument();
  });
});

describe('MultiSelect Component with Object Values', () => {
  const defaultProps = {
    label: 'Test Label',
    values: [
      { id: 1, name: 'Option 1' },
      { id: 2, name: 'Option 2' },
      { id: 3, name: 'Option 3' },
    ],
    selectedValues: [],
    setSelectedValues: vi.fn(),
    formatter: (val) => val.name,
    valuer: (val) => val.id,
  };

  it('should render without crashing', () => {
    render(<MultiSelect {...defaultProps} />);
    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
  });

  it('should render the label correctly', () => {
    render(<MultiSelect {...defaultProps} />);
    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
  });

  it('should handle selection of items', () => {
    render(<MultiSelect {...defaultProps} />);
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    const option = screen.getByText('Option 1');
    fireEvent.click(option);
    expect(defaultProps.setSelectedValues).toHaveBeenCalledWith([1]);
  });

  it('should filter items based on search query', () => {
    render(<MultiSelect {...defaultProps} enableSearch />);
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    const searchInput = screen.getByPlaceholderText('Search');
    fireEvent.change(searchInput, { target: { value: 'Option 2' } });
    expect(screen.queryByText('Option 1')).not.toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
  });

  it('should handle "All" selection', () => {
    render(<MultiSelect {...defaultProps} />);
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    const allOption = screen.getByText('All');
    fireEvent.click(allOption);
    expect(defaultProps.setSelectedValues).toHaveBeenCalledWith([1, 2, 3]);
  });

  it('should handle pagination', () => {
    const paginatedProps = {
      ...defaultProps,
      values: Array.from({ length: 200 }, (_, i) => ({
        id: i + 1,
        name: `Option ${i + 1}`,
      })),
      paginate: true,
      paginateStep: 50,
    };
    render(<MultiSelect {...paginatedProps} />);
    const selectElement = screen.getByLabelText('Test Label');
    fireEvent.mouseDown(selectElement);
    expect(screen.getByText('Show more (150)')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Show more (150)'));
    expect(screen.getByText('Show more (100)')).toBeInTheDocument();
  });
});
