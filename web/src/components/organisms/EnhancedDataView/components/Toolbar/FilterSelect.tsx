import { Box, IconButton, Tooltip } from '@mui/material';
import { useMemo } from 'react';
import FilterAltOffOutlined from '@mui/icons-material/FilterAltOffOutlined';

import { useIsMobile } from '../../hooks/useIsMobile';
import { useEnhancedDataViewStore } from '../../store';
import type { DataSpec, EnhancedDataViewProps } from '../../types';
import { FieldTypes } from '@/types';
import { useSearchParamsUrl } from '../../hooks/useParams';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { getTestId } from '@/utils/test';

export const FilterSelect = ({
  sortFilterByPosition,
  filters,
  enableResetFilters,
}: Pick<EnhancedDataViewProps, 'enableResetFilters'> &
  Pick<DataSpec, 'filters' | 'sortFilterByPosition'>) => {
  const { isMobile } = useIsMobile();
  const { searchParams, setSearchParams } = useSearchParamsUrl();
  const filterList = useEnhancedDataViewStore((s) => s.filterList);

  // TODO: UI filter will be refactored soon
  // This is a short term solution to make expected filter order
  const sortedFilters = useMemo(() => {
    const DEFAULT_SORT_POSITION = 1000; // If no sort position is defined, we put it at the end
    let list = filterList
      .filter((v) => v.type === FieldTypes.MULTI_SELECT)
      .map(({ ...filter }) => {
        filter.label = filters?.[filter.id]?.label || filter.label; // This is the label shown in the UI
        filter.sortPosition =
          filters?.[filter.id]?.sortPosition || DEFAULT_SORT_POSITION;
        return filter;
      });

    if (sortFilterByPosition) {
      list = list.sort((a, b) => {
        // biome-ignore lint/style/noNonNullAssertion: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        return a.sortPosition! - b.sortPosition!;
      });
    } else {
      list = list.sort((a, b) => {
        return a.label.localeCompare(b.label);
      });
    }
    return list;
  }, [filterList, filters, sortFilterByPosition]);

  const getValues = (paramKey: string) => {
    const paramValues = searchParams.getAll(paramKey) || [];
    // Unselect all
    if (paramValues.length === 1 && paramValues[0] === 'undefined') {
      return [];
    }

    const filter = sortedFilters.find((f) => f.id === paramKey);
    if (!filter?.options) return [];

    let selected = paramValues.length
      ? paramValues
          .map((val) =>
            filter.options?.find((option) => String(option.id) === val)
          )
          .filter((item) => !!item)
      : filter.options;

    // Handle unselected_contacts
    if (paramKey === 'contacts') {
      const unselected = searchParams.getAll('unselected_contacts');
      if (Array.isArray(unselected) && unselected.length > 0) {
        selected = selected.filter(
          (item) => !unselected.includes(String(item.id))
        );
      }
    }

    return selected;
  };

  const { objectSelects, stringSelects } = useMemo(() => {
    const objectSelects: typeof sortedFilters = [];
    const stringSelects: typeof sortedFilters = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    sortedFilters.forEach((filter) => {
      if (filter.options?.every((option) => typeof option === 'object')) {
        objectSelects.push(filter);
      } else {
        stringSelects.push(filter);
      }
    });
    return { objectSelects, stringSelects };
  }, [sortedFilters]);

  return (
    <Box
      sx={{
        mt: isMobile ? 1 : 0,
        display: 'flex',
        flexWrap: isMobile ? 'wrap' : 'nowrap',
        alignItems: 'center',
        flexShrink: 0,
        gap: 1,
      }}
    >
      {/* Select with options that are objects */}
      {objectSelects.map(({ id: k, options = [] }) => {
        const selectedValue = getValues(k);
        const filterConfig = filters?.[k] || {};

        return (
          <EnhancedSelect
            key={k}
            enableActiveColor={
              selectedValue.length > 0 && selectedValue.length < options.length
            }
            sx={{ minWidth: 130, width: 'fit-content' }}
            label={filterConfig.label || k}
            enableSearch
            multiple
            options={options}
            value={selectedValue}
            tokenizeSearch={filterConfig.tokenizeSearch}
            listContainerSx={filterConfig.listContainerSx}
            renderLabel={filterConfig.renderLabel}
            renderValue={filterConfig.renderValue}
            onChange={(values) => {
              const ids = values.map((item) => item.id);

              // Select all
              if (!values.length) {
                setSearchParams((prev) => {
                  prev.set(k, 'undefined');
                  return prev;
                });
              } else {
                setSearchParams((prev) => {
                  prev.delete(k);
                  // If select all values, we remove params on the url instead
                  const deselectedValues = options.filter(
                    (o) => !values.find((v) => v.id === o.id)
                  );
                  if (ids.length !== options.length) {
                    if (
                      k === 'contacts' &&
                      ids.length > 50 &&
                      deselectedValues.length > 0
                    ) {
                      // Clear all contacts=
                      prev.delete(k);
                      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      deselectedValues.forEach((v) => {
                        prev.append('unselected_contacts', v.id);
                      });
                    } else {
                      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      ids.forEach((v) => {
                        prev.append(k, v);
                      });
                    }
                  }
                  return prev;
                });
              }
            }}
          />
        );
      })}

      {stringSelects.map(({ id: k, options = [] }) => {
        const selectedValue = searchParams.getAll(k)?.length
          ? searchParams.getAll(k).filter((k) => k && k !== 'undefined')
          : options;
        const filterConfig = filters?.[k] || {};

        return (
          <EnhancedSelect
            key={k}
            sx={{ minWidth: 130, width: 'fit-content' }}
            label={filterConfig.label || k}
            enableSearch
            multiple
            enableActiveColor={
              selectedValue.length > 0 && selectedValue.length < options.length
            }
            options={options}
            listContainerSx={filterConfig.listContainerSx}
            renderLabel={filterConfig.renderLabel}
            renderValue={filterConfig.renderValue}
            value={selectedValue}
            onChange={(values) => {
              if (!values.length) {
                setSearchParams((prev) => {
                  prev.set(k, 'undefined');
                  return prev;
                });
              } else {
                setSearchParams((prev) => {
                  prev.delete(k);
                  // If select all values, we remove params on the url instead
                  if (values.length !== options.length) {
                    // Get the deselected values
                    const deselectedValues = options.filter(
                      (o) => !values.find((v) => v.id === o.id)
                    );
                    if (k === 'contacts' && deselectedValues.length > 0) {
                      // Clear all contacts=
                      prev.delete(k);
                      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      deselectedValues.forEach((v) => {
                        prev.append('unselected_contacts', v.id);
                      });
                    } else {
                      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      values.forEach((v) => {
                        prev.append(k, v);
                      });
                    }
                  }
                  return prev;
                });
              }
            }}
          />
        );
      })}

      {filterList.length > 0 && enableResetFilters && searchParams.size > 0 && (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Tooltip title="Clear filters">
            <IconButton
              {...getTestId('btn-clear-filters')}
              onClick={() => {
                setSearchParams({});
              }}
              sx={{ mr: 1 }}
            >
              <FilterAltOffOutlined />
            </IconButton>
          </Tooltip>
        </Box>
      )}
    </Box>
  );
};
