import { render, screen, fireEvent } from '@testing-library/react';

import {
  mockSetIsFilterScrollable,
  setIsFilterScrollable,
} from '../../__mocks__/store';
import { Filters } from './Filters';

jest.mock('@mui/icons-material/ChevronLeft', () => () => (
  <span>ChevronLeftIcon</span>
));
jest.mock('@mui/icons-material/ChevronRight', () => () => (
  <span>ChevronRightIcon</span>
));

// Mock child components
jest.mock('./FilterQueryChips', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  FilterQueryChips: ({ queryChips }: any) => {
    return (
      <div data-testid="filter-query-chips">{JSON.stringify(queryChips)}</div>
    );
  },
}));
jest.mock('./FilterDateRange', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  FilterDateRange: ({ dateFilters }: any) => (
    <div data-testid="filter-date-range">{JSON.stringify(dateFilters)}</div>
  ),
}));
jest.mock('./FilterSelect', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  FilterSelect: ({ filters }: any) => (
    <div data-testid="filter-select">{JSON.stringify(filters)}</div>
  ),
}));

describe('Filters', () => {
  const defaultProps = {
    queryChips: [{ label: 'chip1' }],
    table: 'testTable',
    dateFilters: [{ id: 'date1' }],
    filters: [{ id: 'filter1' }],
    enableResetFilters: true,
    sortFilterByPosition: false,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  } as any;

  beforeEach(() => {
    setIsFilterScrollable(false);
    mockSetIsFilterScrollable.mockClear();
  });

  it('Given FilterQueryChips, FilterDateRange, and FilterSelect, should render them', () => {
    render(<Filters {...defaultProps} />);
    expect(screen.getByTestId('filter-query-chips')).toBeInTheDocument();
    expect(screen.getByTestId('filter-date-range')).toBeInTheDocument();
    expect(screen.getByTestId('filter-select')).toBeInTheDocument();
  });

  it('Given navigation icons, should render left and right icons', () => {
    render(<Filters {...defaultProps} />);
    expect(screen.getAllByText(/ChevronLeftIcon/)).toHaveLength(1);
    expect(screen.getAllByText(/ChevronRightIcon/)).toHaveLength(1);
  });

  it('Given navigation icons are clicked, should call scrollFilter', async () => {
    render(<Filters {...defaultProps} />);
    const buttons = await screen.findAllByRole('button', { hidden: true });
    const leftButton = buttons[0];
    const rightButton = buttons[1];

    // Mock scrollTo on the filter container
    const scrollToMock = jest.fn();
    Object.defineProperty(HTMLElement.prototype, 'scrollTo', {
      value: scrollToMock,
      writable: true,
    });

    fireEvent.click(leftButton);
    fireEvent.click(rightButton);

    // ScrollTo should be called twice (left and right)
    expect(scrollToMock).toHaveBeenCalledTimes(2);
  });

  it('Given enableResetFilters and sortFilterByPosition, should pass them to FilterSelect', () => {
    render(<Filters {...defaultProps} />);
    expect(screen.getByTestId('filter-select').textContent).toContain(
      'filter1'
    );
  });

  it('Given scroll event, should call setIsFilterScrollable', () => {
    render(<Filters {...defaultProps} />);
    // Simulate scroll event
    const filterContainer = document.querySelector('.hiddenScrollbar');
    if (filterContainer) {
      fireEvent.scroll(filterContainer);
      expect(mockSetIsFilterScrollable).toHaveBeenCalled();
    }
  });
});
