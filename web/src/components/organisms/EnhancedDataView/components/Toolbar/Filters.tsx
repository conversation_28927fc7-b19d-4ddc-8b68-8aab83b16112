import { Box, IconButton } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { useEffect, useRef, useMemo } from 'react';

import type { DataSpec } from '../../types';
import { useEnhancedDataViewStore } from '../../store';
import { FilterQueryChips } from './FilterQueryChips';
import { FilterDateRange } from './FilterDateRange';
import { FilterNumberRange } from './FilterNumberRange';
import { FilterSelect } from './FilterSelect';

export const Filters = ({
  queryChips,
  table,
  dateFilters,
  filters,
  enableResetFilters,
  sortFilterByPosition,
  numberRangeFilters,
}: Pick<
  DataSpec,
  | 'queryChips'
  | 'table'
  | 'dateFilters'
  | 'filters'
  | 'sortFilterByPosition'
  | 'numberRangeFilters'
> & {
  enableResetFilters?: boolean;
}) => {
  const refFilterContainer = useRef<HTMLDivElement>(null);
  const refFilterLeftIcon = useRef<HTMLButtonElement>(null);
  const refFilterRightIcon = useRef<HTMLButtonElement>(null);

  const isFilterScrollable = useEnhancedDataViewStore(
    (s) => s.isFilterScrollable
  );
  const setIsFilterScrollable = useEnhancedDataViewStore(
    (s) => s.setIsFilterScrollable
  );

  const scrollFilter = (direction: 'left' | 'right') => {
    const distance = 400;
    refFilterContainer.current?.scrollTo({
      left:
        refFilterContainer.current.scrollLeft +
        (direction === 'right' ? distance : -distance),
      behavior: 'smooth',
    });
  };

  const validNumberRangeFilters = useMemo(() => {
    return (numberRangeFilters || []).filter((f) => !!f);
  }, [numberRangeFilters]);

  useEffect(() => {
    const elFilter = refFilterContainer.current;
    const elFilterLeftIcon = refFilterLeftIcon.current;
    const elFilterRightIcon = refFilterRightIcon.current;

    const onScroll = () => {
      if (
        elFilter === null ||
        elFilterLeftIcon === null ||
        elFilterRightIcon === null
      )
        return;

      const isScrollable = elFilter.scrollWidth > elFilter.clientWidth;

      // Reset
      elFilterLeftIcon.style.display = '';
      elFilterRightIcon.style.display = '';

      if (elFilter.scrollLeft === 0) {
        elFilterLeftIcon.style.display = 'none';
      }
      if (
        Math.ceil(elFilter.scrollLeft) >=
        elFilter.scrollWidth - elFilter.clientWidth
      ) {
        elFilterRightIcon.style.display = 'none';
      }
      setIsFilterScrollable(isScrollable);
    };

    if (elFilter) {
      elFilter.addEventListener('scroll', onScroll);
    }

    const observer = new MutationObserver(() => {
      onScroll();
    });

    if (elFilter) {
      observer.observe(elFilter, { subtree: true, childList: true });
    }

    return () => {
      if (elFilter) {
        elFilter.removeEventListener('scroll', onScroll);
        observer.disconnect();
      }
    };
  }, [setIsFilterScrollable]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexGrow: 1,
        overflow: 'hidden',
        position: 'relative',
        alignItems: 'center',
        alignSelf: 'stretch',
        '&:hover': {
          '& .navigate-icon': {
            display: isFilterScrollable ? 'inline-flex' : 'none',
          },
        },
      }}
    >
      {/* === navigate icon === */}
      <IconButton
        ref={refFilterLeftIcon}
        className="navigate-icon"
        onClick={() => {
          scrollFilter('left');
        }}
        sx={{
          display: 'none',
          position: 'absolute',
          left: 8,
          zIndex: 3,
          background: '#e3e3e3',
          boxShadow: '-6px 0px 7px -3px rgba(0, 0, 0, 0.1)',
          '&:hover': {
            background: '#e3e3e3',
          },
        }}
      >
        <ChevronLeftIcon />
      </IconButton>
      <Box
        ref={refFilterContainer}
        className="hiddenScrollbar"
        sx={{
          flexGrow: 1,
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          alignSelf: 'stretch',
          px: 2,
          overflow: 'auto',
          '&:hover': {
            '& .navigate-icon': {
              display: isFilterScrollable ? 'inline-flex' : 'none',
            },
          },
        }}
      >
        <FilterQueryChips queryChips={queryChips} />
        <FilterDateRange table={table} dateFilters={dateFilters} />
        <FilterSelect
          filters={filters}
          sortFilterByPosition={sortFilterByPosition}
          enableResetFilters={enableResetFilters}
        />
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mx: 1 }}>
          {validNumberRangeFilters.map((cfg) => (
            <FilterNumberRange
              key={`${cfg.startKey}-${cfg.endKey}`}
              startKey={cfg.startKey}
              endKey={cfg.endKey}
              startLabel={cfg.startLabel}
              endLabel={cfg.endLabel}
              min={cfg.min}
              max={cfg.max}
              step={cfg.step}
            />
          ))}
        </Box>

        {/* === navigate icon === */}
      </Box>
      <IconButton
        className="navigate-icon"
        onClick={() => {
          scrollFilter('right');
        }}
        ref={refFilterRightIcon}
        sx={{
          display: 'none',
          position: 'absolute',
          right: 8,
          zIndex: 3,
          background: '#e3e3e3',
          boxShadow: '-6px 0px 7px -3px rgba(0, 0, 0, 0.1)',
          '&:hover': {
            background: '#e3e3e3',
          },
        }}
      >
        <ChevronRightIcon />
      </IconButton>
    </Box>
  );
};
