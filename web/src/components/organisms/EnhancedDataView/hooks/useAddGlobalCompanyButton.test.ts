import { renderHook } from '@testing-library/react';
import { vi, type Mock } from 'vitest';

import { useAddGlobalCompanyButton } from './useAddGlobalCompanyButton';
import * as useParamsModule from './useParams';
import * as storeModule from '../store';

describe('useAddGlobalCompanyButton', () => {
  let setAddGlobalCompanyConfig: Mock;
  let setNewData: Mock;

  beforeEach(() => {
    setAddGlobalCompanyConfig = vi.fn();
    setNewData = vi.fn();

    vi.spyOn(storeModule, 'useEnhancedDataViewStore')
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      .mockImplementation((selector: any) =>
        selector({ setAddGlobalCompanyConfig })
      );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const mockSearchParams = (params: Record<string, string>) => {
    const searchParams = {
      get: (key: string) => params[key],
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;
    vi.spyOn(useParamsModule, 'useSearchParamsUrl')
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      .mockReturnValue({ searchParams } as any);
  };

  it('Given initial state, should reset state', () => {
    mockSearchParams({});
    renderHook(() =>
      useAddGlobalCompanyButton({
        queryChips: {},
        hideAdd: true,
        setNewData,
      })
    );
    expect(setAddGlobalCompanyConfig).toHaveBeenCalledWith({
      addBtnLabel: '',
      hideAddSelect: true,
      fields: undefined,
    });
  });

  it('Given qc param is missing, should do nothing', () => {
    mockSearchParams({ m: 'add' });
    renderHook(() =>
      useAddGlobalCompanyButton({
        queryChips: {},
        hideAdd: false,
        setNewData,
      })
    );
    // Only initial reset
    expect(setAddGlobalCompanyConfig).toHaveBeenCalledTimes(1);
  });

  it('Given chip is not found, should do nothing', () => {
    mockSearchParams({ qc: 'notfound', m: 'add' });
    renderHook(() =>
      useAddGlobalCompanyButton({
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        queryChips: { foo: { id: 'foo' } as any },
        hideAdd: false,
        setNewData,
      })
    );
    expect(setAddGlobalCompanyConfig).toHaveBeenCalledTimes(1);
  });

  it('Given chip has addBtnLabel and hideAddSelect, should set them', () => {
    mockSearchParams({ qc: 'chip1', m: 'edit' });
    const chip = {
      id: 'chip1',
      showAddBtn: true,
      addBtnLabel: 'Add Company',
    };
    renderHook(() =>
      useAddGlobalCompanyButton({
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        queryChips: { chip1: chip as any },
        hideAdd: false,
        setNewData,
      })
    );
    expect(setAddGlobalCompanyConfig).toHaveBeenCalledWith({
      hideAddSelect: false,
    });
    expect(setAddGlobalCompanyConfig).toHaveBeenCalledWith({
      addBtnLabel: 'Add Company',
    });
  });

  it('Given add mode, should set new data and fields', () => {
    mockSearchParams({ qc: 'chip2', m: 'add' });
    const chip = {
      id: 'chip2',
      showAddBtn: true,
      addFields: {
        name: { enabled: true, value: 'Test Name' },
        age: { enabled: false, value: 42 },
        created: { enabled: true, value: () => 'now' },
      },
    };
    renderHook(() =>
      useAddGlobalCompanyButton({
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        queryChips: { chip2: chip as any },
        hideAdd: false,
        setNewData,
      })
    );
    // setNewData should be called with only fields that have value
    expect(setNewData).toHaveBeenCalledWith({
      name: 'Test Name',
      age: 42,
      created: 'now',
    });
    // setAddGlobalCompanyConfig should be called with enabled fields
    expect(setAddGlobalCompanyConfig).toHaveBeenCalledWith({
      fields: [
        { enabled: true, value: 'Test Name', id: 'name' },
        { enabled: true, value: expect.any(Function), id: 'created' },
      ],
    });
  });

  it('Given edit mode, should set fields', () => {
    mockSearchParams({ qc: 'chip3', m: 'edit' });
    const chip = {
      id: 'chip3',
      editFields: {
        foo: { enabled: true, value: 1 },
        bar: { enabled: false, value: 2 },
      },
    };
    renderHook(() =>
      useAddGlobalCompanyButton({
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        queryChips: { chip3: chip as any },
        hideAdd: false,
        setNewData,
      })
    );
    expect(setAddGlobalCompanyConfig).toHaveBeenCalledWith({
      fields: [{ enabled: true, value: 1, id: 'foo' }],
    });
  });
});
