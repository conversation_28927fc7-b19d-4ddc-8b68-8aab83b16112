import { renderHook } from '@testing-library/react';
import { vi, type Mock } from 'vitest';

import { useBuildFilterList } from './useBuildFilterList';
import { FieldTypes } from '@/types';
import { useEnhancedDataViewStore } from '../store';

// Mock the store
vi.mock('../store', () => ({
  useEnhancedDataViewStore: vi.fn(),
}));

describe('useBuildFilterList', () => {
  const setFilterListMock = vi.fn();
  beforeEach(() => {
    vi.clearAllMocks();
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (useEnhancedDataViewStore as any as Mock).mockReturnValue(
      setFilterListMock
    );
  });

  it('Given filters and fieldOptions are undefined, should not call setFilterList', () => {
    renderHook(() =>
      useBuildFilterList({ filters: undefined, fieldOptions: undefined })
    );
    expect(setFilterListMock).not.toHaveBeenCalled();
  });

  it('Given filters with _date_start and _date_end, should build filter list', () => {
    const filters = {
      created_date_start: '2023-01-01',
      created_date_end: '2023-01-31',
    };
    renderHook(() => useBuildFilterList({ filters }));
    expect(setFilterListMock).toHaveBeenCalledWith([
      {
        label: 'Start date',
        type: FieldTypes.DATE,
        id: 'created_date_start',
        value: '2023-01-01',
      },
      {
        label: 'End date',
        type: FieldTypes.DATE,
        id: 'created_date_end',
        value: '2023-01-31',
      },
    ]);
  });

  it('Given filters with multi-select array, should build filter list', () => {
    const filters = {
      status: ['active', 'inactive'],
    };
    renderHook(() => useBuildFilterList({ filters }));
    expect(setFilterListMock).toHaveBeenCalledWith([
      {
        label: 'status',
        type: FieldTypes.MULTI_SELECT,
        options: ['active', 'inactive'],
        id: 'status',
      },
    ]);
  });

  it('Given filters is undefined, should build filter list from fieldOptions', () => {
    const fieldOptions = {
      type: ['foo', 'bar'],
      updated_date_start: '2022-01-01',
    };
    renderHook(() => useBuildFilterList({ filters: undefined, fieldOptions }));
    expect(setFilterListMock).toHaveBeenCalledWith([
      {
        label: 'type',
        type: FieldTypes.MULTI_SELECT,
        options: ['foo', 'bar'],
        id: 'type',
      },
      {
        label: 'Start date',
        type: FieldTypes.DATE,
        id: 'updated_date_start',
        value: '2022-01-01',
      },
    ]);
  });

  it('Given filters with empty arrays, should ignore them', () => {
    const filters = {
      status: [],
      category: ['A'],
    };
    renderHook(() => useBuildFilterList({ filters }));
    expect(setFilterListMock).toHaveBeenCalledWith([
      {
        label: 'category',
        type: FieldTypes.MULTI_SELECT,
        options: ['A'],
        id: 'category',
      },
    ]);
  });
});
