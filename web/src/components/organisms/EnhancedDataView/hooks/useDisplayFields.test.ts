import { renderHook } from '@testing-library/react';
import { vi, type Mock } from 'vitest';

import { useDisplayFields } from './useDisplayFields';
import { useEnhancedDataViewStore } from '../store';

// Mock the store
const setDisplayFieldsMock = vi.fn();
vi.mock('../store', () => ({
  useEnhancedDataViewStore: vi.fn(),
}));

describe('useDisplayFields', () => {
  const defaultValue = ['field1', 'field2'];
  const pathname = '/test-path';

  beforeEach(() => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (useEnhancedDataViewStore as any as Mock).mockReturnValue(
      setDisplayFieldsMock
    );

    // Mock global location
    Object.defineProperty(window, 'location', {
      value: { pathname },
      writable: true,
    });

    // Mock localStorage
    const localStorageMock = (() => {
      let store: Record<string, string> = {};
      return {
        getItem: vi.fn(),
        setItem: vi.fn((key, value) => {
          store[key] = value;
        }),
        removeItem: vi.fn((key) => {
          delete store[key];
        }),
        clear: vi.fn(() => {
          store = {};
        }),
      };
    })();
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('Given no saved fields in localStorage, should use defaultValue', () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (window.localStorage.getItem as any).mockReturnValue(null);

    renderHook(() => useDisplayFields({ defaultValue }));

    expect(setDisplayFieldsMock).toHaveBeenCalledWith(defaultValue);
  });

  it('Given saved fields in localStorage, should use them', () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (window.localStorage.getItem as any).mockReturnValue('foo,bar');

    renderHook(() => useDisplayFields({ defaultValue }));

    expect(setDisplayFieldsMock).toHaveBeenCalledWith(['foo', 'bar']);
  });

  it('Given localStorage returns empty string, should not use saved fields', () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (window.localStorage.getItem as any).mockReturnValue('');

    renderHook(() => useDisplayFields({ defaultValue }));

    expect(setDisplayFieldsMock).toHaveBeenCalledWith(defaultValue);
  });

  it('Given localStorage returns only commas, should not use saved fields', () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (window.localStorage.getItem as any).mockReturnValue(',');

    renderHook(() => useDisplayFields({ defaultValue }));

    expect(setDisplayFieldsMock).toHaveBeenCalledWith(defaultValue);
  });
});
