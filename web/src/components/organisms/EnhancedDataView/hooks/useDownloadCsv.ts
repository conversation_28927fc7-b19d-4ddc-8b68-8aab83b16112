import { useCallback, useState } from 'react';

import { auth } from '@/firebase';
import type { DataSpec, ExportOption } from '../types';
import { useSearchParamsUrl } from './useParams';
import useSnackbar from '@/contexts/useSnackbar';
import { exportToCsv } from '@/services/helpers';
import { useEnhancedDataViewStore } from '../store';
import { FieldTypes } from '@/types';

export const useDownloadCsv = ({ queryChips, table }: DataSpec) => {
  const { searchParams } = useSearchParamsUrl();
  const qcParam = searchParams.get('qc');
  const [isDownloading, setIsDownloading] = useState(false);
  const { showSnackbar } = useSnackbar();
  const filterList = useEnhancedDataViewStore((s) => s.filterList);
  const orderBy = searchParams.get('orderBy') || 'created_at';
  const order = searchParams.get('order') || 'desc';

  const appendChipQueryParams = (
    queryChips: DataSpec['queryChips'],
    qcParam: string | null,
    additionalQueryParams: URLSearchParams
  ): URLSearchParams => {
    if (queryChips && qcParam) {
      const chipQuery = queryChips[qcParam]?.query ?? {};
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.entries(chipQuery).forEach(([k, v]) => {
        if (Array.isArray(v)) {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          v.forEach((e) => {
            additionalQueryParams.append(k, e);
          });
        } else {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          additionalQueryParams.append(k, v as any);
        }
      });
    }
    return additionalQueryParams;
  };

  /**
   * Download CSV
   */

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const downloadCsvFn = useCallback(
    async (options: ExportOption['options']) => {
      const idToken = await auth.currentUser?.getIdToken(true);
      if (!idToken) {
        throw new Error('User is not authenticated');
      }

      let additionalQueryParams = new URLSearchParams(searchParams);
      additionalQueryParams = appendChipQueryParams(
        queryChips,
        qcParam,
        additionalQueryParams
      );
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const tempQuery: Record<string, any> = {};
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      filterList.forEach((f) => {
        const paramValueArr = searchParams.getAll(f.id);
        const paramValue = searchParams.get(f.id);
        if (
          f.type === FieldTypes.MULTI_SELECT &&
          f.options?.length !== paramValueArr.length
        ) {
          tempQuery[f.id] = paramValueArr;
        }

        if (f.type === FieldTypes.DATE && paramValue) {
          tempQuery[f.id] = new Date(
            encodeURIComponent(
              new Date(paramValue).toISOString().substring(0, 10)
            )
          );
        }
      });

      // Special case for ReconciliationView special casing reconciled status
      // TODO: These should be read directly from Reconciliations.js. And the reconciled statuses should be converted to enums.
      if (table === 'reconciliation_data') {
        tempQuery.reconciled =
          queryChips?.[qcParam ?? 'all']?.query?.reconciled;
      }

      if (options) {
        for (const key in options) {
          tempQuery[key] = options[key];
          additionalQueryParams.set(key, options[key]);
        }
      }

      if (!additionalQueryParams.has('orderBy')) {
        additionalQueryParams.set('orderBy', orderBy);
      }

      try {
        await exportToCsv(
          {
            sort: order,
            extraParams: additionalQueryParams,
            ...tempQuery,
          },
          { idToken, endpoint: table }
        );
      } catch {
        showSnackbar(
          'Export failed. Please contact support for more assistance.',
          'error'
        );
      }
    },
    [
      searchParams,
      queryChips,
      qcParam,
      filterList,
      table,
      orderBy,
      order,
      showSnackbar,
    ]
  );

  const handleDownload = useCallback(
    async (options: ExportOption['options']) => {
      setIsDownloading(true);
      await downloadCsvFn(options);
      setIsDownloading(false);
    },
    [downloadCsvFn]
  );

  return {
    downloadCsvFn,
    handleDownload,
    isDownloading,
  };
};
