import { renderHook } from '@testing-library/react';
import * as mui from '@mui/material';
import { vi } from 'vitest';

import { useIsMobile } from './useIsMobile';

vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material');
  return {
    ...actual,
    useMediaQuery: vi.fn(),
  };
});

describe('useIsMobile', () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('Given media query matches, should return isMobile as true', () => {
    vi.spyOn(mui, 'useMediaQuery').mockImplementation(() => true);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current.isMobile).toBe(true);
  });

  it('Given media query does not match, should return isMobile as false', () => {
    vi.spyOn(mui, 'useMediaQuery').mockImplementation(() => false);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current.isMobile).toBe(false);
  });

  it('Given useMediaQuery is called, should use correct query', () => {
    const spy = vi.spyOn(mui, 'useMediaQuery').mockImplementation(() => false);
    renderHook(() => useIsMobile());
    expect(spy).toHaveBeenCalledWith('(max-width:600px)');
  });
});
