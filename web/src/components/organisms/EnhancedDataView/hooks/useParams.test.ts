import { renderHook, act } from '@testing-library/react';
import { MemoryRouter, useSearchParams } from 'react-router-dom';
import { vi, type Mock } from 'vitest';

import { useSearchParamsUrl } from './useParams';

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: vi.fn(),
  };
});

describe('useSearchParamsUrl', () => {
  let mockSearchParams: URLSearchParams;
  let mockSetSearchParams: Mock;

  beforeEach(() => {
    mockSearchParams = new URLSearchParams({ foo: 'bar', keep: 'yes' });
    mockSetSearchParams = vi.fn((updater) => {
      if (typeof updater === 'function') {
        mockSearchParams = updater(mockSearchParams);
      } else {
        mockSearchParams = new URLSearchParams(updater);
      }
    });
    (useSearchParams as Mock).mockReturnValue([
      mockSearchParams,
      mockSetSearchParams,
    ]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Given searchParams and setSearchParams, should return them', () => {
    const { result } = renderHook(() => useSearchParamsUrl(), {
      wrapper: MemoryRouter,
    });
    expect(result.current.searchParams.get('foo')).toBe('bar');
    expect(typeof result.current.setSearchParams).toBe('function');
    expect(typeof result.current.updateSearchParams).toBe('function');
  });

  it('Given new params, should update searchParams', () => {
    const { result } = renderHook(() => useSearchParamsUrl(), {
      wrapper: MemoryRouter,
    });
    act(() => {
      result.current.updateSearchParams({ foo: 'baz', newKey: 'value' });
    });
    expect(mockSetSearchParams).toHaveBeenCalled();
    expect(mockSearchParams.get('foo')).toBe('baz');
    expect(mockSearchParams.get('newKey')).toBe('value');
    expect(mockSearchParams.get('keep')).toBe('yes');
  });

  it('Given null or undefined params, should delete them', () => {
    const { result } = renderHook(() => useSearchParamsUrl(), {
      wrapper: MemoryRouter,
    });
    act(() => {
      result.current.updateSearchParams({ foo: null, keep: undefined });
    });
    expect(mockSetSearchParams).toHaveBeenCalled();
    expect(mockSearchParams.has('foo')).toBe(false);
    expect(mockSearchParams.has('keep')).toBe(false);
  });

  it('Given other params, should keep them unchanged', () => {
    const { result } = renderHook(() => useSearchParamsUrl(), {
      wrapper: MemoryRouter,
    });
    act(() => {
      result.current.updateSearchParams({ newKey: '123' });
    });
    expect(mockSearchParams.get('foo')).toBe('bar');
    expect(mockSearchParams.get('keep')).toBe('yes');
    expect(mockSearchParams.get('newKey')).toBe('123');
  });

  it('Given setSearchParams is called directly, should update searchParams', () => {
    const { result } = renderHook(() => useSearchParamsUrl(), {
      wrapper: MemoryRouter,
    });
    act(() => {
      result.current.setSearchParams({ direct: 'call' });
    });
    expect(mockSetSearchParams).toHaveBeenCalledWith({ direct: 'call' });
  });
});
