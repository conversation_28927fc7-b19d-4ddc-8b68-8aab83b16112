import { renderHook, act } from '@testing-library/react';
import { useSearchParams } from 'react-router-dom';
import { vi, type Mock } from 'vitest';

import { useDeleteAndCopyActions } from './useDeleteAndCopyActions';

// Mock useCreateCopy
vi.mock('../DataForm/hooks/useCreateCopy', () => ({
  useCreateCopy: () => ({
    createCopy: vi.fn(({ data }) => ({ ...data, copied: true })),
  }),
}));

// Mock useSearchParams from react-router-dom
const setSearchParamMock = vi.fn();
vi.mock('react-router-dom', () => {
  return {
    useSearchParams: vi.fn(),
  };
});

describe('useDeleteAndCopyActions', () => {
  const setDefaultDataMock = vi.fn();
  const fields = [{ name: 'field1' }];
  const table = 'test_table';
  const baseActions = [{ id: 'base', label: 'Base', onClick: vi.fn() }];

  beforeEach(() => {
    vi.clearAllMocks();
    (useSearchParams as Mock).mockReturnValue([null, setSearchParamMock]);
  });

  it('Given no flags enabled, should return base actions', () => {
    const { result } = renderHook(() =>
      useDeleteAndCopyActions({
        actions: baseActions,
        setDefaultData: setDefaultDataMock,
        fields,
        table,
      })
    );
    expect(result.current.actions).toHaveLength(1);
    expect(result.current.actions[0].id).toBe('base');
  });

  it('Given create copy action is enabled, should add create copy action', () => {
    const { result } = renderHook(() =>
      useDeleteAndCopyActions({
        actions: baseActions,
        enableRowCreateCopyAction: true,
        setDefaultData: setDefaultDataMock,
        fields,
        table,
      })
    );
    const copyAction = result.current.actions.find(
      (a) => a.id === 'create_copy'
    );
    expect(copyAction).toBeDefined();
  });

  it('Given delete action is enabled, should add delete action', () => {
    const { result } = renderHook(() =>
      useDeleteAndCopyActions({
        actions: baseActions,
        enableRowDeleteAction: true,
        setDefaultData: setDefaultDataMock,
        fields,
        table,
      })
    );
    const deleteAction = result.current.actions.find((a) => a.id === 'delete');
    expect(deleteAction).toBeDefined();
  });

  it('Given create copy action is clicked, should call setDefaultData and setSearchParam', () => {
    const { result } = renderHook(() =>
      useDeleteAndCopyActions({
        actions: [],
        enableRowCreateCopyAction: true,
        setDefaultData: setDefaultDataMock,
        fields,
        table,
      })
    );
    const copyAction = result.current.actions.find(
      (a) => a.id === 'create_copy'
    );
    act(() => {
      copyAction.onClick({ id: 1, name: 'row1' });
    });
    expect(setDefaultDataMock).toHaveBeenCalledWith({
      id: 1,
      name: 'row1',
      copied: true,
    });
    expect(setSearchParamMock).toHaveBeenCalledWith({ m: 'add' });
  });

  it('Given delete action is clicked, should set deleteIds and showDelConfirm', () => {
    const { result } = renderHook(() =>
      useDeleteAndCopyActions({
        actions: [],
        enableRowDeleteAction: true,
        setDefaultData: setDefaultDataMock,
        fields,
        table,
      })
    );
    const deleteAction = result.current.actions.find((a) => a.id === 'delete');
    act(() => {
      deleteAction.onClick({ id: 42 });
    });
    expect(result.current.deleteIds).toEqual([42]);
    expect(result.current.showDelConfirm).toBe(true);
  });

  it('Given setShowDelConfirm and setDeleteIds are called, should update state correctly', () => {
    const { result } = renderHook(() =>
      useDeleteAndCopyActions({
        actions: [],
        enableRowDeleteAction: true,
        setDefaultData: setDefaultDataMock,
        fields,
        table,
      })
    );
    act(() => {
      result.current.setShowDelConfirm(false);
      result.current.setDeleteIds([99]);
    });
    expect(result.current.showDelConfirm).toBe(false);
    expect(result.current.deleteIds).toEqual([99]);
  });
});
