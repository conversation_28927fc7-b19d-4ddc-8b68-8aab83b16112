import { Download } from '@mui/icons-material';
import {
  Box,
  Button,
  Chip,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TablePagination,
  Tooltip,
  Typography,
} from '@mui/material';
import type { CellValueChangedEvent } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import CommonFormatter from 'common/Formatter';
import { isValidString, numberOrDefault } from 'common/helpers';
import cloneDeep from 'lodash-es/cloneDeep';
import isEqual from 'lodash-es/isEqual';
import uniq from 'lodash-es/uniq';
import flatten from 'lodash-es/flatten';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Navigate, useSearchParams } from 'react-router-dom';

import LoadingCircle from '@/components/atoms/LoadingCircle';
import BasicDatePicker from '@/components/molecules/BasicDatePicker';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { SearchBoxUrlQuery } from '@/components/molecules/SearchBox';
import {
  type ISearchSetting,
  SearchSettings,
} from '@/components/molecules/SearchSettings';
import CompGridBulkActions from '@/components/schedules/compGrids/CompGridViewerView/CompGridBulkActions';
import ConfirmNewRatesDialog from '@/components/schedules/compGrids/CompGridViewerView/ConfirmNewRatesDialog';
import type {
  CompGridRate,
  CriteriaUpdateData,
  NewRate,
} from '@/components/schedules/compGrids/CompGridViewerView/types';
import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import { useRoleStore } from '@/store';
import { type DateRange, DateRangesTypes, type Roles } from '@/types';
import {
  ArrayParam,
  BooleanParam,
  DateParam,
  NumberParam,
  StringParam,
  useQueryParams,
  withDefault,
} from 'use-query-params';
import CompGridExportDialog from './CompGridExportDialog';

const getGridLevelCols = (
  name: string,
  rate_fields: string[],
  _role: Roles | null,
  isEditMode: boolean,
  adding: boolean,
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  editedCells: any = {}
) => {
  return [
    {
      field: `carrier_rate::${name}`,
      headerName: 'Carrier rate',
      fileExport: true,
      type: 'numericColumn',
      cellDataType: 'number',
      width: 80,
      cellStartedEdit: (params) =>
        isEditMode || (params.node.rowPinned === 'top' && adding),
      editable: (params) =>
        isEditMode || (params.node.rowPinned === 'top' && adding),
      wrapHeaderText: true,
      hide: !rate_fields.includes('carrier_rate'),
      cellClassRules: {
        'ag-cell-custom-valid': (params) =>
          editedCells[`${params.data.id}-${params.colDef.field}`] === 'valid',
        'ag-cell-custom-invalid': (params) =>
          editedCells[`${params.data.id}-${params.colDef.field}`] === 'invalid',
      },
    },
    {
      field: `house_rate::${name}`,
      headerName: 'House rate',
      fileExport: true,
      type: 'numericColumn',
      width: 80,
      cellStartedEdit: (params) =>
        isEditMode || (params.node.rowPinned === 'top' && adding),
      editable: (params) =>
        isEditMode || (params.node.rowPinned === 'top' && adding),
      wrapHeaderText: true,
      hide: !rate_fields.includes('house_rate'),
      cellClassRules: {
        'ag-cell-custom-valid': (params) =>
          editedCells[`${params.data.id}-${params.colDef.field}`] === 'valid',
        'ag-cell-custom-invalid': (params) =>
          editedCells[`${params.data.id}-${params.colDef.field}`] === 'invalid',
      },
    },
    {
      field: `rate::${name}`,
      headerName: 'Total rate',
      fileExport: true,
      type: 'numericColumn',
      width: 80,
      cellStartedEdit: (params) =>
        isEditMode || (params.node.rowPinned === 'top' && adding),
      editable: (params) =>
        isEditMode || (params.node.rowPinned === 'top' && adding),
      wrapHeaderText: true,
      hide: !rate_fields.includes('total_rate'),
      cellClassRules: {
        'ag-cell-custom-valid': (params) =>
          editedCells[`${params.data.id}-${params.colDef.field}`] === 'valid',
        'ag-cell-custom-invalid': (params) =>
          editedCells[`${params.data.id}-${params.colDef.field}`] === 'invalid',
      },
    },
  ];
};

const URL_FILTERS = {
  compensation_type: 'compensation_types',
};

const CompGridsView = () => {
  const { data: accountSettings, isFetched: isFetchedAccountSettings } =
    API.getBasicQuery(`accounts/settings`);

  const [queryParams, setQueryParams] = useQueryParams({
    effective_date: DateParam,
    range_date: NumberParam,
    incl_date_ranges: withDefault(BooleanParam, false),
    q: StringParam,
    comp_grids: ArrayParam,
    levels: ArrayParam,
    [URL_FILTERS.compensation_type]: ArrayParam,
    page: withDefault(NumberParam, 0),
    limit: withDefault(NumberParam, 50),
  });

  const {
    effective_date,
    range_date,
    incl_date_ranges: showDateRanges,
    q: query,
    comp_grids,
    page,
    limit,
  } = queryParams;

  const viewSettings = accountSettings?.pages_settings?.comp_grids_schedules;
  let pageLabel = 'Comp grids';
  let readOnly = false;
  if (viewSettings?.page_label) {
    pageLabel = viewSettings?.page_label;
  }
  if (viewSettings?.read_only) {
    readOnly = viewSettings?.read_only;
  }

  const initialInputRow = {
    id: undefined,
    comp_grid: null,
    company: null,
    product_type: null,
    product: null,
    compensation_type: null,
    issue_age_start: null,
    issue_age_end: null,
    premium_min: null,
    premium_max: null,
    policy_year_start: null,
    policy_year_end: null,
    str_id: undefined,
  };
  const [inputRow, _setInputRow] = useState(initialInputRow);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [newRowNode, setNewRowNode] = useState<any>(null);
  const [adding, setAdding] = useState(false);
  const [dropdownValues, setDropdownValues] = useState<{
    comp_grid: number[];
    comp_grid_mapping: Record<number, string>;
    product: number[];
    product_mapping: Record<number, string>;
  }>({
    comp_grid: [],
    comp_grid_mapping: { 0: 'Click to add' },
    product: [],
    product_mapping: { 0: 'Click to add' },
  });
  const [newRowValues, setNewRowValues] = useState(null);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [compGrids, setCompGrids] = useState<any[]>([]);
  const [selectedCompGrid, setSelectedCompGrid] = useState<string | null>(null);
  const [searchParams] = useSearchParams({});
  const [filteredLevels, setFilteredLevels] = useState<string[]>([]);
  const [filteredCompTypes, setFilteredCompTypes] = useState<string[]>([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedCells, setEditedCells] = useState({});
  const [editedValues, setEditedValues] = useState({});
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [editNode, setEditNode] = useState<{ [key: string]: any[] }>({});
  const [loading, setLoading] = useState(false);
  const [addLoading, setAddLoading] = useState(false);
  const [openBulkActionsDialog, setOpenBulkActionsDialog] = useState(false);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [selectedDateRanges, setSelectedDateRanges] = useState<any>([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedLevels, setSelectedLevels] = useState<string[]>([]);
  const [selectedCompTypes, setSelectedCompTypes] = useState<string[]>([]);
  const [selectedRatesIds, setSelectedRatesIds] = useState<string[]>([]);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [selectedCriteria, setSelectedCriteria] = useState<any[]>([]);
  const [showConfirmDeleteDialog, setShowConfirmDeleteDialog] = useState(false);
  const [selectedDateFilter, setSelectedDateFilter] = useState<string>(
    effective_date ? 'singleDate' : range_date ? 'dateRange' : 'today'
  );
  const [effectiveDateFilterValue, setEffectiveDateFilterValue] =
    useState(effective_date);

  const [dateRangeFilterValue, setDateRangeFilterValue] = useState(range_date);
  const [confirmNewRates, setConfirmNewRates] = useState(false);
  const [openExportDialog, setOpenExportDialog] = useState(false);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [params, setParams] = useState<any>(null);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const gridRef = useRef<any>(null);

  const { userRole } = useRoleStore();
  const { showSnackbar } = useSnackbar();

  // Uncertain if we can share the queryString with the API
  const queryString = searchParams.toString();

  const {
    data: _compGrids,
    isLoading: isLoadingCompGrids,
    refetch: refetchCompGrids,
  } = API.getBasicQuery('comp-grids/view', queryString);
  const compGridFilters = _compGrids?.filters;
  const filteredRatesCompGrids = useMemo(() => {
    return _compGrids?.data.map((compGrid) => ({
      ...compGrid,
      comp_grid_criteria: compGrid.comp_grid_criteria.filter(
        (criteria) => criteria.comp_grid_rates.length > 0
      ),
    }));
  }, [_compGrids]);

  useEffect(() => {
    setCompGrids(filteredRatesCompGrids);
  }, [filteredRatesCompGrids]);

  const criteriaCount = _compGrids?.criteria_count;

  const selectedCompGrids = (comp_grids ?? []).flatMap((grid) =>
    grid == null ? [] : Number(grid)
  );

  const { data: dateRangesData } = API.getBasicQuery(
    'date-ranges',
    `type=${DateRangesTypes.ANY}${selectedCompGrids?.length && selectedCompGrids?.length !== compGrids?.length ? `&comp_grids=${selectedCompGrids?.join(',')}` : ''}`
  );
  const ratesCriteriaPatcher = API.getMutation('comp-grids/view', 'PATCH');
  const ratesBulkPatcher = API.getMutation(
    'comp-grids/rates/bulk-edit',
    'PATCH'
  );
  const ratesBulkDeleter = API.getMutation(
    'comp-grids/rates/bulk-edit',
    'DELETE'
  );
  const copyRatesPoster = API.getMutation(
    'comp-grids/rates/copy-rates',
    'POST'
  );
  const criteriaWithRatesPoster = API.getMutation(
    'comp-grids/criteria/criteria-with-rates',
    'POST'
  );

  const columnDefsBase = useMemo(() => {
    const nonNegativeValueSetter = (valueSetterArgs) => {
      // Ensure the input value is non-negative
      if (valueSetterArgs.newValue < 0) {
        showSnackbar('Cannot set a negative value.', 'error');
        return false;
      }

      if (
        valueSetterArgs.data[valueSetterArgs.colDef.field] !==
        valueSetterArgs.newValue
      ) {
        valueSetterArgs.data[valueSetterArgs.colDef.field] =
          valueSetterArgs.newValue;
        return true;
      }

      return false;
    };

    return [
      {
        checkboxSelection: isEditMode,
        hide: !isEditMode,
        width: 30,
        pinned: 'left' as const,
        cellStyle: {
          display: 'flex',
          alignItems: 'none',
          justifyContent: 'center',
          marginLeft: 12,
          border: 'none',
        },
      },
      { field: 'id', hide: true },
      {
        field: 'comp_grid',
        headerName: 'Comp grid',
        fileExport: true,
        pinned: 'left' as const,
        width: 140,
        hide: !adding && !isEditMode,
        editable: (params) => params.node.rowPinned === 'top' && adding,
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: dropdownValues.comp_grid,
        },
        refData: dropdownValues.comp_grid_mapping,
        cellStyle: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'left',
          marginLeft: 'inherit',
          border: 'none',
        },
        onCellValueChanged: (params) => {
          setSelectedCompGrid(params.newValue);
          setNewRowNode(params);
          // Refresh the cells to reflect comp_grid changes
          params.data.company = compGrids.find(
            (grid) => grid.id === params.newValue
          )?.company.company_name;
          params.data.company_id = compGrids.find(
            (grid) => grid.id === params.newValue
          )?.company.id;
          params.data.product = '';
          params.data.product_type = '';
          params.api.refreshCells({
            rowNodes: [params.node],
            force: true,
          });
        },
      },
      { field: 'company_id', hide: true },
      {
        field: 'product_type',
        headerName: 'Product type',
        fileExport: true,
        pinned: 'left' as const,
        width: 90,
        wrapHeaderText: true,
        cellStyle: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'left',
          marginLeft: 'inherit',
          border: 'none',
        },
      },
      {
        field: 'product',
        headerName: 'Product',
        fileExport: true,
        pinned: 'left' as const,
        width: 180,
        tooltipValueGetter: (p) =>
          `${p?.data?.comp_grid_product} -- Carrier products: ${p?.data?.company_products}`,
        editable: (params) => params.node.rowPinned === 'top' && adding,
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: dropdownValues.product,
        },
        refData: dropdownValues.product_mapping,
        cellStyle: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'left',
          marginLeft: 'inherit',
          border: 'none',
        },
        onCellValueChanged: (params) => {
          params.data.product_type = compGrids
            .find((grid) => grid.id === selectedCompGrid)
            ?.comp_grid_products.find(
              (product) => product.id === params.newValue
            )?.type;
          params.api.refreshCells({
            rowNodes: [params.node],
            force: true,
          });
        },
      },
      {
        field: 'company',
        headerName: 'Carrier',
        fileExport: true,
        pinned: 'left' as const,
        width: 140,
        tooltipValueGetter: (p) => p?.data?.company,
        cellStyle: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'left',
          marginLeft: 'inherit',
          border: 'none',
        },
      },
      {
        headerName: 'Policy year',
        fileExport: true,
        valueGetter: (params) => {
          return CommonFormatter.numberRange(
            params.data.policy_year_start,
            params.data.policy_year_end,
            { anyValue: '' }
          );
        },
        pinned: 'left' as const,
        width: 84,
        wrapHeaderText: true,
        hide: isEditMode || adding,
      },
      {
        field: 'policy_year_start',
        headerName: 'Policy year start',
        fileExport: true,
        pinned: 'left' as const,
        width: 70,
        wrapHeaderText: true,
        editable: (params) =>
          isEditMode || (params.node.rowPinned === 'top' && adding),
        type: 'numberColumn',
        valueSetter: nonNegativeValueSetter,
        hide: !isEditMode && !adding,
      },
      {
        field: 'policy_year_end',
        headerName: 'Policy year end',
        fileExport: true,
        pinned: 'left' as const,
        width: 70,
        wrapHeaderText: true,
        editable: (params) =>
          isEditMode || (params.node.rowPinned === 'top' && adding),
        type: 'numberColumn',
        valueSetter: nonNegativeValueSetter,
        hide: !isEditMode && !adding,
      },
      {
        headerName: 'Issue age',
        fileExport: true,
        valueGetter: (params) => {
          return CommonFormatter.numberRange(
            params.data.issue_age_start,
            params.data.issue_age_end,
            { anyValue: '' }
          );
        },
        pinned: 'left' as const,
        width: 80,
        wrapHeaderText: true,
        hide: isEditMode || adding,
      },
      {
        field: 'issue_age_start',
        headerName: 'Age start',
        fileExport: true,
        pinned: 'left' as const,
        width: 55,
        wrapHeaderText: true,
        editable: (params) =>
          isEditMode || (params.node.rowPinned === 'top' && adding),
        type: 'numberColumn',
        valueSetter: nonNegativeValueSetter,
        hide: !isEditMode && !adding,
      },
      {
        field: 'issue_age_end',
        headerName: 'Age end',
        fileExport: true,
        pinned: 'left' as const,
        width: 55,
        wrapHeaderText: true,
        editable: (params) =>
          isEditMode || (params.node.rowPinned === 'top' && adding),
        type: 'numberColumn',
        valueSetter: nonNegativeValueSetter,
        hide: !isEditMode && !adding,
      },
      {
        headerName: 'Premium',
        fileExport: true,
        valueGetter: (params) => {
          return CommonFormatter.numberRange(
            params.data.premium_start,
            params.data.premium_end,
            { anyValue: '' }
          );
        },
        pinned: 'left' as const,
        width: 100,
        wrapHeaderText: true,
        hide: isEditMode || adding,
      },
      {
        field: 'premium_min',
        headerName: 'Premium min',
        fileExport: true,
        pinned: 'left' as const,
        width: 55,
        wrapHeaderText: true,
        editable: (params) =>
          isEditMode || (params.node.rowPinned === 'top' && adding),
        type: 'numberColumn',
        hide: !isEditMode && !adding,
      },
      {
        field: 'premium_max',
        headerName: 'Premium max',
        fileExport: true,
        pinned: 'left' as const,
        width: 55,
        wrapHeaderText: true,
        editable: (params) =>
          isEditMode || (params.node.rowPinned === 'top' && adding),
        type: 'numberColumn',
        hide: !isEditMode && !adding,
      },
      {
        field: 'compensation_type',
        headerName: 'Compensation\ntype',
        fileExport: true,
        pinned: 'left' as const,
        width: 106,
        wrapHeaderText: true,
        tooltipValueGetter: (p) => p?.data?.compensation_type,
        editable: (params) =>
          isEditMode || (params.node.rowPinned === 'top' && adding),
      },
      {
        field: 'rates_date_ranges',
        headerName: 'Date range',
        fileExport: true,
        pinned: 'left' as const,
        width: 220,
        autoHeight: true,
        hide: !showDateRanges,
        cellRenderer: (params) => {
          const { value } = params;
          if (!value) return null;
          return (
            <Box display="flex" flexDirection="column">
              {value.map((dateRange, index) => (
                // biome-ignore lint/correctness/useJsxKeyInIterable: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                <Tooltip title={dateRange}>
                  <Chip
                    key={`${
                      // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      index
                    }-datechip`}
                    sx={{ m: 0.25 }}
                    label={dateRange}
                  />
                </Tooltip>
              ))}
            </Box>
          );
        },
      },
    ];
  }, [
    isEditMode,
    adding,
    dropdownValues.comp_grid,
    dropdownValues.comp_grid_mapping,
    dropdownValues.product,
    dropdownValues.product_mapping,
    showDateRanges,
    showSnackbar,
    compGrids,
    selectedCompGrid,
  ]);

  const searchSettings: ISearchSetting[] = [
    {
      id: 'incl_date_ranges',
      type: 'toggle',
      label: 'Show date ranges',
    },
  ];

  useEffect(() => {
    if (selectedDateFilter === 'singleDate') {
      setDateRangeFilterValue(undefined);
      setQueryParams({ range_date: null });
    } else if (selectedDateFilter === 'dateRange') {
      setEffectiveDateFilterValue(undefined);
      setQueryParams({
        range_date: dateRangeFilterValue,
        effective_date: null,
      });
    } else {
      setQueryParams({ range_date: null, effective_date: null });
      setEffectiveDateFilterValue(undefined);
      setDateRangeFilterValue(undefined);
    }

    setQueryParams({ effective_date: effectiveDateFilterValue });
  }, [
    selectedDateFilter,
    effectiveDateFilterValue,
    dateRangeFilterValue,
    setQueryParams,
  ]);

  const filteredCompGrids = useMemo(
    () => (Array.isArray(compGrids) ? compGrids : []),
    [compGrids]
  );

  const compGridLevels = useMemo(() => {
    const uniqueLevels = [
      ...new Set(
        filteredCompGrids.flatMap((compGrid) => compGrid?.comp_grid_levels)
      ),
    ];

    // Add comp grid available rate fields to each level for showing/hiding rate columns
    return uniqueLevels.map((level) => {
      const correspondingCompGrid = filteredCompGrids.find((compGrid) =>
        compGrid?.comp_grid_levels.includes(level)
      );

      return {
        ...level,
        rate_fields: correspondingCompGrid?.rate_fields || [
          'carrier_rate',
          'house_rate',
          'total_rate',
        ],
      };
    });
  }, [filteredCompGrids]);

  // The following assumes that all levels have the same children
  const levelColsMap = useMemo(
    () =>
      new Map(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        compGridLevels?.map((compGridLevel: any) => [
          compGridLevel.name,
          {
            headerName: compGridLevel.name,
            headerClass: 'custom-cell-border',
            children: getGridLevelCols(
              compGridLevel.name,
              compGridLevel.rate_fields,
              userRole,
              isEditMode,
              adding,
              editedCells
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            ).map((col: any, index: number) => ({
              ...col,
              headerClass: 'custom-cell-border',
              cellClass: [index === 0 ? 'first-child-border' : '']
                .join(' ')
                .trim(),
            })),
          },
        ])
      ),
    [compGridLevels, isEditMode, editedCells, adding, userRole]
  );

  const levelCols = useMemo(
    () =>
      Array.from(levelColsMap.values()).filter((v) =>
        filteredLevels.includes(v.headerName)
      ),
    [levelColsMap, filteredLevels]
  );

  const levelNames = useMemo(
    () => Array.from(levelColsMap.keys()),
    [levelColsMap]
  );

  const compensationTypes = useMemo(() => {
    if (compGridFilters) {
      return uniq(
        flatten(compGridFilters.map((grid) => grid?.compensationTypes)).filter(
          isValidString
        ) as string[]
      );
    }

    return [];
  }, [compGridFilters]);

  useEffect(() => {
    if (Array.isArray(compGridLevels) && filteredLevels?.length === 0) {
      setFilteredLevels(levelNames);
    }

    const compGridFilteredValues = searchParams
      .getAll('comp_grids')
      .map(Number);
    if (Array.isArray(compGrids))
      if (
        compGridFilteredValues.length > 0 &&
        !isEqual(compGridFilteredValues, selectedCompGrids)
      ) {
        setQueryParams({
          comp_grids: compGridFilteredValues.map((value) => value.toString()),
        });
      }

    const levelFilteredValues = searchParams.getAll('levels');
    if (Array.isArray(compGridLevels)) {
      if (
        levelFilteredValues.length > 0 &&
        !isEqual(levelFilteredValues, filteredLevels)
      ) {
        setFilteredLevels(levelFilteredValues);
      }
    }
  }, [
    compGrids,
    compGridLevels,
    selectedCompGrids,
    searchParams,
    filteredLevels,
    levelNames,
    setQueryParams,
  ]);

  useEffect(() => {
    if (compGrids) {
      const uniqueCompGridsIds = [
        ...new Set<number>(compGrids.map((grid) => grid.id)),
      ];

      const uniqueCompGridsMapping = compGrids.reduce(
        (acc, grid) => {
          acc[grid.id] = grid.name;
          return acc;
        },
        { 0: 'Click to add' } as Record<number, string>
      );

      const uniqueProductsMapping = compGrids.reduce(
        (acc, grid) => {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          grid.comp_grid_products.forEach((product) => {
            acc[product.id] = product.name;
          });
          return acc;
        },
        { 0: 'Click to add' } as Record<number, string>
      );
      setDropdownValues((prevValues) => ({
        ...prevValues,
        comp_grid: uniqueCompGridsIds,
        comp_grid_mapping: uniqueCompGridsMapping,
        product_mapping: uniqueProductsMapping,
      }));
    }
  }, [compGrids]);

  useEffect(() => {
    // When adding a new row, set the carrier and products dropdowns to the selected comp grid data
    if (selectedCompGrid) {
      const filteredGrids = compGrids.filter(
        (grid) => grid.id === selectedCompGrid
      );

      const uniqueProducts = [
        ...new Set<number>(
          filteredGrids.flatMap((grid) =>
            grid.comp_grid_products.map((product) => product.id)
          )
        ),
      ];

      setDropdownValues((prevValues) => ({
        ...prevValues,
        product: uniqueProducts,
      }));
    }
  }, [selectedCompGrid, compGrids]);

  useEffect(() => {
    if (compensationTypes.length > 0 && filteredCompTypes.length === 0) {
      setFilteredCompTypes(compensationTypes);
    }
  }, [filteredCompTypes, compensationTypes]);

  const columnDefs = [...columnDefsBase, ...levelCols];

  const rowList = filteredCompGrids
    .flatMap((compGrid) => compGrid.comp_grid_criteria)
    ?.filter((datum) => {
      if (!query) return true;
      const searchFields = [
        datum.comp_grid_product.name,
        datum.comp_grid_product.type,
        datum.comp_grid_product.company_products
          ?.map((cp) => cp.product_name)
          .join(' '),
        datum.company?.company_name,
        datum.compensation_type,
      ];
      return searchFields.join(' ').toLowerCase().includes(query.toLowerCase());
    });

  const filteredData = rowList?.map((criterion) => {
    const datum = {
      id: criterion.id,
      comp_grid: filteredCompGrids
        .filter((compGrid) =>
          compGrid.comp_grid_criteria.some(
            (criteria) => criteria.id === criterion.id
          )
        )
        .map((compGrid) => compGrid.id)[0],
      company: criterion.company?.company_name,
      company_id: criterion.company?.id,
      product_type: criterion.comp_grid_product?.type,
      product: criterion.comp_grid_product?.id,
      comp_grid_product: criterion.comp_grid_product?.name,
      company_products: criterion.comp_grid_product?.company_products
        ?.map((cp) => cp.product_name)
        .join(', '),
      compensation_type: criterion.compensation_type,
      issue_age_start: numberOrDefault(criterion.issue_age_start, null),
      issue_age_end: numberOrDefault(criterion.issue_age_end, null),
      premium_min: numberOrDefault(criterion.premium_min, null),
      premium_max: numberOrDefault(criterion.premium_max, null),
      policy_year_start: numberOrDefault(criterion.policy_year_start, null),
      policy_year_end: numberOrDefault(criterion.policy_year_end, null),
      str_id: criterion.str_id,
      rates_date_ranges: [] as string[],
    };
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    criterion.comp_grid_rates?.forEach((rate) => {
      const compGridLevel = rate.comp_grid_level.name;
      datum[`carrier_rate::${compGridLevel}`] = Number(rate.carrier_rate);
      datum[`house_rate::${compGridLevel}`] = Number(rate.house_rate);
      datum[`rate::${compGridLevel}`] = Number(rate.rate);
      datum[`rate_id::${compGridLevel}`] = rate.id;
    });

    if (showDateRanges) {
      const dateLevelMap: Record<string, string[]> =
        criterion.comp_grid_rates.reduce((acc, cur) => {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          cur.date_ranges?.forEach((dateRange: DateRange) => {
            const dateStr = CommonFormatter.dateRange(
              dateRange.start_date,
              dateRange.end_date
            );
            acc[dateStr] = [...(acc[dateStr] || []), cur.comp_grid_level.name];
          });
          return acc;
        }, {});
      const numLevelsInCriteria = criterion.comp_grid_rates.reduce(
        (acc, cur) => acc.add(cur.comp_grid_level.name),
        new Set<string>()
      ).size;
      datum.rates_date_ranges = Object.entries(dateLevelMap).map(
        ([dateRange, levels]) => {
          return levels.length === numLevelsInCriteria
            ? dateRange
            : `${levels.join(', ')}: ${dateRange}`;
        }
      );
    }
    return datum;
  });

  // Merge filteredData and editedValues to show edited values
  useEffect(() => {
    const editKeys = Object.keys(editedValues);
    if (filteredData?.length && editKeys.length) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      editKeys.forEach((key) => {
        const [rowIndex, _key] = key.split(';;');
        const node = filteredData.find(
          (row) => row.id === parseInt(rowIndex, 10)
        );
        if (node) {
          node[_key] = editedValues[key];
        }
      });
    }
    gridRef.current?.api?.refreshCells();

    if (filteredData) {
      const allColumnIds: string[] = [];
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      gridRef.current?.api?.getColumns().forEach((column) => {
        const columnId = column.getId();
        if (columnId?.includes('rate::')) {
          allColumnIds.push(columnId);
        }
      });
      gridRef.current?.api?.autoSizeColumns(allColumnIds, false);
    }
  }, [editedValues, filteredData]);

  // TODO: Move this implementation to the backend
  const onExportCsv = async () => {
    const _isEditMode = isEditMode;
    if (!isEditMode) setIsEditMode(true);

    const allColumns = gridRef.current?.api?.getColumns();
    const exportableColumnIds = allColumns
      ?.filter((column) => {
        return column.userProvidedColDef.fileExport;
      })
      .map((column) => column.getColId());

    // Wait for the grid to be in edit mode before exporting
    // This is a temporary workaround until the export is moved to the backend
    // So accepting a hacky temporary solution here.
    await new Promise((resolve) => {
      setTimeout(() => {
        gridRef.current?.api.exportDataAsCsv({
          fileName: 'Fintary-Comp-Grid-Export.csv',
          columnKeys: exportableColumnIds,
        });
        if (!_isEditMode) setIsEditMode(false);
        resolve(null);
      }, 1000);
    });
  };

  const onCellValueChanged = useCallback(
    (params: CellValueChangedEvent) => {
      const { column, data, newValue } = params;

      if (params.rowPinned === 'top') setNewRowValues(data);

      if (newValue !== params.oldValue) {
        const _key = column.getId();
        const isValid = !Number.isNaN(newValue);
        setEditedCells((prev) => ({
          ...prev,
          [`${data.id};;${_key}`]: isValid ? 'valid' : 'invalid',
        }));
        setEditedValues((prev) => ({
          ...prev,
          [`${data.id};;${_key}`]: newValue,
        }));

        const rowNode = rowList.find((row) => row.id === data.id);

        const [_fieldKey, _levelName] = _key.split('::');

        if (!rowNode) {
          return;
        }
        const rowNodeRates = rowNode.comp_grid_rates.filter((r) => {
          return r.comp_grid_level.name === _levelName;
        });

        let targetNodes = editNode[rowNode.str_id];

        if (!targetNodes) {
          const newNodes = cloneDeep(editNode);
          newNodes[rowNode.str_id] = rowNodeRates.flat();
          setEditNode(newNodes);
          targetNodes = newNodes[rowNode.str_id];
        }

        const copyNodes = cloneDeep(targetNodes);
        const nodeIndex = copyNodes.findIndex((r) => {
          return r.comp_grid_level?.name === _levelName;
        });

        if (nodeIndex > -1) {
          if (rowNodeRates.length > 0) {
            copyNodes[nodeIndex] = rowNodeRates;
          }
        } else {
          copyNodes.push({
            rowNode,
            key: _key,
            newValue: newValue,
          });
        }

        const newNodes = cloneDeep(editNode);
        newNodes[rowNode.str_id] = copyNodes.flat();

        setEditNode(newNodes);
      }
    },
    [editNode, rowList]
  );

  const handleNewRatesConfirm = async () => {
    if (params) {
      saveData(params);
    }
  };

  const onSave = async () => {
    if (!isEditMode) {
      setIsEditMode(true);
      setCompGrids(_compGrids?.data);
      return;
    }

    if (adding) {
      onCreate();
    }

    setCompGrids(filteredRatesCompGrids);

    gridRef.current.api.stopEditing();
    // Prepare data to save
    const changeObj = {};
    const _editedValues = cloneDeep(editedValues);
    let _editNode = cloneDeep(editNode);
    // Get the focused cell
    const focusedCell = gridRef.current.api.getFocusedCell();
    if (focusedCell) {
      const { rowIndex, column } = focusedCell;
      const {
        colDef: { field },
      } = column;
      const key = `${rowIndex};;${field}`;
      // Get the cell node
      const node = gridRef.current.api.getDisplayedRowAtIndex(rowIndex);
      if (node && _editedValues[key] === undefined) {
        _editedValues[key] = node.data[field];
        // Update editedNodes
        const [_fieldKey, _levelName] = field.split('::');
        const rowNode = rowList.find((row) => row.id === node.data.id);
        const rowNodeRates = rowNode.comp_grid_rates.filter((r) => {
          return r.comp_grid_level.name === _levelName;
        });
        const targetNodes = editNode[rowNode.str_id];

        if (targetNodes) {
          const copyNodes = cloneDeep(targetNodes);
          const nodeIndex = copyNodes.findIndex((r) => {
            return r.comp_grid_level?.name === _levelName;
          });
          if (nodeIndex > -1) {
            copyNodes[nodeIndex] = rowNodeRates;
          } else {
            copyNodes.push(...rowNodeRates);
          }

          const newNodes = cloneDeep(editNode);
          newNodes[rowNode.str_id] = copyNodes.flat();

          _editNode = newNodes;
        } else {
          const newNodes = cloneDeep(editNode);
          newNodes[rowNode.str_id] = rowNodeRates.flat();
          _editNode = newNodes;
        }
      }
    }

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.keys(_editedValues).forEach((key) => {
      const rowIndex = key.split(';;')[0];
      let _key = key.split(';;')[1];

      if (!_key.includes('::')) {
        _key += '::criteria';
      }

      const [field, level] = _key.split('::');
      const node = filteredData.find(
        (row) => row.id === parseInt(rowIndex, 10)
      );
      if (node) {
        if (!changeObj[node.str_id]) {
          changeObj[node.str_id] = {
            [level]: {
              [field]: _editedValues[key],
            },
          };
        } else {
          if (!changeObj[node.str_id][level]) {
            changeObj[node.str_id][level] = {
              [field]: _editedValues[key],
            };
          } else {
            changeObj[node.str_id][level][field] = _editedValues[key];
          }
        }
      }
    });

    // Find rates by str_id
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const changeEntries = Object.entries(changeObj) as [string, any];
    const changedRates: CompGridRate[] = [];
    const changedCriteria: CriteriaUpdateData[] = [];
    const newRates: NewRate[] = [];

    for (const [strId, item] of changeEntries) {
      const rowItems = _editNode[strId];
      if (!rowItems) {
        continue;
      }
      const tempCriteriaItem = item.criteria;
      if (tempCriteriaItem) {
        const criteria = {
          str_id: strId,
          ...tempCriteriaItem,
        };
        changedCriteria.push(criteria);
      }

      for (let rate of rowItems) {
        const rateLevelKey = rate.comp_grid_level?.name;
        const tempPreItem = item[rateLevelKey];

        if (tempPreItem) {
          rate = {
            ...rate,
            ...tempPreItem,
          };
          changedRates.push(rate);
        } else {
          // New rate
          const [, level] = rate.key.split('::'); // TODO: We need to refactor how the columns keys are created to avoid this

          const newRate = {
            item: item[level],
            level_name: level,
            rate_value: rate.newValue,
            comp_grid_id: rate.rowNode.comp_grid_id,
            comp_grid_criterion_id: rate.rowNode.id,
          };
          newRates.push(newRate);
        }
      }
    }
    const uniqueNewRates = newRates.filter(
      (rate, index, self) =>
        index ===
        self.findIndex(
          (r) =>
            r.level_name === rate.level_name &&
            r.comp_grid_id === rate.comp_grid_id
        )
    );
    const newRatesParams = uniqueNewRates.map((rate) => {
      return {
        rates: rate.item,
        level_name: rate.level_name,
        comp_grid_id: rate.comp_grid_id,
        comp_grid_criterion_id: rate.comp_grid_criterion_id,
      };
    });

    const ratesParams = changedRates.map((rate) => {
      return {
        id: rate.id,
        carrier_rate: rate.carrier_rate,
        house_rate: rate.house_rate,
        rate: rate.rate,
        comp_grid_level_id: rate.comp_grid_level_id,
        comp_grid_criterion_id: rate.comp_grid_criterion_id,
        date_ranges: rate.date_ranges,
      };
    });

    const dataParams = {
      rates: ratesParams,
      criteria: changedCriteria,
      new_rates: newRatesParams,
    };

    if (newRatesParams.length > 0) {
      setParams(dataParams);
      setConfirmNewRates(true);
      return;
    }

    if (!dataParams.rates.length && !dataParams.criteria.length) {
      setIsEditMode(false);
      return;
    }

    saveData(dataParams);
  };

  const saveData = async (params) => {
    setLoading(true);
    try {
      await ratesCriteriaPatcher.mutateAsync(params);
    } catch (e) {
      console.error(e);
      setLoading(false);
    }
    setLoading(false);
    setConfirmNewRates(false);
    // Toggle editable
    clear();
    // Refetch data
    refetchCompGrids();
  };

  const handleDateRangeChange = (index, newDateRange) => {
    const updatedRates = [...params.new_rates];
    updatedRates[index].date_range = newDateRange;
    setParams({ ...params, new_rates: updatedRates });
  };

  const clearNewRow = () => {
    if (newRowNode) {
      // Clean up the new row
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.keys(initialInputRow).forEach((key) => {
        newRowNode.data[key] = initialInputRow[key];
      });
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.keys(newRowNode.data).forEach((key) => {
        if (!(key in initialInputRow)) {
          newRowNode.data[key] = null;
        }
      });

      newRowNode.api.refreshCells({
        rowNodes: [newRowNode.node],
        force: true,
      });
    }
  };

  const clear = () => {
    setCompGrids(filteredRatesCompGrids);
    setEditedCells({});
    setEditedValues({});
    setIsEditMode(false);
    setEditNode({});
    setAdding(false);
    clearNewRow();
  };

  const handleRowSelected = (event) => {
    const selectedNodes = event.api.getSelectedNodes();
    const selectedData = selectedNodes.map((node) => node.data);
    setSelectedRows(selectedData);
  };

  useEffect(() => {
    if (selectedRows.length > 0) {
      const levelsSet = new Set<string>();
      const ratesIdsSet = new Set<string>();
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const _selectedCriteria: any = [];
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      selectedRows.forEach((row: any, index) => {
        _selectedCriteria[index] = {
          comp_grid_product: { type: row.product_type, name: row.product },
          policy_year_start: row.policy_year_start,
          policy_year_end: row.policy_year_end,
          issue_age_start: row.issue_age_start,
          issue_age_end: row.issue_age_end,
          compensation_type: row.compensation_type,
        };
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.keys(row).forEach((key) => {
          const [field, level] = key.split('::');
          if (level) {
            levelsSet.add(level);
          }
          if (field === 'rate_id') {
            ratesIdsSet.add(row[key]);
          }
        });
      });
      setSelectedLevels(Array.from(levelsSet));
      setSelectedRatesIds(Array.from(ratesIdsSet));
      setSelectedCriteria(_selectedCriteria);
    }
  }, [selectedRows]);

  const bulkEditRates = async () => {
    setLoading(true);
    try {
      const params = {
        ids: selectedRatesIds,
        date_ranges: selectedDateRanges.date_ranges,
      };
      const res = await ratesBulkPatcher.mutateAsync(params);
      if (res.error) {
        showSnackbar(
          `An error occurred when updating date range: ${res.error}`,
          'error'
        );
      } else {
        showSnackbar('Rates updated.', 'success');
      }
    } catch (e) {
      console.error(e);
      setLoading(false);
    }
    setSelectedDateRanges([]);
    setLoading(false);
    clear();
    refetchCompGrids();
    setOpenBulkActionsDialog(false);
  };

  const bulkDeleteRates = async () => {
    setLoading(true);
    try {
      const params = {
        ids: selectedRatesIds,
      };
      const res = await ratesBulkDeleter.mutateAsync(params);
      if (res.error) {
        showSnackbar(
          `An error occurred when deleting rates: ${res.error}`,
          'error'
        );
      } else {
        showSnackbar('Rates deleted.', 'success');
      }
    } catch (e) {
      console.error(e);
      setLoading(false);
    }
    setSelectedDateRanges([]);
    setLoading(false);
    clear();
    refetchCompGrids();
    setShowConfirmDeleteDialog(false);
  };

  const bulkCopyRates = async () => {
    setLoading(true);
    try {
      const params = {
        ids: selectedRatesIds,
        date_ranges: selectedDateRanges.date_ranges,
      };
      const res = await copyRatesPoster.mutateAsync(params);
      if (res.errors) {
        showSnackbar(
          `An error occurred when updating date range: ${res.errors[0].error}`,
          'error'
        );
      } else {
        showSnackbar('Rates copied.', 'success');
      }
    } catch (e) {
      console.error(e);
      setLoading(false);
    }
    setSelectedDateRanges([]);
    setLoading(false);
    clear();
    refetchCompGrids();
    setOpenBulkActionsDialog(false);
  };

  const onCreate = async () => {
    setAddLoading(true);

    if (newRowValues) {
      const response = await criteriaWithRatesPoster.mutateAsync(newRowValues);
      if (response.data) {
        showSnackbar('New row added successfully', 'success');

        clearNewRow();

        refetchCompGrids();
      } else {
        showSnackbar('An error occurred while adding new row', 'error');
      }
    }
    setAdding(false);
    setAddLoading(false);
  };

  // Function to provide a unique ID for each row, this allows to mantain selected rows on re-render
  const getRowId = useMemo(() => (params) => String(params.data.id), []);

  if (isFetchedAccountSettings && viewSettings?.show_page === false) {
    return (
      // TODO: Remove navigate after figuring out how to handle this in router
      <Navigate to="/settings" />
    );
  }

  // Custom styles for add new top row
  const getRowHeight = (params) => {
    if (params.node.rowPinned === 'top') {
      return 55;
    }
    return 40;
  };

  const handleCompTypeSelection = (selectedTypes: string[]) => {
    setSelectedCompTypes(selectedTypes);

    setQueryParams({
      [URL_FILTERS.compensation_type]:
        selectedTypes.length > 0 ? selectedTypes : undefined,
    });
  };

  const compGridOptions = Array.isArray(compGridFilters)
    ? compGridFilters.map((compGrid) => ({
        id: compGrid.id,
        name: compGrid.name,
      }))
    : [];
  if (!isFetchedAccountSettings) return <LoadingCircle />;

  const compTypeSelectOptions =
    compensationTypes.length > 0 ? compensationTypes : filteredCompTypes;

  const compTypeSelectValues =
    selectedCompTypes.length > 0 ? selectedCompTypes : compTypeSelectOptions;

  return (
    <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
      <style>
        {`
          .ag-cell-custom-valid {
            background-color: #eaf5fd !important;
            font-weight: bold;
          }
          .ag-cell-custom-invalid {
            border-color: #f60 !important;
            font-weight: bold;
          }
        `}
      </style>
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="h5" sx={{ whiteSpace: 'nowrap' }}>
            {pageLabel}
          </Typography>
          <Box sx={{ display: 'flex', mx: 1, gap: 1 }}>
            <EnhancedSelect
              enableSearch
              label="Comp grids"
              sx={{ width: 'auto' }}
              options={compGridOptions}
              value={
                !selectedCompGrids || selectedCompGrids?.length === 0
                  ? compGridOptions
                  : compGridOptions.filter((item) =>
                      selectedCompGrids?.includes(item.id)
                    )
              }
              multiple
              onChange={(v) => {
                const ids: number[] = v?.map((item) => item.id) || [];
                if (
                  Array.isArray(compGridOptions) &&
                  compGridOptions.length !== ids.length
                ) {
                  setQueryParams({
                    comp_grids: ids.map((idValue) => idValue.toString()),
                  });
                }
              }}
            />

            <EnhancedSelect
              enableSearch
              multiple
              label="Levels"
              options={levelNames}
              value={filteredLevels.filter((item) =>
                Object.values(levelNames).includes(item)
              )}
              onChange={(values) => {
                setFilteredLevels(values);
                if (
                  Array.isArray(levelNames) &&
                  levelNames.length !== values.length
                ) {
                  setQueryParams({
                    levels: values,
                  });
                }
              }}
            />

            <EnhancedSelect
              enableSearch
              multiple
              label="Compensation types"
              options={compTypeSelectOptions}
              value={compTypeSelectValues}
              onChange={handleCompTypeSelection}
            />

            <FormControl sx={{ minWidth: 120 }}>
              <InputLabel>Date</InputLabel>
              <Select
                value={selectedDateFilter}
                label="Date"
                onChange={(e) => setSelectedDateFilter(e.target.value)}
                sx={{
                  '.MuiSelect-select': {
                    py: 0.75,
                    px: 1.5,
                    minWidth: 120,
                  },
                }}
              >
                <MenuItem value="today">Today</MenuItem>
                <MenuItem value="singleDate">Date</MenuItem>
                <MenuItem value="dateRange">Date range</MenuItem>
              </Select>
            </FormControl>
            {selectedDateFilter === 'singleDate' && (
              <BasicDatePicker
                label="Effective date"
                value={effectiveDateFilterValue}
                setValue={(e) => setEffectiveDateFilterValue(e)}
                sx={{ ml: 1, minWidth: 152 }}
              />
            )}
            {selectedDateFilter === 'dateRange' && (
              <FormControl sx={{ ml: 1, mr: 1, minWidth: 148 }}>
                <InputLabel sx={{ lineHeight: 1 }}>Date range</InputLabel>
                <Select
                  value={dateRangeFilterValue ?? ''}
                  label="Date range"
                  onChange={(e) => {
                    setDateRangeFilterValue(parseInt(String(e.target.value)));
                  }}
                  sx={{
                    '.MuiSelect-select': { py: 0.75, px: 1.5 },
                  }}
                >
                  {dateRangesData?.map((dateRange: DateRange) => (
                    <MenuItem key={dateRange.id} value={dateRange.id}>
                      {dateRange.name
                        ? `${dateRange.name}: ${CommonFormatter.dateRange(dateRange.start_date, dateRange.end_date)}`
                        : `${CommonFormatter.dateRange(dateRange.start_date, dateRange.end_date)}`}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          </Box>
          <Box>
            <Box sx={{ display: 'flex' }}>
              <SearchBoxUrlQuery id="comp_grids" />
              <SearchSettings settings={searchSettings} />
              {!readOnly && (
                <>
                  {isEditMode && (
                    <>
                      <Button sx={{ ml: 1 }} onClick={clear}>
                        Cancel
                      </Button>
                      <Tooltip title="Select rows to enable bulk actions">
                        <span>
                          <Button
                            sx={{ ml: 1 }}
                            variant="outlined"
                            disabled={selectedRows.length === 0}
                            onClick={() => setOpenBulkActionsDialog(true)}
                          >
                            Bulk actions
                          </Button>
                        </span>
                      </Tooltip>
                      <Button
                        variant="contained"
                        sx={{ ml: 1 }}
                        onClick={() => {
                          if (!adding) {
                            setAdding(true);
                            return;
                          }
                        }}
                        loading={addLoading}
                        disabled={adding}
                      >
                        Add row
                      </Button>
                    </>
                  )}
                  <Button
                    variant="contained"
                    sx={{ ml: 1 }}
                    onClick={onSave}
                    loading={loading}
                  >
                    {isEditMode ? 'Save' : 'Edit'}
                  </Button>

                  <Button
                    startIcon={<Download />}
                    variant="outlined"
                    sx={{ ml: 1 }}
                    onClick={() => setOpenExportDialog(true)}
                  >
                    Export
                  </Button>
                </>
              )}
            </Box>
            {isEditMode && (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'end',
                  mt: 1,
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    backgroundColor: '#ffffaa',
                    p: 0.5,
                    pb: 0.25,
                    lineHeight: 'unset',
                    borderRadius: 2,
                  }}
                >
                  ℹ️ Click rates to edit
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
      <Box sx={{ flex: 1 }} className="ag-theme-material">
        {!isLoadingCompGrids ? (
          <Box sx={{ height: '100%', width: '100%' }}>
            <AgGridReact
              pinnedTopRowData={adding ? [inputRow] : undefined}
              headerHeight={40}
              columnDefs={columnDefs}
              rowData={filteredData}
              suppressHorizontalScroll={false}
              alwaysShowHorizontalScroll={true}
              debounceVerticalScrollbar={true}
              ref={gridRef}
              onCellValueChanged={onCellValueChanged}
              singleClickEdit={true}
              tooltipShowDelay={500}
              onRowSelected={handleRowSelected}
              suppressRowClickSelection={true}
              getRowId={getRowId}
              gridOptions={{
                suppressMovableColumns: true,
                rowSelection: isEditMode
                  ? {
                      mode: 'multiRow',
                      headerCheckbox: true,
                    }
                  : undefined,
              }}
              getRowHeight={getRowHeight}
            />
            <TablePagination
              rowsPerPageOptions={[50, 250, 500, 1000]}
              component="div"
              count={criteriaCount}
              rowsPerPage={limit}
              page={page}
              onPageChange={(_e, newPage) => setQueryParams({ page: newPage })}
              onRowsPerPageChange={(e) =>
                setQueryParams({
                  limit: e.target.value ? +e.target.value : 50,
                  page: 0,
                })
              }
              sx={{
                '&.MuiTablePagination-root': {
                  position: 'fixed',
                  bottom: 0,
                  right: 0,
                  background: 'white',
                },
                '& .MuiTablePagination-toolbar': {
                  minHeight: 42,
                  height: 42,
                  pl: 2,
                },
              }}
            />
          </Box>
        ) : (
          <LoadingCircle />
        )}
      </Box>
      <CompGridBulkActions
        openBulkActionsDialog={openBulkActionsDialog}
        setOpenBulkActionsDialog={setOpenBulkActionsDialog}
        selectedCriteria={selectedCriteria}
        selectedLevels={selectedLevels}
        selectedDateRanges={selectedDateRanges}
        setSelectedDateRanges={setSelectedDateRanges}
        loading={loading}
        bulkEditRates={bulkEditRates}
        bulkCopyRates={bulkCopyRates}
        bulkDeleteRates={bulkDeleteRates}
        showConfirmDeleteDialog={showConfirmDeleteDialog}
        setShowConfirmDeleteDialog={setShowConfirmDeleteDialog}
      />
      <ConfirmNewRatesDialog
        open={confirmNewRates}
        onClose={() => setConfirmNewRates(false)}
        params={params}
        compGrids={compGrids}
        selectedDateRanges={selectedDateRanges}
        handleNewRatesConfirm={handleNewRatesConfirm}
        handleDateRangeChange={handleDateRangeChange}
      />
      <CompGridExportDialog
        open={openExportDialog}
        setOpen={setOpenExportDialog}
        onExportCsv={onExportCsv}
      />
    </Box>
  );
};

export default CompGridsView;
