import { useState } from 'react';
import { DocumentExportType } from 'common/globalTypes';
import axios from 'axios';

import API from '@/services/API';
import { handleFileDownload } from '@/services/helpers';
import useSnackbar from '@/contexts/useSnackbar';
import { getEnvVariable } from '@/env';

interface UseCompGridExportProps {
  onExportCsv: () => void;
  setOpen: (open: boolean) => void;
}

// TODO: Add unit tests for this hook when vitesting is set up for the FE.
export const useCompGridExport = ({
  onExportCsv,
  setOpen,
}: UseCompGridExportProps) => {
  const [loading, setLoading] = useState<boolean>(false);
  const { showSnackbar } = useSnackbar();

  const exportPoster = API.getMutation('export/comp_grids_viewer', 'POST');

  const handleExport = async (
    exportType: DocumentExportType,
    selectedCarriers: Array<{ id: string; name: string }>,
    selectedLevels: Array<{ id: string; name: string }>
  ) => {
    if (exportType === DocumentExportType.CSV) {
      onExportCsv();
    } else if (exportType === DocumentExportType.PDF) {
      setLoading(true);
      try {
        const dataToExport = {
          export_type: exportType,
          company_str_ids: selectedCarriers.map((carrier) => carrier.id),
          comp_grid_levels_str_ids: selectedLevels.map((level) => level.id),
        };

        const res = await axios({
          url: `${getEnvVariable('API')}/api/export/comp_grids_viewer`,
          method: 'POST',
          headers: await API.getHeaders(),
          responseType: 'blob',
          data: dataToExport,
        });

        const responseWrapper = {
          headers: {
            get: (key: string) => res.headers[key.toLowerCase()],
          },
          blob: () => Promise.resolve(res.data),
        } as Response;

        await handleFileDownload(responseWrapper, {
          exportOptions: { fileName: 'comp-grids-export.pdf' },
        });
      } catch (error) {
        showSnackbar('Export failed', 'error');
        console.error('Export error:', error);
      } finally {
        setLoading(false);
      }
    }
    setOpen(false);
  };

  return {
    handleExport,
    loading,
    isExporting: exportPoster.isPending,
  };
};
