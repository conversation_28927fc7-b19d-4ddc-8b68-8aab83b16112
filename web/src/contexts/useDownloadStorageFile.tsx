import { captureException } from '@sentry/react';
import { removeLeadingTrailingChar } from 'common/helpers';
import { useCallback, useEffect, useState } from 'react';
import type { AxiosResponse } from 'axios';

import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';

type downloadParams = {
  endpoint_str_id: string;
  file_preview_type: 'override' | 'original' | 'logo';
  endpoint?: 'documents' | 'accounts' | 'admin/processors/descriptions';
};

const useDownloadStorageFile = () => {
  const [file, setFile] = useState<File | null>(null);
  const [blobFile, setBlobFile] = useState<Blob | null>(null);
  const [error, setError] = useState<string | null>(null);

  const [downloadFileParams, setDownloadFileParams] = useState<downloadParams>(
    {} as downloadParams
  );

  const { showSnackbar } = useSnackbar();

  const downloader = API.getMutation('storage/download', 'POST', {
    rawData: true,
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const downloadFile = useCallback(
    async (downloadFileParams: downloadParams) => {
      const { endpoint_str_id, file_preview_type, endpoint } =
        downloadFileParams;
      if (!file_preview_type || !endpoint_str_id) return;
      try {
        setError(null);
        const res: AxiosResponse = await downloader.mutateAsync({
          endpoint_str_id,
          file_preview_type,
          endpoint,
        });

        if (res.status !== 200) {
          showSnackbar(`Error: ${res.statusText}`, 'error');
          return null;
        }

        const filename = removeLeadingTrailingChar(
          res.headers['content-disposition']?.split('=')[1] || 'documents',
          '"'
        );
        const blob = res.data;
        const file = new File([blob], filename, { type: blob.type });
        setFile(file);
        setBlobFile(blob);
        return file;
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } catch (err: any) {
        if (err instanceof Blob) {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader?.result;
            setError(
              typeof result === 'object'
                ? JSON.stringify(result)
                : String(result)
            );
          };
          reader.readAsText(err);
        } else {
          console.error('Error downloading file', err);
          captureException(err);
        }
        return null;
      }
    },
    /*
     * Lint disabled due to regression in the exhaustive-deps rule
     * PLT-2299 https://linear.app/fintary/issue/PLT-2299/usedownloadstoragefile-hook-lint-fix
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [downloadFileParams]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const { endpoint_str_id, file_preview_type } = downloadFileParams;
    if (endpoint_str_id && file_preview_type) {
      downloadFile(downloadFileParams);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [downloadFileParams]);

  return { file, blobFile, error, downloadFile, setDownloadFileParams };
};

export default useDownloadStorageFile;
