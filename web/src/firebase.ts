import { getEnvVariable, isProductionApp, isTestMode } from '@/env';
import firebase from 'firebase/compat/app';

import 'firebase/compat/analytics';
import 'firebase/compat/auth';
import 'firebase/compat/firestore';
import 'firebase/compat/functions';
import 'firebase/compat/storage';
import 'firebase/compat/performance';

let auth: firebase.auth.Auth,
  firestore: firebase.firestore.Firestore,
  storage: firebase.storage.Storage,
  functions: firebase.functions.Functions,
  analytics: firebase.analytics.Analytics;

if (!isTestMode()) {
  firebase.initializeApp({
    apiKey: getEnvVariable('FIREBASE_API_KEY'),
    authDomain: getEnvVariable('FIREBASE_AUTH_DOMAIN'),
    projectId: getEnvVariable('FIREBASE_PROJECT_ID'),
    storageBucket: getEnvVariable('FIREBASE_STORAGE_BUCKET'),
    messagingSenderId: getEnvVariable('FIREBASE_MESSAGING_SENDER_ID'),
    appId: getEnvVariable('FIREBASE_APP_ID'),
    measurementId: getEnvVariable('FIREBASE_MEASUREMENT_ID'),
  });

  auth = firebase.auth();
  firestore = firebase.firestore();
  storage = firebase.storage();
  functions = firebase.functions();
  analytics = firebase.analytics();

  if (
    !isProductionApp() &&
    getEnvVariable({ name: 'USE_FIREBASE_EMULATOR', defaultValue: 'false' }) ===
      'true'
  ) {
    console.log('Using Firebase emulators');
    // biome-ignore lint/correctness/useHookAtTopLevel: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    auth.useEmulator('http://localhost:4002');
    // biome-ignore lint/correctness/useHookAtTopLevel: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    functions.useEmulator('localhost', 4001);
    // biome-ignore lint/correctness/useHookAtTopLevel: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    firestore.useEmulator('localhost', 4003);
  }
}

export { auth, firestore, storage, functions, analytics };

export default firebase;
