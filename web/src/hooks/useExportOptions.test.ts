import { renderHook } from '@testing-library/react';
import type {
  RefetchOptions,
  QueryObserverResult,
} from '@tanstack/react-query';
import { vi, type Mock } from 'vitest';

import { useExportOptions } from './useExportOptions';
import API from '@/services/API';

vi.mock('@/services/API', () => ({
  __esModule: true,
  default: {
    getBasicQuery: vi.fn(),
  },
}));

const validProcessor = {
  name: 'Test Processor',
  str_id: 'test-processor',
  processor: '() => {}',
};

const invalidProcessor = {
  name: '',
  str_id: '',
  processor: '',
};

describe('useExportOptions', () => {
  beforeEach(() => {
    (API.getBasicQuery as Mock).mockReset();
  });

  it('Given no processors, should return default options', () => {
    (API.getBasicQuery as Mock).mockReturnValue({
      // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      abort: () => {},
      data: [],
      error: null,
      isError: false,
      isEnabled: true,
      isPending: false,
      isLoading: false,
      isLoadingError: false,
      isRefetchError: false,
      isSuccess: true,
      isPlaceholderData: false,
      status: 'success',
      failureCount: 0,
      failureReason: null,
      isFetched: true,
      isFetchedAfterMount: true,
      isInitialLoading: false,
      isPaused: false,
      isRefetching: false,
      isStale: false,
      refetch: vi.fn(),
      dataUpdatedAt: Date.now(),
      errorUpdatedAt: 0,
      fetchStatus: 'idle',
      promise: Promise.resolve(),
      errorUpdateCount: 0,
      isFetching: false,
    });
    const { result } = renderHook(() => useExportOptions('commissions-report'));
    expect(result.current).toEqual([
      {
        id: 'export',
        label: 'Export',
        options: {},
      },
      {
        id: 'export-producer-view',
        label: 'Export producer view',
        options: {
          producer_view: true,
          disabled: true,
          tooltip: 'Please select an Agent in the Agents filter to enable.',
        },
      },
    ]);
  });

  it('Given valid processors, should return default and processor options', () => {
    (API.getBasicQuery as Mock).mockReturnValue({
      data: [validProcessor],
      error: null,
      isEnabled: true,
      isLoading: false,
      // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      abort: () => {},
      isError: false,
      isPending: false,
      isLoadingError: false,
      isRefetchError: false,
      isSuccess: true,
      isPlaceholderData: false,
      status: 'success',
      dataUpdatedAt: 0,
      errorUpdatedAt: 0,
      failureCount: 0,
      failureReason: null,
      errorUpdateCount: 0,
      isFetched: false,
      isFetchedAfterMount: false,
      isFetching: false,
      isInitialLoading: false,
      isPaused: false,
      isRefetching: false,
      isStale: false,
      refetch: (
        _options?: RefetchOptions
      ): Promise<QueryObserverResult<unknown, Error>> => {
        throw new Error('Function not implemented.');
      },
      fetchStatus: 'fetching',
      promise: Promise.resolve(),
    });
    const { result } = renderHook(() => useExportOptions('commissions-report'));
    expect(
      result.current.map((option) => {
        const { options, ...rest } = option;
        const filteredOptions = Object.fromEntries(
          Object.entries(options).filter(([_, v]) => typeof v !== 'function')
        );
        return { ...rest, options: filteredOptions };
      })
    ).toEqual([
      {
        id: 'export',
        label: 'Export',
        options: {},
      },
      {
        id: 'export-producer-view',
        label: 'Export producer view',
        options: {
          producer_view: true,
          disabled: true,
          tooltip: 'Please select an Agent in the Agents filter to enable.',
        },
      },
      {
        id: 'test-processor',
        label: 'Test Processor',
        options: {
          disabled: false,
          tooltip: 'Exports the commission data with Test Processor.',
          report_processor: 'test-processor',
        },
      },
    ]);
  });

  it('Given invalid processors, should filter out invalid processor options', () => {
    (API.getBasicQuery as Mock).mockReturnValue({
      // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      abort: () => {},
      data: [invalidProcessor],
      error: null,
      isError: false,
      isEnabled: true,
      isPending: false,
      isLoading: false,
      isLoadingError: false,
      isRefetchError: false,
      isSuccess: true,
      isPlaceholderData: false,
      status: 'success',
      failureCount: 0,
      failureReason: null,
      isFetched: true,
      isFetchedAfterMount: true,
      isInitialLoading: false,
      isPaused: false,
      isRefetching: false,
      isStale: false,
      refetch: vi.fn(),
      dataUpdatedAt: Date.now(),
      errorUpdatedAt: 0,
      fetchStatus: 'idle',
      promise: Promise.resolve(),
      errorUpdateCount: 0,
      isFetching: false,
    });
    const { result } = renderHook(() => useExportOptions('commissions-report'));
    expect(result.current).toEqual([
      {
        id: 'export',
        label: 'Export',
        options: {},
      },
      {
        id: 'export-producer-view',
        label: 'Export producer view',
        options: {
          producer_view: true,
          disabled: true,
          tooltip: 'Please select an Agent in the Agents filter to enable.',
        },
      },
    ]);
  });
});
