import { Launch, VerticalSplit } from '@mui/icons-material';
import {
  Box,
  Chip,
  IconButton,
  Skeleton,
  Tooltip,
  Typography,
} from '@mui/material';
import CommonFormatter from 'common/Formatter';
import { SystemRoles, TransactionParty } from 'common/globalTypes';
import { getFilenameFromPath } from 'common/helpers';
import { formatCurrency } from 'common/helpers/formatCurrency';
import copy from 'copy-to-clipboard';
import { useContext } from 'react';
import { Link } from 'react-router-dom';
import { AccountIds } from 'common/constants';
import { States } from 'common/helpers/states';
import { getStatementFieldConfig } from 'common/field-config/statement';
import { mergeConfigsWithRenderInstance } from 'common/field-config/report';

import AgentCommissionsEdit from '@/components/CommissionsDataView/AgentCommissionsEdit';
import PayoutStatusEdit from '@/components/CommissionsDataView/PayoutStatusEdit';
import CommissionCalcLog from '@/components/molecules/CommissionCalcLog';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { CHAR_WIDTH } from '@/components/molecules/EnhancedTable/constants';
import type { NewTableProps } from '@/components/molecules/EnhancedTable/NewTable';
import { ComparisonContext } from '@/contexts/ReconciliationConfirmProvider';
import API from '@/services/API';
import DataTransformation from '@/services/DataTransformation';
import Formatter from '@/services/Formatter';
import { useAccountStore } from '@/store';
import { type Field, Roles } from '@/types';
import { buildReceivableValueFilterRenderOnly } from './helpers/transactions.helpers';
import { ROUTES } from '@/constants/routes';
import {
  ChildrenDataRenderer,
  ChildrenDataTableFormatter,
} from '@/components/Statements/ChildrenData';
import {
  policyDataIfEmpty,
  PolicyIdColumn,
} from '@/components/Statements/PolicyIdColumn';
import { getEnvVariable } from '@/env';

const Normalizer = DataTransformation;

/**
 * Statements class represents the structure and behavior of statements data for data view.
 * How to create a new column at: https://github.com/Fintary/fintary/blob/main/docs/data-view/README.md
 */
class Statements {
  #mode = 'default';

  label = 'Statements data';
  labelSimple = 'Statements data';

  table = 'statement_data';

  copyable = true;

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  filters: { [key: string]: any } = {};

  fields: { [key: string]: Field } = {};

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  actions: Record<string, any>[] = [];

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  outstandingFieldsInMobileView: any[] = [];

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  dateFilters: any[] = [];

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  fieldsCollapsed: any[] = [];

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  options: Record<string, any> = {};

  stickyColumns: string[] = [];
  useNewTable = true;
  sortFilterByPosition = true;

  extraBulkEditCsvFields = [{ id: 'id', label: 'id', required: true }];

  // Only work for new table
  dynamicSelectsConfig: NewTableProps['dynamicSelectsConfig'] = [];

  constructor(
    mode?: string | null,
    role?: SystemRoles | null,
    userRole?: Roles | null,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    options?: any
  ) {
    this.#mode = mode ?? this.#mode;
    this.options = options;

    const config = getStatementFieldConfig({
      // @ts-expect-error
      mode,
      role,
    });

    const renderFieldsConfig = {
      policy_id: {
        normalizer: (s) => s?.toString(),
        tableFormatter: (val, row) => (
          <PolicyIdColumn
            val={val}
            row={row}
            accountId={this.options?.account_id}
          />
        ),
        getWidth: ({ estimatedWidth }) => {
          return estimatedWidth + 80; // 40 for copy button
        },
      },
      // TotalAmount: {
      //   label: 'Total amount',
      //   matches: ['total', 'total amount', 'total amt', 'amount'],
      //   enabled: true,
      //   global: true,
      //   formatter: (s) => (s?.toString()?.startsWith('$') ? s : `$${s}`),
      // },
      commission_amount: {
        formatter: config.fields.commission_amount.textFormatter,
        normalizer: Normalizer.normalizeCurrency,
      },
      commission_paid_amount: {
        formatter: config.fields.commission_paid_amount.textFormatter,
        normalizer: Normalizer.normalizeCurrency,
      },
      advanced_commission_amount: {
        formatter: config.fields.advanced_commission_amount.textFormatter,
        normalizer: Normalizer.normalizeCurrency,
      },
      customer_name: {
        getWidth: ({ estimatedWidth }) => {
          // TODO: find better solution
          const headerWidth = 'Customer name'.length * CHAR_WIDTH + 12 + 24 + 8;
          return Math.max(estimatedWidth, headerWidth);
        },
        tableFormatter: config.fields.customer_name.textFormatter,
      },
      invoice_date: {
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      payment_date: {
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      processing_date: {
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      agent_name: {},
      statement_number: {},
      transaction_type: {
        tableFormatter: policyDataIfEmpty('transaction_type'),
      },
      writing_carrier_name: {
        render: (
          field,
          newData,
          setNewData,
          _dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          return (
            <EnhancedSelect<{ company_name: string }>
              label={field.label}
              listContainerSx={{
                minWidth: 500,
              }}
              enableSearch
              options={dynamicSelectData?.companies || []}
              extractValue={(options) => {
                return options.find(
                  (item) =>
                    item.company_name === newData?.writing_carrier_name?.trim()
                );
              }}
              url="/companies"
              labelKey="company_name"
              valueKey="company_name"
              onChange={(val) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      writing_carrier_name: val.company_name,
                    };
                  });
                }
              }}
            />
          );
        },
        formatter: (val, collectionVals: { company_name: string }[] = []) => {
          if (
            val &&
            Array.isArray(collectionVals) &&
            collectionVals.length > 0
          ) {
            const datum = collectionVals?.filter((company) => {
              return company.company_name === val?.trim();
            })?.[0];
            return datum ? (
              <Chip
                key={val}
                label={`${datum.company_name}`}
                clickable
                component="a"
                href={`/companies?q=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) => option.company_name,
        optionValuer: (option) => option.company_name,
      },
      premium_type: {},
      carrier_name: {
        render: (
          field,
          newData,
          setNewData,
          _dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          return (
            <EnhancedSelect<{ company_name: string }>
              label={field.label}
              enableSearch
              listContainerSx={{
                minWidth: 500,
              }}
              options={dynamicSelectData?.companies || []}
              extractValue={(options) => {
                return options.find(
                  (item) => item.company_name === newData?.carrier_name?.trim()
                );
              }}
              url="/companies"
              labelKey="company_name"
              valueKey="company_name"
              onChange={(val) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      carrier_name: val.company_name,
                    };
                  });
                }
              }}
            />
          );
        },
        formatter: (val, collectionVals: { company_name: string }[] = []) => {
          if (
            val &&
            Array.isArray(collectionVals) &&
            collectionVals.length > 0
          ) {
            const datum = collectionVals?.filter(
              (company) => company.company_name === val?.trim()
            )?.[0];
            return datum ? (
              <Chip
                key={val}
                label={`${datum.company_name}`}
                clickable
                component="a"
                href={`/companies?q=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) => option.company_name,
        optionValuer: (option) => option.company_name,
      },
      agent_id: {},
      premium_amount: {
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
      },
      expected_result: {},
      commission_rate: {
        formatter: Normalizer.formatPercentage,
        normalizer: Normalizer.normalizePercentage,
      },
      carrier_rate: {
        formatter: Normalizer.formatPercentage,
        normalizer: Normalizer.normalizePercentage,
      },
      receivable_value_agency_rate: buildReceivableValueFilterRenderOnly(
        TransactionParty.POLICY
      ),
      receivable_value_agent_rate: buildReceivableValueFilterRenderOnly(
        TransactionParty.AGENT
      ),
      receivable_value_override_rate: buildReceivableValueFilterRenderOnly(
        TransactionParty.AGENCY
      ),
      // Rows - Optional
      effective_date: {
        tableFormatter: policyDataIfEmpty(
          'effective_date',
          Normalizer.formatDate
        ),
        formatter: Normalizer.formatDate,
        normalizer: Normalizer.normalizeDate,
      },
      product_type: {
        tableFormatter: policyDataIfEmpty('product_type'),
      },
      product_sub_type: {
        tableFormatter: policyDataIfEmpty('product_sub_type'),
      },
      product_name: {
        tableFormatter: policyDataIfEmpty('product_name'),
      },
      product_option_name: {
        tableFormatter: policyDataIfEmpty('product_option_name'),
      },
      fees: {
        formatter: config.fields.fees.textFormatter,
        normalizer: Normalizer.normalizeCurrency,
      },
      group_id: {},
      internal_id: {
        tableFormatter: policyDataIfEmpty('internal_id'),
      },
      period_date: {
        formatter: config.fields.period_date.textFormatter,
        normalizer: Normalizer.normalizeDate,
      },
      status: {},
      performance_bonus: {},
      override: {},
      payee: {},
      payee_id: {},
      agent_level: {},
      agency: {},
      policy_issue_date: {},
      policy_amount: {
        formatter: config.fields.policy_amount.textFormatter,
        normalizer: Normalizer.normalizeCurrency,
      },
      policy_term: {},
      premium_received: {
        formatter: config.fields.premium_received.textFormatter,
        normalizer: Normalizer.normalizeCurrency,
      },
      compensation_type: {},
      income_class: {},
      billing_frequency: {},
      premium_transaction: {},
      commissionable_premium_amount: {
        tableFormatter: policyDataIfEmpty(
          'commissionable_premium_amount',
          Normalizer.formatCurrency
        ),
        formatter: Normalizer.formatCurrency,
        normalizer: Normalizer.normalizeCurrency,
      },
      account_type: {},
      issue_age: {
        normalizer: Normalizer.normalizeInt,
        tableFormatter: policyDataIfEmpty('issue_age'),
      },
      customer_paid_premium_amount: {
        formatter: config.fields.customer_paid_premium_amount.textFormatter,
        normalizer: Normalizer.normalizeCurrency,
      },
      bill_mode: {},
      geo_state: {
        tableFormatter: policyDataIfEmpty(
          'geo_state',
          (val: string): string => {
            const state = States.find((state) => state.id === val);
            return state ? state.label : val;
          }
        ),
      },
      split_percentage: {
        tableFormatter: (val, row) => {
          const isDiff = val !== row.report?.split_percentage;
          const policyVal =
            row.contacts?.length === 1
              ? row.report?.contacts_split?.[row.contacts[0]]
              : undefined;
          return val ? (
            <Tooltip
              title={
                isDiff && policyVal
                  ? `Policy data differs(${Normalizer.formatPercentage(
                      policyVal
                    )})`
                  : ''
              }
            >
              <span>{Normalizer.formatPercentage(val)}</span>
            </Tooltip>
          ) : row.contacts?.length === 1 &&
            row.report?.contacts_split?.[row.contacts[0]] ? (
            <Tooltip title="Sourced from policy data">
              <span>{Normalizer.formatPercentage(policyVal)}*</span>
            </Tooltip>
          ) : (
            ''
          );
        },
        formatter: Normalizer.formatPercentage,
        normalizer: Normalizer.normalizePercentage,
      },
      group_name: {
        tableFormatter: policyDataIfEmpty('group_name'),
      },
      payment_mode: {
        tableFormatter: policyDataIfEmpty('payment_mode'),
      },
      aggregation_id: {
        tableFormatter: (val, row) => {
          const policyVal = row.report?.aggregation_id;
          const isDiff = val !== policyVal;
          return val ? (
            <Tooltip
              title={
                isDiff && policyVal ? `Policy data differs(${policyVal})` : ''
              }
            >
              {val}
            </Tooltip>
          ) : policyVal && row.report?.policy_id ? (
            <Tooltip title="Sourced from policy data">
              <span>{policyVal}*</span>
            </Tooltip>
          ) : (
            ''
          );
        },
      },
      member_count: {
        normalizer: Normalizer.normalizeInt,
      },
      commission_basis: {},
      standardized_customer_name: {},
      contacts: {
        render: (
          field,
          newData,
          setNewData,
          _dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          return (
            <EnhancedSelect
              label={field.label}
              enableSearch
              multiple
              disableAllOption
              enableSelectAllSearchResult={false}
              options={dynamicSelectData?.contacts || []}
              url="/contacts"
              valueKey="str_id"
              extractValue={(options) => {
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                return options.filter((item: any) =>
                  newData?.contacts?.includes(item.str_id)
                );
              }}
              customLabel={(option) => {
                return Formatter.contact(option, {
                  account_id:
                    useAccountStore.getState().selectedAccount?.accountId,
                });
              }}
              onChange={(vals) => {
                if (vals) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      contacts: (vals as any[]).map((item) => item.str_id),
                    };
                  });
                }
              }}
            />
          );
        },
        getWidth: ({ allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;
          let maxWidth = 0;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          allRows.forEach((row) => {
            if (row.contacts?.length > 0) {
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              row.contacts.forEach((id) => {
                const contact = mapData[id];
                const label = Formatter.contact(contact, {
                  account_id: this.options?.account_id,
                });
                const width = label.length * CHAR_WIDTH;
                if (width > maxWidth) {
                  maxWidth = width;
                }
              });
            }
          });
          return maxWidth;
        },
        formatter: (val, collectionVals: { str_id: string }[] = []) => {
          if (Array.isArray(collectionVals) && collectionVals.length > 0) {
            const datum = collectionVals?.filter(
              (datum) => datum.str_id === val
            )?.[0];
            return datum ? (
              <Chip
                key={val}
                label={Formatter.contact(datum, {
                  account_id: this.options?.account_id,
                })}
                clickable
                component="a"
                href={`${ROUTES.agents.url}?id=${val}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val; // Not formatting when collectionVals is not available
        },
        optionFormatter: (o) =>
          Formatter.contact(o, { account_id: this.options?.account_id }),
        optionValuer: (option) => option.str_id,
      },
      agent_commissions: {
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="agent_commissions"
            />
          );
        },
        getWidth: ({ allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;

          let maxWidth = 0;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          allRows.forEach((row) => {
            if (row.agent_commissions) {
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.entries(row.agent_commissions).forEach(([id, v]) => {
                const contact = mapData[id];
                const label = `${Formatter.contact(contact, {
                  account_id: this.options?.account_id,
                })}: ${formatCurrency(v as number)}`;

                const width = label.length * CHAR_WIDTH;
                if (width > maxWidth) {
                  maxWidth = width;
                }
              });
            }
          });
          return maxWidth;
        },
        tableFormatter: (
          val,
          _row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, _v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact = collectionVals?.find(
                    (e) => e?.str_id === k
                  ) ?? { id: undefined };
                  return contact.id ? (
                    <Chip
                      clickable
                      component="a"
                      href={`${ROUTES.agents.url}?id=${contact.str_id}`}
                      target="_blank"
                      key={contact?.id}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${Formatter.currency(v)}`}
                      sx={{ m: 0.1 }}
                    />
                  ) : (
                    <Skeleton key={k} />
                  );
                })}
            </Box>
          );
        },
      },
      agent_commissions_v2: {
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="agent_commissions_v2"
            />
          );
        },
        tableFormatter: (
          val,
          _row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, _v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact = collectionVals?.find(
                    (e) => e?.str_id === k
                  ) ?? { id: undefined };
                  return contact.id ? (
                    <Chip
                      key={contact?.id}
                      clickable
                      component="a"
                      target="_blank"
                      href={`${ROUTES.agents.url}?id=${contact.str_id}`}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${Formatter.currency(v)}`}
                      sx={{ m: 0.1 }}
                    />
                  ) : (
                    <Skeleton key={k} />
                  );
                })}
            </Box>
          );
        },
      },
      comp_calc: {
        render: (field, row, setter, _collectionVals, dynamicSelectsData) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="comp_calc"
            />
          );
        },
        tableFormatter: (
          val,
          _row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, _v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact = collectionVals?.find((e) => e?.id === +k) ?? {
                    id: undefined,
                  };
                  return contact.id ? (
                    <Chip
                      key={contact?.id}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${Formatter.currency(v)}`}
                      sx={{ m: 0.1 }}
                    />
                  ) : (
                    <Skeleton key={k} />
                  );
                })}
            </Box>
          );
        },
      },
      comp_calc_log: {
        formatter: (
          val,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, _v]) => k !== 'total')
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                .map(([k, v]: [string, any]) => {
                  const contact =
                    collectionVals?.find((e) => e?.id === +k) ?? {};
                  // After grouping if is not an array means that grouped data is not the same so we render ***
                  if (!Array.isArray(v)) {
                    return <Box key={k}>***</Box>;
                  }
                  return (
                    <Box key={k}>
                      {Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}
                      <br />
                      {v.map((e, i) => (
                        // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                        <CommissionCalcLog commissionProfile={e} key={i} />
                      ))}
                    </Box>
                  );
                })}
            </Box>
          );
        },
      },
      comp_calc_status: {
        render: (
          field,
          row,
          setter,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          return (
            <PayoutStatusEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelects={collectionVals}
              key="comp_calc_status"
            />
          );
        },
        tableFormatter: (
          val,
          _row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val).map(([k, v]) => {
                const contact = collectionVals?.find((e) => e?.id === +k) ?? {
                  id: undefined,
                };
                return contact.id ? (
                  <Chip
                    key={contact?.id}
                    label={`${Formatter.contact(contact, {
                      account_id: this.options?.account_id,
                    })}: ${v}`}
                    sx={{ m: 0.1 }}
                  />
                ) : (
                  <Skeleton key={k} />
                );
              })}
            </Box>
          );
        },
      },
      agent_commissions_log: {
        // biome-ignore lint/correctness/noEmptyPattern: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        getWidth: ({}) => {
          // The formatter renders complex component, can't calculate width for now
          // TODO: find a better solution
          return 650;
        },
        formatter: (val, collectionVals: { str_id: string }[] = []) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, _v]) => k !== 'total')
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                .map(([k, v]: [string, any]) => {
                  const contact =
                    collectionVals?.find((e) => e.str_id === k) ?? {};
                  // After grouping if is not an array means that grouped data is not the same so we render ***
                  if (!Array.isArray(v)) {
                    return <Box key={k}>***</Box>;
                  }

                  return (
                    <Box key={k}>
                      {Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}
                      <br />
                      {v.map((e, i) => (
                        <CommissionCalcLog
                          statement_id={this.options.statement_id}
                          commissionProfile={e}
                          // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                          key={i}
                        />
                      ))}
                    </Box>
                  );
                })}
            </Box>
          );
        },
      },
      flags: {
        getWidth: ({ allRows }) => {
          let maxWidth = 0;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          allRows.forEach((row) => {
            const flags = row?.flags;
            if (!flags) return 2;
            const keyWithMaxLengthValue = Object.keys(flags).reduce(
              (maxKey, currentKey) => {
                return flags[currentKey].length > flags[maxKey].length
                  ? currentKey
                  : maxKey;
              },
              Object.keys(flags)[0]
            );
            const width =
              ((keyWithMaxLengthValue?.length || 0) as number) * CHAR_WIDTH;
            maxWidth = Math.max(maxWidth, width);
          });
          return maxWidth + 50 * 2;
        },
        render: (
          _field,
          row,
          _setter,
          _collectionVals: { id: number; str_id: string }[] = []
        ) => {
          return row?.flags ? (
            <Box key="flags">
              <Typography>Flags</Typography>
              {Object.entries(row.flags).map(([key, value]) => {
                return (
                  <Chip
                    key={key}
                    label={String(`${key}: ${value}`)}
                    sx={{ m: 0.1 }}
                  />
                );
              })}
            </Box>
          ) : null;
        },
        formatter: (val) => {
          if (!val) return '';
          return (
            <Box>
              {Object.entries(val).map(([key, value]) => {
                return (
                  <Chip
                    key={key}
                    label={String(`${key}: ${value}`)}
                    sx={{ m: 0.1 }}
                  />
                );
              })}
            </Box>
          );
        },
      },
      flags_log: {
        getWidth: ({ allRows }) => {
          let maxWidth = 0;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          allRows.forEach((row) => {
            const flags = row?.flags_log;
            if (!flags) return 2;
            const keyWithMaxLengthValue = Object.keys(flags).reduce(
              (maxKey, currentKey) => {
                return flags[currentKey].length > flags[maxKey].length
                  ? currentKey
                  : maxKey;
              },
              Object.keys(flags)[0]
            );
            const width =
              ((keyWithMaxLengthValue?.length || 0) as number) * CHAR_WIDTH;
            maxWidth = Math.max(maxWidth, width);
          });
          return maxWidth + 50 * 2;
        },
        render: (_field, row, _setter) => {
          return row?.flags_log ? (
            <Box key="flags_log">
              <Typography>Flags log</Typography>
              {Object.entries(row.flags_log).map(([key, value]) => {
                return (
                  <Chip
                    key={key}
                    label={
                      value && typeof value === 'object' && 'message' in value
                        ? // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                          String((value as { message: any }).message)
                        : String(value)
                    }
                    sx={{ m: 0.1 }}
                  />
                );
              })}
            </Box>
          ) : null;
        },
        formatter: (val) => {
          if (!val) return '';
          return (
            <Box>
              {Object.entries(val).map(([key, keyValue]) => {
                return (
                  <Chip
                    key={key}
                    label={
                      keyValue &&
                      typeof keyValue === 'object' &&
                      'message' in keyValue
                        ? // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                          String((keyValue as { message: any }).message)
                        : String(keyValue)
                    }
                    sx={{ m: 0.1 }}
                  />
                );
              })}
            </Box>
          );
        },
      },
      agent_commissions_status: {},
      agent_commissions_status2: {
        render: (
          field,
          row,
          setter,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          return (
            <PayoutStatusEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelects={collectionVals}
              key="agent_commissions_status2"
            />
          );
        },
        tableFormatter: (
          val,
          _row,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val).map(([k, v]) => {
                const contact = collectionVals?.find(
                  (e) => e?.str_id === k
                ) ?? { id: undefined };
                return contact.id ? (
                  <Chip
                    key={contact?.id}
                    label={`${Formatter.contact(contact, {
                      account_id: this.options?.account_id,
                    })}: ${v}`}
                    sx={{ m: 0.1 }}
                  />
                ) : (
                  <Skeleton key={k} />
                );
              })}
            </Box>
          );
        },
      },
      notes: {
        tableFormatter: policyDataIfEmpty('notes'),
      },
      tags: {
        normalizer: (val) => {
          if (Array.isArray(val) && val.length === 0) return [];
          if (typeof val === 'string')
            return val?.split(',').map((s) => s.trim()) ?? [];
          return val;
        },
        formatter: config.fields.tags.textFormatter,
      },
      document_id: {
        render: (
          field,
          newData,
          setNewData,
          _dynamicSelects, // Deprecated
          dynamicSelectData
        ) => {
          return (
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            <EnhancedSelect<any>
              label={field.label}
              enableSearch
              listContainerSx={{
                minWidth: 500,
              }}
              options={dynamicSelectData?.documents?.data || []}
              extractValue={(options) => {
                return options.find((item) => {
                  return item.str_id === newData?.document_id;
                });
              }}
              customLabel={(item) => {
                return `${item?.file_path?.endsWith(item.filename) ? item.filename : `${getFilenameFromPath(item.file_path)}`}`;
              }}
              url="/documents"
              labelKey="label"
              valueKey="str_id"
              onChange={(val) => {
                if (val) {
                  setNewData((prev) => {
                    return {
                      ...prev,
                      document_id: val.str_id,
                    };
                  });
                }
              }}
            />
          );
        },
        getWidth: ({ dynamicSelectData, allRows }) => {
          const map =
            dynamicSelectData?.reduce((acc, item) => {
              acc[item.str_id] = item;
              return acc;
            }, {}) || {};
          let estimatedWidth = 0;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          allRows?.forEach((row) => {
            const doc = map[row.document_id];
            if (doc) {
              const filename = getFilenameFromPath(doc.file_path);
              const width = (filename?.length || 0) * CHAR_WIDTH;
              if (width > estimatedWidth) {
                estimatedWidth = width;
              }
            }
          });

          return estimatedWidth;
        },
        formatter: (val, collectionVals) => {
          const dynamicSelectsData = collectionVals?.data || collectionVals;
          if (dynamicSelectsData?.length) {
            const item = dynamicSelectsData?.find(
              (item) => item.str_id === val
            );
            return item ? (
              <Chip
                clickable
                component="a"
                href={`/documents?id=${item?.str_id}`}
                target="_blank"
                label={`${item?.file_path?.endsWith(item.filename) ? item.filename : `${getFilenameFromPath(item.file_path)}`}`}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
        optionFormatter: (option) =>
          `${option.file_path?.endsWith(option.filename) ? option.filename : `${getFilenameFromPath(option.file_path)}`}`,
        optionValuer: (option) => option.str_id,
      },
      processing_status: {},
      children_data: {
        tableFormatter: (vals, row) => (
          <ChildrenDataTableFormatter vals={vals} row={row} />
        ),
        render: (_field, row, _setter, _collectionVals): React.ReactElement =>
          row?.children_data && (
            <ChildrenDataRenderer
              row={row}
              field="children_data"
              collectionVals={row.children_data}
              accountId={this.options?.account_id}
            />
          ),
      },
      details: {
        tableFormatter: (vals, row) => (
          <ChildrenDataTableFormatter
            vals={vals}
            row={row}
            title="Payment allocations"
          />
        ),
        render: (_field, row, _setter, _collectionVals): React.ReactElement => {
          return (
            row?.details && (
              <ChildrenDataRenderer
                row={row}
                field="details"
                title="Payment allocations"
                collectionVals={row.details}
                accountId={this.options?.account_id}
              />
            )
          );
        },
      },
      payment_status: {},
      agent_payout_rate: {
        getWidth: ({ allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;
          let maxWidth = 0;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          allRows.forEach((row) => {
            if (row.agent_payout_rate) {
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.entries(row.agent_payout_rate).forEach(([id, v]) => {
                const contact = mapData[id];
                const label = `${Formatter.contact(contact, {
                  account_id: this.options?.account_id,
                })}: ${formatCurrency(v as number)}`;

                const width = label.length * CHAR_WIDTH;
                if (width > maxWidth) {
                  maxWidth = width;
                }
              });
            }
          });
          return maxWidth;
        },
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="agent_payout_rates"
              percentageFormat="percentage"
            />
          );
        },
        formatter: (
          val,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, _v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact = collectionVals?.find((e) => e.str_id === k);
                  return (
                    <Chip
                      key={contact?.id}
                      component={'a'}
                      target="_blank"
                      clickable
                      href={`${ROUTES.agents.url}?id=${contact?.str_id}`}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${CommonFormatter.percentage(v as number, { isPercentage: true, addPrecisionForRateLowerThanTen: true })}`}
                      sx={{ m: 0.1 }}
                    />
                  );
                })}
            </Box>
          );
        },
      },
      agent_payout_rate_override: {
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              disableConditions={false}
              key="agent_payout_rate_override"
              percentageFormat="percentage"
              showOverrideMode={true}
            />
          );
        },
        dynamicFormatter: (
          val,
          collectionVals: { id: number; str_id: string }[] = [],
          row: {
            report: { agent_payout_rate_override: Record<string, number> };
          }
        ) => {
          const agentPayoutRateOverride =
            val ?? row?.report?.agent_payout_rate_override;

          if (!agentPayoutRateOverride || !Array.isArray(collectionVals))
            return '';

          const agentPayoutRateOverrideDisplay = (
            <Box>
              {Object.entries(agentPayoutRateOverride)
                .filter(([key]) => key !== 'total' && key !== 'config')
                .map(([key, value]) => {
                  const contact = collectionVals.find((e) => e.str_id === key);
                  return (
                    <Chip
                      key={contact?.id || key}
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${Formatter.percentage(Number(value) / 100)}`}
                      sx={{ m: 0.1 }}
                    />
                  );
                })}
            </Box>
          );

          return val ? (
            agentPayoutRateOverrideDisplay
          ) : (
            <Tooltip title="Sourced from policy data">
              {agentPayoutRateOverrideDisplay}
            </Tooltip>
          );
        },
      },
      agent_commission_payout_rate: {
        getWidth: ({ allRows, dynamicSelectData }) => {
          const mapData = dynamicSelectData?.reduce((acc, curr) => {
            acc[curr.str_id] = curr;
            return acc;
          }, {});
          if (!mapData) return 0;

          let maxWidth = 0;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          allRows.forEach((row) => {
            if (row.agent_commission_payout_rate) {
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.entries(row.agent_commission_payout_rate).forEach(
                ([id, v]) => {
                  const contact = mapData[id];
                  const label = `${Formatter.contact(contact, {
                    account_id: this.options?.account_id,
                  })}: ${CommonFormatter.percentage(v as number, { isPercentage: true, addPrecisionForRateLowerThanTen: true })}`;

                  const width = label.length * CHAR_WIDTH;
                  if (width > maxWidth) {
                    maxWidth = width;
                  }
                }
              );
            }
          });
          return maxWidth;
        },
        render: (
          field,
          row,
          setter,
          _collectionVals: { id: number; str_id: string }[] = [],
          dynamicSelectsData
        ) => {
          return (
            <AgentCommissionsEdit
              data={row}
              setter={setter}
              field={field}
              dynamicSelectsData={dynamicSelectsData}
              key="agent_payout_rates"
              percentageFormat="percentage"
            />
          );
        },
        formatter: (
          val,
          collectionVals: { id: number; str_id: string }[] = []
        ) => {
          if (!val || !Array.isArray(collectionVals)) return '';
          return (
            <Box>
              {Object.entries(val)
                .filter(([k, _v]) => k !== 'total')
                .map(([k, v]) => {
                  const contact = collectionVals?.find((e) => e.str_id === k);
                  return (
                    <Chip
                      key={contact?.id}
                      component={'a'}
                      target="_blank"
                      href={`${ROUTES.agents.url}?id=${contact?.str_id}`}
                      clickable
                      label={`${Formatter.contact(contact, {
                        account_id: this.options?.account_id,
                      })}: ${CommonFormatter.percentage(v as number, { isPercentage: true, addPrecisionForRateLowerThanTen: true })}`}
                      sx={{ m: 0.1 }}
                    />
                  );
                })}
            </Box>
          );
        },
      },
      report_data_id: {
        getWidth: ({ allRows }) => {
          let maxWidth = 0;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          allRows.forEach((row) => {
            const width = (row?.report?.str_id?.length || 0) * CHAR_WIDTH;
            maxWidth = Math.max(maxWidth, width);
          });
          return maxWidth + 40 * 2;
        }, // 40 *2 for 2 icons
        render(field, row) {
          return row[field.id] ? (
            <Box sx={{ m: 0.1 }}>
              <Chip
                label="Linked policy"
                component={Link}
                to={`/policies?id=${row?.report?.str_id}`}
                target="_blank"
                clickable
              />
            </Box>
          ) : (
            <Box sx={{ m: 0.1 }}>
              <Chip label="No linked policy" sx={{ cursor: 'default' }} />
            </Box>
          );
        },
        tableFormatter(val, row) {
          const ctx = useContext(ComparisonContext);
          return val ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ whiteSpace: 'nowrap' }}>
                {row?.report?.str_id}
              </span>
              <IconButton
                onClick={() => {
                  ctx.setData({
                    statements: [row],
                    policy: row.report,
                    defaultOpenStatement: row.str_id,
                  });
                  ctx.setShow(true);
                }}
              >
                <VerticalSplit />
              </IconButton>
              <IconButton
                component={Link}
                to={`/policies?id=${row?.report?.str_id}`}
                target="_blank"
              >
                <Launch />
              </IconButton>
            </Box>
          ) : null;
        },
      },
      reconciliation_status: {},
      is_virtual: {},
      virtual_type: {},
      reconciliation_method: {
        formatter: (
          val,
          collectionVals: { id: number; str_id: string; name: string }[] = []
        ) => {
          if (Array.isArray(collectionVals) && collectionVals.length > 0) {
            const datum = collectionVals?.find((datum) => datum.id === +val);
            if (val === '0') {
              return 'Manual';
            }
            return datum ? (
              <Chip
                key={val}
                label={datum.name}
                clickable
                component={Link}
                to={`/reconciliation/reconcilers?id=${datum.str_id}`}
                target="_blank"
                sx={{ m: 0.1 }}
              />
            ) : (
              `${val} (not found in Fintary)`
            );
          }
          return val;
        },
      },
      state: {
        formatter: CommonFormatter.isGrouped,
      },
    };

    mergeConfigsWithRenderInstance(config, this, {
      renderFields: renderFieldsConfig,
    });

    this.actions =
      userRole === Roles.ACCOUNT_ADMIN
        ? [
            {
              id: 'comp_calc',
              label: 'Calculate comp',
              disabled: (row) =>
                [
                  'Approved',
                  'Manual',
                  'No payment',
                  'Offset',
                  'Paid',
                  'Reviewed',
                ].includes(row.agent_commissions_status),
              toolTipMessage:
                'Comp calculation not supported for the following payout statuses: Approved, Manual, No payment, Offset, Paid, Reviewed',
              onClick: async (row) => {
                // TODO: Temp implementation for TG demo. Figure out how to use API for the call. If not, replace with Axios.
                const headers = await API.getHeaders();
                await fetch(
                  `${getEnvVariable('API')}/api/data_processing/commissions/agents`,
                  {
                    method: 'post',
                    headers,
                    body: JSON.stringify({
                      isSync: true,
                      useGroupedCommissions: true,
                      id: row.id,
                    }),
                  }
                );
              },
            },
            ...(this.options?.account_id === AccountIds.ALLIED
              ? [
                  {
                    id: 'comp_calc_with_no_grouped',
                    label: 'Calculate comp (no grouped commission)',
                    disabled: (row) =>
                      [
                        'Approved',
                        'Manual',
                        'No payment',
                        'Offset',
                        'Paid',
                        'Reviewed',
                      ].includes(row.agent_commissions_status),
                    toolTipMessage:
                      'Comp calculation not supported for the following payout statuses: Approved, Manual, No payment, Offset, Paid, Reviewed',
                    onClick: async (row) => {
                      const headers = await API.getHeaders();
                      try {
                        await fetch(
                          `${getEnvVariable('API')}/api/data_processing/commissions/agents`,
                          {
                            method: 'post',
                            headers,
                            body: JSON.stringify({
                              isSync: true,
                              useGroupedCommissions: false,
                              id: row.id,
                            }),
                          }
                        );
                      } catch (error) {
                        console.error(
                          'Error calculating commission (no grouped):',
                          error
                        );
                        throw error;
                      }
                    },
                  },
                ]
              : []),
            {
              id: 'copy_link',
              label: 'Copy link',
              onClick: (row) => {
                copy(`${window.location.origin}/commissions?id=${row.str_id}`);
              },
            },
            {
              id: 'comp_profile_matcher',
              label: 'Comp profile matcher',
              onClick: (row) => {
                let url = `${window.location.origin}/admin/tools/comp-profile-matcher?statement_id=${row.str_id}`;
                const agents = row.contacts;
                if (agents.length > 0) {
                  const agentsQuery = agents.map(encodeURIComponent).join(',');
                  url += `&agents=${agentsQuery}`;
                }
                window.open(url, '_blank');
              },
              access: SystemRoles.ADMIN,
            },
          ]
        : [
            {
              id: 'copy_link',
              label: 'Copy link',
              onClick: (row) => {
                copy(`${window.location.origin}/commissions?id=${row.str_id}`);
              },
            },
          ];

    // Add field key as field property
    // Delete fields that are not enabled
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(this.fields).forEach(([k, v]) => {
      this.fields[k].id = k;
      if (!v.enabled) {
        delete this.fields[k];
      }
    });

    const allFields = Object.values(this.fields).flatMap((field) => {
      const rowMatches = field.id
        ? [field.id, ...(field.matches ?? [])]
        : (field.matches ?? []);
      return rowMatches.filter(Boolean);
    });

    this.fieldsCollapsed = [
      ...new Set(allFields.map((field) => field?.toString().toLowerCase())),
    ];
  }

  getDataInfo = (tableData: (string | number)[][], topN = 8) => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const rowData: any[] = [];
    const ranges: { start: number; end: number }[] = [];
    let curRange: { start: number; end: number } | null = {
      start: 0,
      end: 999,
    };

    const filteredTableData = tableData.filter((row) => Array.isArray(row));
    filteredTableData.forEach((row, i) => {
      let matches = 0;
      let filled = 0;

      const dateStyleRegex =
        /(?:\d{1,2})(?:\/|-)(?:\d{1,2})(?:\/|-)(?:\d{1,4})/;
      const hasDate = dateStyleRegex.test(row.join(' '));

      const hasCurrencyValue = row.filter((cell) =>
        cell?.toString().match(/\$\d{1,}/)
      ).length;

      const hasTotalTitle = row.toString().toLowerCase().includes('total:');

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      row.forEach((cell) => {
        if (cell || cell === 0) {
          filled += 1;
        }
        if (this.fieldsCollapsed.includes(cell?.toString()?.toLowerCase())) {
          matches += 1;
        }
      });
      if (filled === 0 && curRange !== null) {
        curRange.end = i - 1;
        ranges.push(curRange);
        curRange = null;
      } else if (filled > 0 && curRange === null) {
        curRange = { start: i, end: 999 };
      }
      if (!hasDate && !hasCurrencyValue && !hasTotalTitle) {
        rowData.push({ index: i, matches, filled, length: row.length });
      }
    });
    if (curRange !== null) {
      ranges.push({ ...curRange, end: filteredTableData.length - 1 });
    }

    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let rowDataMatches;
    // No matches - use top n rows
    if (rowData.filter((row) => row.matches > 0).length === 0) {
      rowDataMatches = rowData.splice(0, Math.min(topN, rowData.length));
    } else {
      rowDataMatches = rowData
        .sort((a, b) => b.matches - a.matches)
        .filter((row) => row.matches > 0)
        .splice(
          0,
          Math.min(
            topN,
            rowData.filter((row) => row.matches > 0).length,
            rowData.length
          )
        );
    }

    const rangeData = rowDataMatches.map((row) => ({
      index: row.index,
      count: row.matches,
      fields: filteredTableData[row.index],
      data: filteredTableData.filter(
        (_dataRow, i) =>
          i >
            ranges.filter((a) => a.start <= row.index)[
              ranges.filter((a) => a.start <= row.index).length - 1
            ]?.start &&
          i <=
            ranges.filter((a) => a.start <= row.index)[
              ranges.filter((a) => a.start <= row.index).length - 1
            ]?.end &&
          i > row.index
      ),
    }));
    return { rowData, ranges, rangeData };
  };
}

export default Statements;
