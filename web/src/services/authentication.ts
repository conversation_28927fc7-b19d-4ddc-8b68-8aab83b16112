import { getEnvVariable } from '@/env';
import * as Sentry from '@sentry/react';
import { TRANSGLOBAL_SIGNIN_URL } from 'common/customer/customer.constants';

import firebase, { analytics, auth, firestore } from '@/firebase';
import { LOCAL_STORAGE_KEYS } from '@/constants/account';

export const INVITE_CODE = 'TRYFINTARY2025';

const isFirebaseError = (payload: unknown): payload is firebase.FirebaseError =>
  payload instanceof Error && payload.name === 'FirebaseError';

const extractMessageFromInternalError = (message: string): string => {
  try {
    const jsonString = message.substring(
      message.indexOf('{'),
      message.lastIndexOf('}') + 1
    );
    const parsed = JSON.parse(jsonString) as {
      error: { status: string; message: string };
    };
    return parsed.error.message;
  } catch {
    return message;
  }
};

const calculateFriendlyFirebaseMessage = (error: firebase.FirebaseError) => {
  switch (error.code) {
    case 'auth/invalid-credential':
      return `Incorrect email or password. Please try again or reset your password.`;
    case 'auth/too-many-requests':
      return 'Too many login attempts. Please try again later.';
    case 'auth/internal-error':
      return extractMessageFromInternalError(error.message);
    case 'auth/popup-closed-by-user':
      return '';
    default:
      return error.message.replace(/Firebase: (.*) \(.*\)\./, '$1');
  }
};

const signInWithUser = async (user: firebase.User, method: string) => {
  const { uid } = user;

  if (!uid) {
    throw new Error('No UID');
  }

  const userDocumentReference = firestore.collection('users').doc(uid);

  const userDocument = await userDocumentReference.get({
    source: 'server',
  });

  if (!userDocument.exists) {
    await userDocumentReference.set({}, { merge: true });
  }

  analytics.logEvent('login', {
    method,
  });
};

const authentication = {
  signUpWithEmailAddressAndPassword: async ({
    emailAddress,
    password,
  }: {
    emailAddress: string;
    password: string;
  }) => {
    if (!emailAddress || !password) {
      throw new Error('No e-mail address or password');
    }

    if (auth.currentUser) {
      throw new Error('Cannot sign up when a user is already signed in.');
    }

    let user: firebase.User | null = null;

    try {
      const createUserResult = await auth.createUserWithEmailAndPassword(
        emailAddress,
        password
      );
      user = createUserResult.user;
    } catch (error: unknown) {
      if (isFirebaseError(error)) {
        throw new Error(calculateFriendlyFirebaseMessage(error));
      }
      throw error;
    }

    if (!user) {
      throw new Error('No user');
    }
    await signInWithUser(user, 'password');
    return user;
  },
  signIn: async (emailAddress: string, password: string) => {
    if (!emailAddress || !password) {
      throw new Error('No e-mail address or password');
    }

    if (auth.currentUser) {
      throw new Error('Cannot sign in when a user is already signed in');
    }

    try {
      const { user } = await auth.signInWithEmailAndPassword(
        emailAddress,
        password
      );
      if (user == null) {
        throw new Error('No user');
      }
      await signInWithUser(user, 'password');
      return user;
    } catch (error: unknown) {
      if (isFirebaseError(error)) {
        throw new Error(calculateFriendlyFirebaseMessage(error));
      }
      throw error;
    }
  },
  signInLinkToEmail: async (emailAddress: string) => {
    if (!emailAddress) {
      throw new Error('No e-mail address');
    }

    if (auth.currentUser) {
      throw new Error('No current user');
    }

    const url = getEnvVariable('HOMEPAGE');

    if (typeof url === 'undefined') {
      Sentry.captureException(
        'REACT_APP_HOMEPAGE environment variable is undefined'
      );
      throw new Error('REACT_APP_HOMEPAGE environment variable is undefined');
    }

    const actionCodeSettings = {
      url: url,
      handleCodeInApp: true,
    };

    const value = await auth.sendSignInLinkToEmail(
      emailAddress,
      actionCodeSettings
    );

    analytics.logEvent('send_sign_in_link_to_email');

    localStorage.setItem('emailAddress', emailAddress);

    return value;
  },
  signInWithEmailLink: async (emailAddress: string, emailLink: string) => {
    if (!emailAddress || !emailLink) {
      throw new Error('No e-mail address or e-mail link');
    }

    if (auth.currentUser) {
      throw new Error('No current user');
    }

    const value = await auth.signInWithEmailLink(emailAddress, emailLink);

    analytics.logEvent('login', {
      method: 'email-link',
    });

    localStorage.removeItem('emailAddress');

    return value;
  },
  signUpWithAuthProvider: async ({
    providerId,
  }: {
    providerId: 'google.com';
  }) => {
    if (auth.currentUser) {
      throw new Error('Cannot sign up when a user is already signed in.');
    }

    try {
      const googleProvider = new firebase.auth.GoogleAuthProvider();
      const { user } = await auth.signInWithPopup(googleProvider);

      if (user == null) {
        throw new Error('No user was found');
      }

      await signInWithUser(user, providerId);
      return user;
    } catch (error: unknown) {
      if (isFirebaseError(error)) {
        throw new Error(calculateFriendlyFirebaseMessage(error));
      }
      throw error;
    }
  },
  signInWithAuthProvider: async (
    provider: {
      id: string;
      scopes?: string[];
    } & Record<string, unknown>
  ) => {
    if (auth.currentUser) {
      throw new Error('Cannot sign in when a user is already signed in');
    }

    if (!provider || provider.id == null) {
      throw new Error('No auth provider was specified');
    }
    const authProvider = new firebase.auth.OAuthProvider(provider.id);
    const { scopes } = provider;

    if (scopes) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      scopes.forEach((scope) => {
        authProvider.addScope(scope);
      });
    }
    try {
      const { user } = await auth.signInWithPopup(authProvider);

      if (user == null) {
        throw new Error('No user was found');
      }

      await signInWithUser(user, provider.id);
      return user;
    } catch (error: unknown) {
      if (isFirebaseError(error)) {
        throw new Error(calculateFriendlyFirebaseMessage(error));
      }
      throw error;
    }
  },
  signOut: async () => {
    // Check sso token in local storage
    const ssoToken = localStorage.getItem(LOCAL_STORAGE_KEYS.ssoToken);
    if (ssoToken) {
      localStorage.removeItem(LOCAL_STORAGE_KEYS.ssoToken);
      localStorage.setItem('triggerReload', Date.now().toString());
      window.location.href = TRANSGLOBAL_SIGNIN_URL;
      analytics.logEvent('sign_out');
      return 'sign out';
    }

    const { currentUser } = auth;

    if (!currentUser) {
      throw new Error('No current user');
    }

    const value = await auth.signOut();
    analytics.logEvent('sign_out');
    return value;
  },

  signOutExistingUser: async () => {
    const { currentUser } = auth;

    if (!currentUser) {
      return 'No current user';
    }

    const value = await auth.signOut();
    analytics.logEvent('sign_out');
    return value;
  },
  resetPassword: async (emailAddress: string) => {
    if (!emailAddress) {
      throw new Error('No e-mail address');
    }

    if (auth.currentUser) {
      throw new Error('No current user');
    }

    const value = await auth.sendPasswordResetEmail(emailAddress);
    analytics.logEvent('reset_password');
    return value;
  },
  sendEmailVerification: async () => {
    const { currentUser } = auth;

    if (!currentUser) {
      throw new Error('No current user');
    }

    const response = await currentUser.sendEmailVerification();
    analytics.logEvent('send_email_verification');
    return response;
  },
};

export default authentication;
