<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="283" failures="0" errors="0" time="7.888661335">
    <testsuite name="hooks/useDynamicObjectMapUpdates.test.ts" timestamp="2025-09-01T03:58:02.844Z" hostname="Neils-MacBook-Air.local" tests="7" failures="0" errors="0" skipped="0" time="0.026392875">
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a key is in skipUnmappedKeys, when addUpdatesTrack is called, should not add the track" time="0.01200575">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given no previous track, when addUpdatesTrack is called with &quot;added&quot;, should add the track as &quot;added&quot;" time="0.004961542">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a previous track of &quot;added&quot;, when addUpdatesTrack is called with &quot;updated&quot;, should keep the track as &quot;added&quot;" time="0.001497875">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a previous track of &quot;added&quot;, when addUpdatesTrack is called with &quot;deleted&quot;, should remove the track" time="0.000956291">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a previous track of &quot;deleted&quot;, when addUpdatesTrack is called with &quot;added&quot;, should change the track to &quot;updated&quot;" time="0.001623">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a previous track of &quot;updated&quot;, when addUpdatesTrack is called with &quot;deleted&quot;, should change the track to &quot;deleted&quot;" time="0.000597666">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given an objectMap without an updates property, when addUpdatesTrack is called, should create it and add the track" time="0.0011725">
        </testcase>
    </testsuite>
    <testsuite name="hooks/useExportOptions.test.ts" timestamp="2025-09-01T03:58:02.845Z" hostname="Neils-MacBook-Air.local" tests="3" failures="0" errors="0" skipped="0" time="0.030350334">
        <testcase classname="hooks/useExportOptions.test.ts" name="useExportOptions &gt; Given no processors, should return default options" time="0.009339625">
        </testcase>
        <testcase classname="hooks/useExportOptions.test.ts" name="useExportOptions &gt; Given valid processors, should return default and processor options" time="0.014955083">
        </testcase>
        <testcase classname="hooks/useExportOptions.test.ts" name="useExportOptions &gt; Given invalid processors, should filter out invalid processor options" time="0.002898">
            <system-err>
Invalid processor data: { name: [32m&apos;&apos;[39m, str_id: [32m&apos;&apos;[39m, processor: [32m&apos;&apos;[39m }

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="utils/TestResultParser.test.ts" timestamp="2025-09-01T03:58:02.845Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.010039542">
        <testcase classname="utils/TestResultParser.test.ts" name="ParserFactory &gt; should return GeminiParser for AIModel.GEMINI" time="0.001410875">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="ParserFactory &gt; should return DefaultParser for other models" time="0.000616916">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="TestResultParser &gt; should parse GEMINI result correctly" time="0.000846917">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="TestResultParser &gt; should parse default (non-GEMINI) result correctly" time="0.*********">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="TestResultParser &gt; should return error object if result has error" time="0.*********">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="TestResultParser &gt; should return [] if parse fails" time="0.*********">
            <system-err>
Error parsing default result: SyntaxError: Unexpected token &apos;o&apos;, &quot;not a json&quot; is not valid JSON
    at JSON.parse (&lt;anonymous&gt;)
    at DefaultParser.parse [90m(/Users/<USER>/Documents/repos/fintary/web/[39msrc/utils/TestResultParser.ts:44:19[90m)[39m
    at Function.parseResults [90m(/Users/<USER>/Documents/repos/fintary/web/[39msrc/utils/TestResultParser.ts:100:46[90m)[39m
    at [90m/Users/<USER>/Documents/repos/fintary/web/[39msrc/utils/TestResultParser.test.ts:110:37
    at file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:155:11
    at file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:752:26
    at file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1897:20
    at new Promise (&lt;anonymous&gt;)
    at runWithTimeout (file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1863:10)
    at runTest (file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1574:12)

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="components/DialogHost/DialogHost.test.tsx" timestamp="2025-09-01T03:58:02.846Z" hostname="Neils-MacBook-Air.local" tests="3" failures="0" errors="0" skipped="0" time="0.042652458">
        <testcase classname="components/DialogHost/DialogHost.test.tsx" name="DialogHost &gt; renders without crashing" time="0.021908625">
        </testcase>
        <testcase classname="components/DialogHost/DialogHost.test.tsx" name="DialogHost &gt; should not render any dialog if user is present" time="0.013756041">
        </testcase>
        <testcase classname="components/DialogHost/DialogHost.test.tsx" name="DialogHost &gt; should render email verification dialog if user is present and it&apos;s email is not verified" time="0.006072375">
        </testcase>
    </testsuite>
    <testsuite name="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" timestamp="2025-09-01T03:58:02.846Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="0" time="0.176044084">
        <testcase classname="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" name="SignUpDialog &gt; Rendering and Initial State &gt; renders without crashing" time="0.096911542">
        </testcase>
        <testcase classname="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" name="SignUpDialog &gt; Rendering and Initial State &gt; renders the email address" time="0.025514834">
        </testcase>
        <testcase classname="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" name="SignUpDialog &gt; Email Verification &gt; calls sendEmailVerification when button is clicked" time="0.023026625">
        </testcase>
        <testcase classname="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" name="SignUpDialog &gt; Email Verification &gt; displays snackbar message when email verification is sent" time="0.0282915">
        </testcase>
    </testsuite>
    <testsuite name="components/EmptyState/EmptyState.test.tsx" timestamp="2025-09-01T03:58:02.846Z" hostname="Neils-MacBook-Air.local" tests="1" failures="0" errors="0" skipped="0" time="0.021620416">
        <testcase classname="components/EmptyState/EmptyState.test.tsx" name="renders without crashing" time="0.020916875">
        </testcase>
    </testsuite>
    <testsuite name="components/ErrorBoundary/ErrorBoundary.test.tsx" timestamp="2025-09-01T03:58:02.846Z" hostname="Neils-MacBook-Air.local" tests="1" failures="0" errors="0" skipped="0" time="0.009987834">
        <testcase classname="components/ErrorBoundary/ErrorBoundary.test.tsx" name="renders without crashing" time="0.009117541">
        </testcase>
    </testsuite>
    <testsuite name="components/Loader/Loader.test.tsx" timestamp="2025-09-01T03:58:02.847Z" hostname="Neils-MacBook-Air.local" tests="1" failures="0" errors="0" skipped="0" time="0.057709875">
        <testcase classname="components/Loader/Loader.test.tsx" name="renders without crashing" time="0.056617667">
        </testcase>
    </testsuite>
    <testsuite name="components/NotFoundPage/NotFoundPage.test.tsx" timestamp="2025-09-01T03:58:02.847Z" hostname="Neils-MacBook-Air.local" tests="1" failures="0" errors="0" skipped="0" time="0.024841125">
        <testcase classname="components/NotFoundPage/NotFoundPage.test.tsx" name="renders without crashing" time="0.02432675">
            <system-err>
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="components/SignInUp/SignInUp.test.tsx" timestamp="2025-09-01T03:58:02.847Z" hostname="Neils-MacBook-Air.local" tests="9" failures="0" errors="0" skipped="0" time="1.584023792">
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignIn View &gt; Given the component is rendered without query parameters, should display the sign in view by default" time="0.267854958">
            <system-err>
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

            </system-err>
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignIn View &gt; Given an email is provided as a query parameter, should pre-fill the email input field" time="0.0449055">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignIn View &gt; Given the user is on the sign in view, when the &quot;Don&apos;t have an account?&quot; link is clicked, should switch to the sign up view" time="0.*********">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignIn View &gt; Given a valid email and password, when the sign in button is clicked, should call the signIn service and navigate on success" time="0.*********">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given the loginType query parameter is &quot;signUp&quot;, should display the sign up view" time="0.*********">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given an invalid invite code is submitted, should display an error message" time="0.*********">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given a valid invite code is submitted, should display the full sign up form" time="0.093345791">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given the sign up form is submitted with valid data, should call the signUp service and display a success message" time="0.659951125">
            <system-out>
[36m&lt;body&gt;[39m
  [36m&lt;div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;MuiBox-root css-lx8lnm&quot;[39m
    [36m&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation3 css-1szhjxn-MuiPaper-root&quot;[39m
        [33mstyle[39m=[32m&quot;--Paper-shadow: 0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12);&quot;[39m
      [36m&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;MuiBox-root css-1fig7wu&quot;[39m
        [36m&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;MuiBox-root css-co4xce&quot;[39m
          [36m&gt;[39m
            [36m&lt;img[39m
              [33malt[39m=[32m&quot;Fintary Logo&quot;[39m
              [33mclass[39m=[32m&quot;MuiBox-root css-1wsuzl7&quot;[39m
              [33msrc[39m=[32m&quot;/logo192.png&quot;[39m
            [36m/&gt;[39m
            [36m&lt;h1[39m
              [33mclass[39m=[32m&quot;MuiTypography-root MuiTypography-h4 css-17nmnue-MuiTypography-root&quot;[39m
            [36m&gt;[39m
              [0mFintary[0m
            [36m&lt;/h1&gt;[39m
            [36m&lt;h6[39m
              [33mclass[39m=[32m&quot;MuiTypography-root MuiTypography-subtitle1 css-1a7pzqr-MuiTypography-root&quot;[39m
            [36m&gt;[39m
              [0mOne place for all your financial operations[0m
            [36m&lt;/h6&gt;[39m
          [36m&lt;/div&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;MuiBox-root css-w7552a&quot;[39m
          [36m&gt;[39m
            [36m&lt;div[39m
              [33mclass[39m=[32m&quot;MuiBox-root css-9zybzw&quot;[39m
              [33mstyle[39m=[32m&quot;-webkit-transform: none; transform: none; transition: transform 225ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;&quot;[39m
            [36m&gt;[39m
              [36m&lt;div[39m
                [33mclass[39m=[32m&quot;MuiBox-root css-1e8180r&quot;[39m
              [36m&gt;[39m
                [36m&lt;button[39m
                  [33mclass[39m=[32m&quot;MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-fullWidth MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-fullWidth css-15e9p2l-MuiButtonBase-root-MuiButton-root&quot;[39m
                  [33mtabindex[39m=[32m&quot;0&quot;[39m
                  [33mtype[39m=[32m&quot;button&quot;[39m
                [36m&gt;[39m
                  [36m&lt;span[39m
                    [33mclass[39m=[32m&quot;MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-1sh91j5-MuiButton-startIcon&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;svg[39m
                      [33maria-hidden[39m=[32m&quot;true&quot;[39m
                      [33mclass[39m=[32m&quot;MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root&quot;[39m
                      [33mdata-testid[39m=[32m&quot;GoogleIcon&quot;[39m
                      [33mfocusable[39m=[32m&quot;false&quot;[39m
                      [33mviewBox[39m=[32m&quot;0 0 24 24&quot;[39m
                    [36m&gt;[39m
                      [36m&lt;path[39m
                        [33md[39m=[32m&quot;M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z&quot;[39m
                      [36m/&gt;[39m
                    [36m&lt;/svg&gt;[39m
                  [36m&lt;/span&gt;[39m
                  [0mSign up with Google[0m
                [36m&lt;/button&gt;[39m
                [36m&lt;div[39m
                  [33maria-orientation[39m=[32m&quot;horizontal&quot;[39m
                  [33mclass[39m=[32m&quot;MuiDivider-root MuiDivider-fullWidth MuiDivider-withChildren css-7bs9ws-MuiDivider-root&quot;[39m
                  [33mrole[39m=[32m&quot;separator&quot;[39m
                [36m&gt;[39m
                  [36m&lt;span[39m
                    [33mclass[39m=[32m&quot;MuiDivider-wrapper css-1134oje-MuiDivider-wrapper&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;p[39m
                      [33mclass[39m=[32m&quot;MuiTypography-root MuiTypography-body2 css-1wle3ir-MuiTypography-root&quot;[39m
                    [36m&gt;[39m
                      [0mOr sign up with e-mail[0m
                    [36m&lt;/p&gt;[39m
                  [36m&lt;/span&gt;[39m
                [36m&lt;/div&gt;[39m
                [36m&lt;form[39m
                  [33mclass[39m=[32m&quot;MuiBox-root css-0&quot;[39m
                  [33mnovalidate[39m=[32m&quot;&quot;[39m
                [36m&gt;[39m
                  [36m&lt;div[39m
                    [33mclass[39m=[32m&quot;MuiFormControl-root MuiFormControl-marginDense MuiFormControl-fullWidth MuiTextField-root css-xanwcj-MuiFormControl-root-MuiTextField-root&quot;[39m
                    [33mdata-testid[39m=[32m&quot;email-address&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;label[39m
                      [33mclass[39m=[32m&quot;MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-outlined MuiFormLabel-colorPrimary Mui-focused Mui-required MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-outlined css-113d811-MuiFormLabel-root-MuiInputLabel-root&quot;[39m
                      [33mdata-shrink[39m=[32m&quot;true&quot;[39m
                      [33mfor[39m=[32m&quot;emailAddress&quot;[39m
                      [33mid[39m=[32m&quot;emailAddress-label&quot;[39m
                    [36m&gt;[39m
                      [0mEmail address[0m
                      [36m&lt;span[39m
                        [33maria-hidden[39m=[32m&quot;true&quot;[39m
                        [33mclass[39m=[32m&quot;MuiFormLabel-asterisk MuiInputLabel-asterisk css-1ljffdk-MuiFormLabel-asterisk&quot;[39m
                      [36m&gt;[39m
                        [0m [0m
                        [0m*[0m
                      [36m&lt;/span&gt;[39m
                    [36m&lt;/label&gt;[39m
                    [36m&lt;div[39m
                      [33mclass[39m=[32m&quot;MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth Mui-focused MuiInputBase-formControl css-1blp12k-MuiInputBase-root-MuiOutlinedInput-root&quot;[39m
                    [36m&gt;[39m
                      [36m&lt;input[39m
                        [33maria-invalid[39m=[32m&quot;false&quot;[39m
                        [33mautocomplete[39m=[32m&quot;email&quot;[39m
                        [33mclass[39m=[32m&quot;MuiInputBase-input MuiOutlinedInput-input css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input&quot;[39m
                        [33mid[39m=[32m&quot;emailAddress&quot;[39m
                        [33mname[39m=[32m&quot;emailAddress&quot;[39m
                        [33mrequired[39m=[32m&quot;&quot;[39m
                        [33mtype[39m=[32m&quot;text&quot;[39m
                        [33mvalue[39m=[32m&quot;&quot;[39...

            </system-out>
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given the user is on the sign up view, when the &quot;Already have an account?&quot; link is clicked, should switch to the sign in view" time="0.*********">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/useDeleteAndCopyActions.test.ts" timestamp="2025-09-01T03:58:02.847Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.*********">
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given no flags enabled, should return base actions" time="0.016262">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given create copy action is enabled, should add create copy action" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given delete action is enabled, should add delete action" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given create copy action is clicked, should call setDefaultData and setSearchParam" time="0.001852084">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given delete action is clicked, should set deleteIds and showDelConfirm" time="0.002217875">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given setShowDelConfirm and setDeleteIds are called, should update state correctly" time="0.000707833">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/ActionPicker.test.tsx" timestamp="2025-09-01T03:58:02.848Z" hostname="Neils-MacBook-Air.local" tests="9" failures="0" errors="0" skipped="0" time="0.002303791">
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given optionSelected is undefined, should return undefined" time="0.000671917">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given optionSelected is null, should return undefined" time="0.000169625">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of objects and optionSelected matches id, should return matching object" time="0.000272375">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of objects and optionSelected does not match any id, should return optionSelected as string" time="0.000125375">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of primitives and optionSelected matches, should return optionSelected" time="0.0000535">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of primitives and optionSelected does not match, should return optionSelected" time="0.000052125">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is empty array, should return undefined if optionSelected is falsy" time="0.00003875">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of objects with numeric ids and optionSelected is a number, should return matching object" time="0.00009775">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of objects with numeric ids and optionSelected does not match, should return optionSelected" time="0.000190083">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/MultiSelect.test.tsx" timestamp="2025-09-01T03:58:02.848Z" hostname="Neils-MacBook-Air.local" tests="12" failures="0" errors="0" skipped="0" time="1.492649208">
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should render without crashing" time="0.099549542">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should render the label correctly" time="0.039365709">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should handle selection of items" time="0.178140917">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should filter items based on search query" time="0.117709334">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should handle &quot;All&quot; selection" time="0.059736083">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should handle pagination" time="0.444895">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should render without crashing" time="0.010104291">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should render the label correctly" time="0.019372375">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should handle selection of items" time="0.017201875">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should filter items based on search query" time="0.030738667">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should handle &quot;All&quot; selection" time="0.024855791">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should handle pagination" time="0.448968834">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/MultiSelectV2.test.tsx" timestamp="2025-09-01T03:58:02.848Z" hostname="Neils-MacBook-Air.local" tests="13" failures="0" errors="0" skipped="0" time="1.37462475">
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; renders correctly with default props" time="0.222875042">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; displays string[] options correctly" time="0.237922167">
            <system-err>
Each child in a list should have a unique &quot;key&quot; prop.

Check the render method of `ForwardRef(Autocomplete)`. See https://react.dev/link/warning-keys for more information.

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; displays object options correctly" time="0.117273292">
            <system-err>
Each child in a list should have a unique &quot;key&quot; prop.

Check the render method of `ul`. It was passed a child from ForwardRef(Autocomplete). See https://react.dev/link/warning-keys for more information.

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles pagination correctly" time="0.153959417">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles pagination for object options correctly" time="0.279852542">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; displays loading state correctly" time="0.019084667">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles disabled prop correctly" time="0.012048958">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; renders custom options correctly" time="0.032089125">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles selection changes correctly" time="0.048899833">
            <system-err>
A props object containing a &quot;key&quot; prop is being spread into JSX:
  let props = {key: someKey, label: ..., className: ..., disabled: ..., data-tag-index: ..., tabIndex: ..., onDelete: ...};
  &lt;ForwardRef(Chip) {...props} /&gt;
React keys must be passed directly to JSX without using spread:
  let props = {label: ..., className: ..., disabled: ..., data-tag-index: ..., tabIndex: ..., onDelete: ...};
  &lt;ForwardRef(Chip) key={someKey} {...props} /&gt;

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles selection changes for object options correctly" time="0.07014875">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles custom formatter and valuer for object options correctly" time="0.04577925">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles single selection mode correctly" time="0.074192167">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; calls renderCustomOption with selected value and available options" time="0.058561333">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/StringArrayFieldMatcher.test.tsx" timestamp="2025-09-01T03:58:02.849Z" hostname="Neils-MacBook-Air.local" tests="11" failures="0" errors="0" skipped="0" time="0.402637416">
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Renders correctly with empty initial values" time="0.0722765">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Enables Add button when input has value" time="0.033791416">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Adds a new value when clicking Add button" time="0.038017834">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Adds a new value when pressing Enter" time="0.044364166">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Displays existing values as chips" time="0.038192375">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Deletes a value when chip delete button is clicked" time="0.0458815">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Parses array values correctly" time="0.009240458">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Handles empty strings properly" time="0.053862917">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Trims whitespace from added values" time="0.026271458">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Does not add empty values" time="0.017083084">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Appends new values to existing values" time="0.022023209">
        </testcase>
    </testsuite>
    <testsuite name="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" timestamp="2025-09-01T03:58:02.850Z" hostname="Neils-MacBook-Air.local" tests="7" failures="0" errors="0" skipped="0" time="0.048048917">
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given useAdditionalActions is called, should return getAdditionalActions function" time="0.013355042">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given getAdditionalActions is called, should return actions with correct ids" time="0.004353792">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given statuses are checked, should enable &quot;reconcile&quot; action only for correct statuses" time="0.010621458">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given &quot;reconcile&quot; action is clicked, should call setSelectedStatment and setShowReconcile" time="0.008279375">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given &quot;update_policy_payout_rates&quot; action is clicked, should call mutateAsync and showSnackbar on success" time="0.00733625">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given mutateAsync fails, should show error snackbar" time="0.000979416">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given mutateAsync fails with non-Error instance, should show error snackbar with stringified error" time="0.001737333">
        </testcase>
    </testsuite>
    <testsuite name="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" timestamp="2025-09-01T03:58:02.850Z" hostname="Neils-MacBook-Air.local" tests="16" failures="0" errors="0" skipped="0" time="0.032272583">
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given an agent with a corresponding config, then it should remove the agent, its config, and update the total" time="0.015410375">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given unit is rate, then it removes an agent and config and update total" time="0.001399083">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given config is undefined, then it should remove the agent and update the total" time="0.001380042">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given updates is undefined, then it should remove the agent and initialize updates" time="0.000830667">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given existing updates, then it should add a new deleted update without modifying previous values" time="0.001773">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given the new value is the same as the old one, then it should not update anything" time="0.000655583">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given unit is amount, then it should update the agent value, the total, and the updates tracker" time="0.000595333">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given unit is rate, then it should update the agent value, the total, and the updates tracker" time="0.000508125">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given an invalid new value, then it should update the agent value but not the total" time="0.00207075">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given existing updates, then it should add a new updated status without modifying previous values" time="0.001188125">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given an agent with a corresponding config, then it should remove the agent, its config, and update the total" time="0.001305833">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given unit is rate, then it removes an agent and config and update total" time="0.001181375">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given config is undefined, then it should remove the agent and update the total" time="0.000991917">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given updates is undefined, then it should remove the agent and initialize updates" time="0.000561166">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given existing updates, then it should add a new deleted update without modifying previous values" time="0.000377125">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given deleted data and receives new updates for same agent, then it should change the type from deleted to updated" time="0.00036425">
        </testcase>
    </testsuite>
    <testsuite name="components/CommissionsDataView/hooks/useCreatePolicy.test.ts" timestamp="2025-09-01T03:58:02.851Z" hostname="Neils-MacBook-Air.local" tests="2" failures="0" errors="0" skipped="0" time="0.019161583">
        <testcase classname="components/CommissionsDataView/hooks/useCreatePolicy.test.ts" name="useCreatePolicy &gt; Given handleCreatePolicy is called, should navigate with correct state" time="0.01404">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCreatePolicy.test.ts" name="useCreatePolicy &gt; Given commissionData has missing optional fields, should not throw" time="0.0042585">
        </testcase>
    </testsuite>
    <testsuite name="components/CommissionsDataView/hooks/useDataField.test.ts" timestamp="2025-09-01T03:58:02.851Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.040735">
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; setNewFieldData &gt; given a simple field, should update the data row with the new value" time="0.012485542">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; setNewFieldData &gt; given a field that updates an object map &gt; should update the nested values correctly" time="0.002594375">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; setNewFieldData &gt; given a field that updates an object map &gt; should update and keep the existing metadata fields from nested values" time="0.006310125">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; getFieldData &gt; given a simple field, should return the direct value from the data row" time="0.006643667">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; getFieldData &gt; given a field that reads from an object map, should extract and return a primitive map" time="0.004970333">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; getFieldData &gt; given data is not available for the field, should return an empty object" time="0.005243125">
        </testcase>
    </testsuite>
    <testsuite name="views/ProcessorPlayground/hooks/useImageManager.test.tsx" timestamp="2025-09-01T03:58:02.851Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="0" time="0.035864375">
        <testcase classname="views/ProcessorPlayground/hooks/useImageManager.test.tsx" name="useImageManager &gt; createBlobUrl stores URL for cleanup" time="0.016444541">
        </testcase>
        <testcase classname="views/ProcessorPlayground/hooks/useImageManager.test.tsx" name="useImageManager &gt; processImage returns UploadedImage with blob URL" time="0.006963375">
        </testcase>
        <testcase classname="views/ProcessorPlayground/hooks/useImageManager.test.tsx" name="useImageManager &gt; processImage returns error if downloadFile fails" time="0.007868208">
            <system-err>
Failed to process image 2: Error: fail
    at [90m/Users/<USER>/Documents/repos/fintary/web/[39msrc/views/ProcessorPlayground/hooks/useImageManager.test.tsx:50:40
    at file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:155:11
    at file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:752:26
    at file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1897:20
    at new Promise (&lt;anonymous&gt;)
    at runWithTimeout (file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1863:10)
    at runTest (file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1574:12)
    at runSuite (file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1729:8)
    at runSuite (file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1729:8)
    at runFiles (file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1787:3)

            </system-err>
        </testcase>
        <testcase classname="views/ProcessorPlayground/hooks/useImageManager.test.tsx" name="useImageManager &gt; removeImage revokes URL and removes image from array" time="0.002998875">
        </testcase>
    </testsuite>
    <testsuite name="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" timestamp="2025-09-01T03:58:02.852Z" hostname="Neils-MacBook-Air.local" tests="11" failures="0" errors="0" skipped="0" time="0.*********">
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders green check and formatted amount when all amounts match" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders green check and formatted amount when total commission and statement match, bank is NaN" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders green check and formatted amount when total commission and bank match, statement is NaN" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders nothing when all amounts are NaN" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders cross icon and all available amounts when they do not match" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders cross icon and both available amounts when statement and bank are present but total commission is missing" time="0.002019">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders info icon and only available amount when only one amount is present" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders info icon and only available amount when only statement amount is present" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders info icon and only available amount when only bank amount is present" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders statement amount for non-PROCESSED status" time="0.000899">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders nothing for non-PROCESSED status and NaN statement amount" time="0.*********">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" timestamp="2025-09-01T03:58:02.852Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.*********">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given initial state, should reset state" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given qc param is missing, should do nothing" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given chip is not found, should do nothing" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given chip has addBtnLabel and hideAddSelect, should set them" time="0.00503025">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given add mode, should set new data and fields" time="0.00269225">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given edit mode, should set fields" time="0.*********">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" timestamp="2025-09-01T03:58:02.852Z" hostname="Neils-MacBook-Air.local" tests="5" failures="0" errors="0" skipped="0" time="0.02089575">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters and fieldOptions are undefined, should not call setFilterList" time="0.008827">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters with _date_start and _date_end, should build filter list" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters with multi-select array, should build filter list" time="0.001991041">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters is undefined, should build filter list from fieldOptions" time="0.002387583">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters with empty arrays, should ignore them" time="0.003502209">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" timestamp="2025-09-01T03:58:02.853Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="0" time="0.015400667">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" name="useDisplayFields &gt; Given no saved fields in localStorage, should use defaultValue" time="0.010713333">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" name="useDisplayFields &gt; Given saved fields in localStorage, should use them" time="0.001662125">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" name="useDisplayFields &gt; Given localStorage returns empty string, should not use saved fields" time="0.001264916">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" name="useDisplayFields &gt; Given localStorage returns only commas, should not use saved fields" time="0.000760333">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useIsMobile.test.ts" timestamp="2025-09-01T03:58:02.853Z" hostname="Neils-MacBook-Air.local" tests="3" failures="0" errors="0" skipped="0" time="0.017811166">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useIsMobile.test.ts" name="useIsMobile &gt; Given media query matches, should return isMobile as true" time="0.010352583">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useIsMobile.test.ts" name="useIsMobile &gt; Given media query does not match, should return isMobile as false" time="0.002140875">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useIsMobile.test.ts" name="useIsMobile &gt; Given useMediaQuery is called, should use correct query" time="0.003627917">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useParams.test.ts" timestamp="2025-09-01T03:58:02.854Z" hostname="Neils-MacBook-Air.local" tests="5" failures="0" errors="0" skipped="0" time="0.064172875">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given searchParams and setSearchParams, should return them" time="0.018964542">
            <system-err>
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given new params, should update searchParams" time="0.007160917">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given null or undefined params, should delete them" time="0.014234541">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given other params, should keep them unchanged" time="0.002849792">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given setSearchParams is called directly, should update searchParams" time="0.016145209">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" timestamp="2025-09-01T03:58:02.854Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="0" time="0.057344292">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" name="useSearchSettings &gt; Given table is report_data, should return correct settings" time="0.012829708">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" name="useSearchSettings &gt; Given table is reconciliation_data, should return correct settings" time="0.007062417">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" name="useSearchSettings &gt; Given table is statement_data, should return correct settings" time="0.035501375">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" name="useSearchSettings &gt; Given table is unknown, should return empty array" time="0.001036">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/EnhancedSelect/utils/search.test.ts" timestamp="2025-09-01T03:58:02.854Z" hostname="Neils-MacBook-Air.local" tests="10" failures="0" errors="0" skipped="0" time="0.003225125">
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; returns empty array if search is empty" time="0.000957334">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs quoted search (exact substring, case-insensitive)" time="0.000339291">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs quoted search with spaces" time="0.000195625">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs tokenize search with multiple tokens" time="0.00019725">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs tokenize search with comma and whitespace" time="0.00008275">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs simple substring search when tokenizeSearch is false" time="0.000086959">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; returns empty array if no match" time="0.000056334">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; handles case-insensitive search" time="0.000115208">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; handles quoted search with only quotes" time="0.00022725">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; handles tokenizeSearch with empty tokens" time="0.000068542">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" timestamp="2025-09-01T03:58:02.854Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.421614542">
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given MoreDateFilters component, should render IconButton and title" time="0.0829095">
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given IconButton click, should open Popover" time="0.125160708">
            <system-err>
React does not recognize the `endAdornment` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `endadornment` instead. If you accidentally passed it from a parent component, remove it from the DOM element.

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given value is &quot;true&quot;, should render checked Checkbox" time="0.077464208">
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given Checkbox is toggled, should call onSetValue" time="0.073325291">
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given missing checkbox filter, should handle gracefully" time="0.045911166">
            <system-err>
Filters are missing required fields:  {
  filters: [
    { filterKey: [32m&apos;startDate&apos;[39m, label: [32m&apos;start date&apos;[39m, type: [32m&apos;date&apos;[39m },
    { filterKey: [32m&apos;endDate&apos;[39m, label: [32m&apos;end date&apos;[39m, type: [32m&apos;date&apos;[39m }
  ]
}
Filters are missing required fields:  {
  filters: [
    { filterKey: [32m&apos;startDate&apos;[39m, label: [32m&apos;start date&apos;[39m, type: [32m&apos;date&apos;[39m },
    { filterKey: [32m&apos;endDate&apos;[39m, label: [32m&apos;end date&apos;[39m, type: [32m&apos;date&apos;[39m }
  ]
}

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given missing date fields, should handle gracefully" time="0.015789583">
            <system-err>
Filters are missing required fields:  {
  filters: [
    {
      filterKey: [32m&apos;includeBlanks&apos;[39m,
      label: [32m&apos;Include blanks&apos;[39m,
      type: [32m&apos;boolean&apos;[39m
    }
  ]
}
Filters are missing required fields:  {
  filters: [
    {
      filterKey: [32m&apos;includeBlanks&apos;[39m,
      label: [32m&apos;Include blanks&apos;[39m,
      type: [32m&apos;boolean&apos;[39m
    }
  ]
}

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="components/ToolsPage/components/comp-grid/hooks/useCompGridParse.test.ts" timestamp="2025-09-01T03:58:02.855Z" hostname="Neils-MacBook-Air.local" tests="7" failures="0" errors="0" skipped="0" time="0.0296765">
        <testcase classname="components/ToolsPage/components/comp-grid/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; selectedParser &gt; should return the selected parser from search params" time="0.0147355">
        </testcase>
        <testcase classname="components/ToolsPage/components/comp-grid/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; selectedParser &gt; should default to TransGlobal if account is TRANSGLOBAL and no parser is set" time="0.*********">
        </testcase>
        <testcase classname="components/ToolsPage/components/comp-grid/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; selectedParser &gt; should default to Fintary if account is not TRANSGLOBAL and no parser is set" time="0.*********">
        </testcase>
        <testcase classname="components/ToolsPage/components/comp-grid/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; availableParsers &gt; should return both Fintary and TransGlobal if account is TRANSGLOBAL" time="0.*********">
        </testcase>
        <testcase classname="components/ToolsPage/components/comp-grid/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; availableParsers &gt; should return only Fintary if account is not TRANSGLOBAL" time="0.********">
        </testcase>
        <testcase classname="components/ToolsPage/components/comp-grid/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; handleSelectParser &gt; should set the parser in search params when called" time="0.*********">
        </testcase>
        <testcase classname="components/ToolsPage/components/comp-grid/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; handleSelectParser &gt; should remove the parser from search params if called with empty string" time="0.*********">
        </testcase>
    </testsuite>
    <testsuite name="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" timestamp="2025-09-01T03:58:02.855Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="4" time="0">
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" name="useTransactionUpdates &gt; when updateTransactions is called &gt; Given new transactions are added, should update transactions and pagination correctly" time="0">
            <skipped/>
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" name="useTransactionUpdates &gt; when updateTransactions is called &gt; Given transactions are removed, should update transactions and pagination correctly" time="0">
            <skipped/>
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" name="useTransactionUpdates &gt; when updateTransactions is called &gt; Given all transactions are removed, should update transactions and pagination correctly" time="0">
            <skipped/>
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" name="useTransactionUpdates &gt; when updateTransactions is called &gt; Given the number of transactions is unchanged, should update transactions but keep totalItems the same" time="0">
            <skipped/>
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" timestamp="2025-09-01T03:58:02.855Z" hostname="Neils-MacBook-Air.local" tests="8" failures="0" errors="0" skipped="0" time="0.660386">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given bookmark icon button, should render it" time="0.038922208">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given params exist, should show tooltip with correct message" time="0.179831042">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given params are empty, should show tooltip with correct message" time="0.025847625">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given popover is opened, should allow entering view name" time="0.115998584">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given onSave is called, should show snackbar on success" time="0.097281959">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given onSave is called, should show snackbar on error" time="0.136157792">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given table prop, should compute correct page" time="0.025527">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given unknown table, should compute empty page" time="0.038901541">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" timestamp="2025-09-01T03:58:02.856Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="0" time="0.054342959">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" name="FilterDateRange &gt; Given date range picker, should render it" time="0.017903792">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" name="FilterDateRange &gt; Given dateFilters are provided, should render MoreDateFilters" time="0.028007375">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" name="FilterDateRange &gt; Given isMobile is true, should render with mobile styles" time="0.005640625">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" name="FilterDateRange &gt; Given empty dateFilters, should not render MoreDateFilters" time="0.001755708">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" timestamp="2025-09-01T03:58:02.856Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.156050292">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given no chips, should render nothing" time="0.019694334">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given chips, should render all visible chips" time="0.086067792">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given admin chip and user is admin, should show admin chip with lock" time="0.021722333">
            <system-out>
[36m&lt;body&gt;[39m
  [36m&lt;div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;MuiBox-root css-i44mvr&quot;[39m
    [36m&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiButtonBase-root MuiChip-root MuiChip-filled MuiChip-sizeMedium MuiChip-colorPrimary MuiChip-clickable MuiChip-clickableColorPrimary MuiChip-filledPrimary css-17ql1vv-MuiButtonBase-root-MuiChip-root&quot;[39m
        [33mrole[39m=[32m&quot;button&quot;[39m
        [33mtabindex[39m=[32m&quot;0&quot;[39m
      [36m&gt;[39m
        [36m&lt;span[39m
          [33mclass[39m=[32m&quot;MuiChip-label MuiChip-labelMedium css-1dybbl5-MuiChip-label&quot;[39m
        [36m&gt;[39m
          [0mAll[0m
        [36m&lt;/span&gt;[39m
      [36m&lt;/div&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-5rfr0w-MuiButtonBase-root-MuiChip-root&quot;[39m
        [33mdata-testid[39m=[32m&quot;foo&quot;[39m
        [33mrole[39m=[32m&quot;button&quot;[39m
        [33mtabindex[39m=[32m&quot;0&quot;[39m
      [36m&gt;[39m
        [36m&lt;span[39m
          [33mclass[39m=[32m&quot;MuiChip-label MuiChip-labelMedium css-16cgrcw-MuiChip-label&quot;[39m
        [36m&gt;[39m
          [0mFoo[0m
        [36m&lt;/span&gt;[39m
      [36m&lt;/div&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-5rfr0w-MuiButtonBase-root-MuiChip-root&quot;[39m
        [33mrole[39m=[32m&quot;button&quot;[39m
        [33mtabindex[39m=[32m&quot;0&quot;[39m
      [36m&gt;[39m
        [36m&lt;span[39m
          [33mclass[39m=[32m&quot;MuiChip-label MuiChip-labelMedium css-16cgrcw-MuiChip-label&quot;[39m
        [36m&gt;[39m
          [0mBar[0m
        [36m&lt;/span&gt;[39m
      [36m&lt;/div&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-5rfr0w-MuiButtonBase-root-MuiChip-root&quot;[39m
        [33mrole[39m=[32m&quot;button&quot;[39m
        [33mtabindex[39m=[32m&quot;0&quot;[39m
      [36m&gt;[39m
        [36m&lt;span[39m
          [33mclass[39m=[32m&quot;MuiChip-label MuiChip-labelMedium css-16cgrcw-MuiChip-label&quot;[39m
        [36m&gt;[39m
          [0mAdmin 🔒[0m
        [36m&lt;/span&gt;[39m
      [36m&lt;/div&gt;[39m
    [36m&lt;/div&gt;[39m
  [36m&lt;/div&gt;[39m
[36m&lt;/body&gt;[39m

            </system-out>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given admin chip and user is not admin, should not show admin chip" time="0.005015459">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given chip with more=true, should render MoreMenu" time="0.014767792">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given chip is clicked, should call setSearchParams" time="0.007519209">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" timestamp="2025-09-01T03:58:02.856Z" hostname="Neils-MacBook-Air.local" tests="5" failures="0" errors="0" skipped="0" time="0.283488125">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given filters with object and string options, should render filter selects" time="0.106471084">
            <system-err>
React does not recognize the `enableActiveColor` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `enableactivecolor` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `enableSearch` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `enablesearch` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `tokenizeSearch` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `tokenizesearch` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `listContainerSx` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `listcontainersx` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `renderLabel` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `renderlabel` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `renderValue` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `rendervalue` instead. If you accidentally passed it from a parent component, remove it from the DOM element.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given filters and params exist, should show reset button" time="0.071528709">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given clear filters button is clicked, should call setSearchParams" time="0.083191916">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given default sorting, should sort filters by label" time="0.011476833">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given sortFilterByPosition is enabled, should sort filters by sortPosition" time="0.0094825">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" timestamp="2025-09-01T03:58:02.856Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.122301542">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given FilterQueryChips, FilterDateRange, and EnhancedDataViewFilterSelect, should render them" time="0.052092667">
            <system-err>
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given FilterNumberRange, should render it when numberRangeFilters provided" time="0.0083095">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given navigation icons, should render left and right icons" time="0.018880459">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given navigation icons are clicked, should call scrollFilter" time="0.021969541">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given enableResetFilters and sortFilterByPosition, should pass them to EnhancedDataViewFilterSelect" time="0.008730375">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given scroll event, should call setIsFilterScrollable" time="0.010268">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" timestamp="2025-09-01T03:58:02.857Z" hostname="Neils-MacBook-Air.local" tests="11" failures="0" errors="0" skipped="0" time="0.266567167">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given default state, should render fields select and add button" time="0.078807">
            <system-err>
The `value` prop supplied to &lt;select&gt; must be an array if `multiple` is true.

Check the render method of `EnhancedSelect`.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given hideAddSelect is true, should not render add button" time="0.005671083">
            <system-err>
The `value` prop supplied to &lt;select&gt; must be an array if `multiple` is true.

Check the render method of `EnhancedSelect`.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given default state, should render export button" time="0.005717333">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given enableSaves is true, should render bookmark" time="0.004074041">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type button, should render as button" time="0.059454958">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type select, should render as select" time="0.0488005">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type date, should render as date" time="0.006530208">
            <system-err>
React does not recognize the `setValue` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `setvalue` instead. If you accidentally passed it from a parent component, remove it from the DOM element.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type dateRange, should render as dateRange" time="0.006758542">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type multiSelect, should render as multiSelect" time="0.018499875">
            <system-err>
React does not recognize the `selectedValues` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `selectedvalues` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `setSelectedValues` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `setselectedvalues` instead. If you accidentally passed it from a parent component, remove it from the DOM element.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given hideExport is true, should not render export button" time="0.017652208">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given fields select changes, should call setFieldsStorage" time="0.012274625">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" timestamp="2025-09-01T03:58:02.857Z" hostname="Neils-MacBook-Air.local" tests="5" failures="0" errors="0" skipped="0" time="0.123629625">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given default props, should render label and SearchBox" time="0.046955209">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given tabbed variant, should not render label" time="0.014846125">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given searchSettings is not empty, should render SearchSettings" time="0.008217958">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given isMobile true, should set flexWrap to wrap" time="0.044516834">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given isMobile false, should set flexWrap to nowrap" time="0.007365333">
        </testcase>
    </testsuite>
    <testsuite name="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" timestamp="2025-09-01T03:58:02.858Z" hostname="Neils-MacBook-Air.local" tests="17" failures="0" errors="0" skipped="0" time="0.008802334">
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; should parse the input to a valid amount correctly for &apos;0.45&apos;" time="0.003714084">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; should parse the input to a valid amount correctly for &apos;10.999&apos;" time="0.000801833">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; should parse the input to a valid amount correctly for &apos;50.05954&apos;" time="0.000302875">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; should parse the input to a valid amount correctly for &apos;12005&apos;" time="0.000271167">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: abc" time="0.00024575">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 123..45" time="0.00008">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: --123" time="0.00004675">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 12.34.56" time="0.000087833">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 12.00.00" time="0.000217125">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 12a3" time="0.000062208">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 1,23,4.56" time="0.000041625">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 12,34" time="0.000036458">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: ." time="0.000079208">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: -" time="0.000033042">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs:  " time="0.000028791">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: null" time="0.000026125">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: undefined" time="0.000025459">
        </testcase>
    </testsuite>
    <testsuite name="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" timestamp="2025-09-01T03:58:02.858Z" hostname="Neils-MacBook-Air.local" tests="34" failures="0" errors="0" skipped="1" time="0.006727083">
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should remove the transaction detail for the found transaction in the transactions list by target input" time="0.001344708">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should not change the transactions input when the transaction or transaction detail is not found" time="0.00031625">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should handle cases where the transaction exists but the detail does not" time="0.000116333">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should handle cases where the transaction has no details" time="0.000077959">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should return a new transactions array and not mutate the original input" time="0.000591083">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionById &gt; Should remove the transaction with the given ID from the transactions list" time="0.000174042">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionById &gt; Should not modify the transactions list if the transaction ID is not found" time="0.000070166">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionById &gt; Should return a new transactions array and not mutate the original input" time="0.000140042">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionById &gt; Should handle an empty transactions list gracefully" time="0.000181625">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should return updated and deleted transactions based on updatedRows and removedRows" time="0.000206125">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should return only updated transactions when no rows are removed" time="0.000075417">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should return only deleted transactions when no rows are updated" time="0.000060916">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should return empty arrays when there are no updated or removed rows" time="0.000051292">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should handle cases where updatedRows reference non-existent transactions" time="0.000050917">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should handle cases where removedRows reference non-existent transactions" time="0">
            <skipped/>
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should return transactions that are not settled" time="0.000101792">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should return an empty array if all transactions are settled" time="0.000041292">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should return transactions with filtered details if some details are settled" time="0.000173375">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should handle an empty transactions list gracefully" time="0.000037916">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should return transactions unchanged if none are settled" time="0.000052917">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="addNewTransactionDetail &gt; Should add a new transaction detail to the specified transaction" time="0.00007725">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="addNewTransactionDetail &gt; Should not modify transactions if the transaction ID is not found" time="0.000040333">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="addNewTransactionDetail &gt; Should return a new transactions array and not mutate the original input" time="0.000100709">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="addNewTransactionDetail &gt; Should handle an empty transactions list gracefully" time="0.000037667">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should update the specified transaction detail in the given transaction" time="0.000253583">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should update the transaction amout with a sum of transaction details amount" time="0.000214209">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should not modify transactions if the transaction ID is not found" time="0.000051292">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should not modify transactions if the transaction detail ID is not found" time="0.000065167">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should return a new transactions array and not mutate the original input" time="0.000080041">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should handle an empty transactions list gracefully" time="0.000048125">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransaction &gt; Should update the specified transaction with the provided updates" time="0.000201167">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransaction &gt; Should not modify transactions if the transaction ID is not found" time="0.000081542">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransaction &gt; Should return a new transactions array and not mutate the original input" time="0.000140542">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransaction &gt; Should handle an empty transactions list gracefully" time="0.000179917">
        </testcase>
    </testsuite>
</testsuites>
