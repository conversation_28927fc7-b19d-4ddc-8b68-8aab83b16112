import { getEnvVariable } from '@/env';
import { CssBaseline, Snackbar, ThemeProvider } from '@mui/material';
import { StyledEngineProvider, createTheme } from '@mui/material/styles';
import { CacheProvider } from '@emotion/react';
import * as Sentry from '@sentry/react';
import TawkMessengerReact from '@tawk.to/tawk-messenger-react';
import { TRANSGLOBAL_SIGNIN_URL } from 'common/customer/customer.constants';
import { useEffect, useRef, useState } from 'react';
import { hotjar } from 'react-hotjar';
import { RouterProvider } from 'react-router-dom';
import { StatsigProvider } from 'statsig-react';
import { ResponseAction } from 'common/constants';
import type { AuthResponse } from 'common/types/login';

import { AuthGate } from './AuthGate';
import DialogHost from '@/components/DialogHost';
import ErrorBoundary from '@/components/ErrorBoundary';
import LoadingMask from '@/components/atoms/LoadingMask';
import GlobalSnackbar from '@/components/atoms/Snackbar';
import { LOCAL_STORAGE_KEYS } from '@/constants/account';
import LoadingProvider from '@/contexts/LoadingContext';
import GlobalSnackbarProvider from '@/contexts/SnackbarContext';
import UiStateProvider from '@/contexts/UIStateProvider';
import useSnackbar from '@/contexts/useSnackbar';
import { useUserInfo } from '@/hooks/useUserInfo';
import API from '@/services/API';
import appearance from '@/services/appearance';
import { useAuth } from '@/services/useAuth';
import { useAccountStore, useFeVersionStore } from '@/store';
import useLoginStore from '@/store/loginStore';
import { GlobalStateCodes, Roles } from '@/types';
import Bar from '@/views/Bar';
import Router from '@/views/Router';
import { createEmotionCache } from '@/utils/emotion-cache';
import { useUpdateSelectedAccount } from './hooks/useUpdateSelectedAccount';
import { useUpdateUserRole } from './hooks/useUpdateUserRole';
import { useSessionMonitor } from './hooks/useSessionMonitor';
import TermsOfService from '@/components/terms-of-service/components/TermsOfService';

const baseTheme = appearance.defaultTheme;

const theme = createTheme({
  ...baseTheme,
});

const initialState: AppState = {
  ready: false,
  performingAction: false,
  userData: null,
  roles: [],
  defaultLandingPage: '/',
  emailVerificationDialog: {
    open: false,
  },
  snackbar: {
    autoHideDuration: 0,
    message: '',
    open: false,
  },
};

const App = () => {
  // Create emotion cache with nonce support
  const emotionCache = createEmotionCache();

  const [user, setUser] = useState<User | null>(null);
  const [authError, setAuthError] = useState<string | null>(null);
  const [loginEmail, setLoginEmail] = useState<string>('');
  const [isSessionExpired, setIsSessionExpired] = useState(false);
  const setLoginParam = useLoginStore((s) => s.setLoginParam);

  // Custom hook for handle authentication
  const { signOut, signOutExistingUser } = useAuth(
    setUser,
    setLoginEmail,
    setAuthError
  );
  const { showCountdown } = useSessionMonitor();
  const statsigKey = getEnvVariable({
    name: 'STATSIG_API_KEY',
    defaultValue: '',
  });
  const environment = getEnvVariable({ name: 'ENVIRONMENT', defaultValue: '' });
  const [state, setState] = useState<AppState>(initialState);
  const [showUpdateMessage, setShowUpdateMessage] = useState(false);
  const [hasSignedOut, setHasSignedOut] = useState(false);
  const tawkMessengerRef = useRef<{
    hideWidget?: () => void;
    maximize?: () => void;
  } | null>(null);
  const handleTawkHide = () => {
    if (tawkMessengerRef?.current?.hideWidget)
      tawkMessengerRef.current.hideWidget();
  };
  const handleTawkMaximize = () => {
    if (tawkMessengerRef?.current?.maximize)
      tawkMessengerRef.current.maximize();
  };

  const { data: loginResponse } = useUserInfo();

  const { feVersion, setFeVersion } = useFeVersionStore();
  const { showSnackbar } = useSnackbar();
  const {
    selectedAccount,
    setSelectedAccount,
    setUserState,
    resetAccountStore,
  } = useAccountStore();

  const authHandlerPoster = API.getMutation('auth_handler', 'POST');

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    setTimeout(() => {
      setShowUpdateMessage(false);
    }, 5000);
  }, [showUpdateMessage]);

  useEffect(() => {
    if (
      feVersion &&
      feVersion.stateCode === GlobalStateCodes.FE_OUT_OF_DATE &&
      !showUpdateMessage
    ) {
      if (feVersion.message !== null) {
        window.confirm(feVersion.message);
      }
      setFeVersion({
        stateCode: null,
        message: null,
      });
      setShowUpdateMessage(true);
    }
    if (
      feVersion &&
      feVersion.stateCode === GlobalStateCodes.FE_INCOMPATIBLE &&
      !showUpdateMessage
    ) {
      if (feVersion.message !== null) {
        window.confirm(feVersion.message);
        window.location.reload();
      }
      setFeVersion({
        stateCode: null,
        message: null,
      });
      setShowUpdateMessage(true);
    }
  }, [feVersion, setFeVersion, showUpdateMessage]);

  useEffect(() => {
    // Reload all application tabs after a logout
    const handleStorageEvent = (event: StorageEvent) => {
      if (['loggedOut', 'triggerReload'].includes(event.key || '')) {
        window.location.reload();
      }
    };
    window.addEventListener('storage', handleStorageEvent);

    //   // Listen on session expired event
    const handleSessionExpired = () => {
      showSnackbar('Session expired, please sign in again', 'error');
      signOutExistingUser(() => {
        localStorage.clear();
        window.location.reload();
      });
      setIsSessionExpired(true);
    };

    window.addEventListener('sessionExpired', handleSessionExpired, {
      once: true,
    });

    // Cleanup listener on component unmount
    return () => {
      window.removeEventListener('storage', handleStorageEvent);
      // Window.removeEventListener('sessionExpired', handleSessionExpired);
    };
  }, [showSnackbar, signOutExistingUser]);

  useUpdateSelectedAccount();
  useUpdateUserRole();

  useEffect(() => {
    const isLoggedIn = user !== null;

    if (authError) {
      setState((prevState) => ({
        ...prevState,
        ready: true,
        user: null,
        roles: [],
      }));
      showSnackbar(authError, 'error');
      return;
    }

    const userCheck = async () => {
      const impUser = JSON.parse(
        localStorage.getItem('customLoginUser') ?? '{}'
      );

      try {
        if (isLoggedIn && Object.keys(impUser).length > 0) {
          setLoginParam({
            email: impUser.email,
            isImpUser: true,
            role_name: impUser.role_name,
            sso: impUser.sso,
          });
        } else if (isLoggedIn && user) {
          setLoginParam({
            email: user.email,
            role_name: user.role_name,
            sso: user.sso,
          });
        } else if (!user) {
          setLoginParam({ email: '' });
        }
      } catch (error) {
        console.error('An error occurred:', error);
        Sentry.captureException(error);
      }
    };

    if (isLoggedIn && user) {
      if (process.env.NODE_ENV === 'production') {
        hotjar.initialize({ id: 3472012, sv: 6 });
        setTimeout(() => {
          if (hotjar.initialized()) {
            hotjar.identify(user.uid, {
              userUid: user.uid,
            });
          }
        }, 2000);
      }
    } else {
      setState((prevState) => ({
        ...prevState,
        ready: true,
        performingAction: false,
      }));
    }

    userCheck();
  }, [user, authError, showSnackbar, setLoginParam]);

  const openDialog = (dialogId: keyof AppState): void => {
    const dialog = state[dialogId];

    if (!dialog || dialog.open === undefined || null) {
      return;
    }

    dialog.open = true;

    setState((prevState) => ({
      ...prevState,
      dialog,
    }));
  };

  const closeDialog = (dialogId: keyof AppState): void => {
    const dialog = state[dialogId];

    if (!dialog || dialog.open === undefined || null) {
      return;
    }

    dialog.open = false;

    setState((prevState) => ({
      ...prevState,
      dialog,
    }));
  };

  const closeDialogCallback = (
    dialogId: keyof AppState,
    callback?: () => void
  ) => {
    closeDialog(dialogId);
    if (callback && typeof callback === 'function') {
      callback();
    }
  };

  // Sign In or Sign Up dialog based on query params
  const queryParams = new URLSearchParams(window.location.search);
  const loginType = queryParams.get('login-type');
  const userLoginEmail = queryParams.get('email');

  const issuer = queryParams.get('issuer');
  const token = queryParams.get('token');
  const role_name = queryParams.get('role_name');
  const ssoToken = localStorage.getItem(LOCAL_STORAGE_KEYS.ssoToken);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const verifyToken = async (issuer, token) => {
      const response = await authHandlerPoster.mutateAsync({
        issuer,
        token,
      });
      if (response.valid) {
        signOutExistingUser(() => {
          localStorage.clear();
          setSelectedAccount(null);
          setState((prevState) => ({
            ...prevState,
            ready: true,
            performingAction: false,
            userData: null,
            roles: [],
          }));
          setUser({
            ...response.user,
            sso: true,
            role_assigned: role_name,
          });
          localStorage.setItem(
            LOCAL_STORAGE_KEYS.ssoToken,
            token ? `sso:${token}` : ''
          );
        });
      } else {
        showSnackbar(
          'Unable to log in, invalid token, redirecting to login page',
          'info'
        );
        setTimeout(() => {
          if (issuer === 'transglobal')
            window.location.href = TRANSGLOBAL_SIGNIN_URL;
        }, 2000);
      }
    };

    if (issuer && token) {
      verifyToken(issuer, token);
    } else if (ssoToken) {
      const token = ssoToken.split(':')[1];
      verifyToken('transglobal', token);
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Jan/2025
     * MISSED REFs: 'authHandlerPoster', 'setSelectedAccount', 'showSnackbar', and 'signOutExistingUser'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [issuer, role_name, token, ssoToken]);

  useEffect(() => {
    setState((prev) => ({
      ...prev,
      user,
    }));
  }, [user]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const setUserAndAccount = async (userLogin: AuthResponse) => {
      setUserState({
        userOverallState: userLogin.userOverallState,
        userEmail: userLogin.userEmail,
        userOnboardingNeeded: userLogin.userOnboardingNeeded,
        accountOnboardingNeeded: userLogin.accountOnboardingNeeded,
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        userAccounts: userLogin.userAccounts as any,
      });

      setState((prevState) => ({
        ...prevState,
        defaultLandingPage: getDefaulLandingPage(userLogin.userLandingPage),
        ready: true,
      }));
    };

    const getDefaulLandingPage = (userLandingPage) => {
      if (!userLandingPage) {
        return '/reconciliation';
      }
      if (Array.isArray(userLandingPage)) {
        return userLandingPage[0].default_page;
      }
      if (typeof userLandingPage === 'string') {
        return userLandingPage;
      }
      return '/reconciliation';
    };

    const checkResult = () => {
      if (!loginResponse || !user) {
        showSnackbar('Not authorized', 'error');
        return;
      }

      const isUserEmailVerified =
        'emailVerified' in user ? user.emailVerified : true;

      if (loginResponse.action === ResponseAction.LOG_OUT) {
        signOut(() => {
          localStorage.clear();
          setSelectedAccount(null);
          setState((prevState) => ({
            ...prevState,
            ready: true,
            performingAction: false,
            userData: null,
            roles: [],
          }));
          showSnackbar('You have been signed out', 'info');
        });
        return;
      }
      if (
        loginResponse.userAccounts[0]?.role_id !== Roles.ACCOUNT_ADMIN &&
        !isUserEmailVerified
      ) {
        openDialog('emailVerificationDialog');
        return;
      }

      setUserAndAccount(loginResponse);
    };

    checkResult();
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * SINCE:  Nov/2024
     * MISSED REFs:  'openDialog', 'setSelectedAccount', and 'signOut'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loginResponse, setUserState, showSnackbar, user]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const signOutWhenInvitingUser = () => {
      if (!user) setHasSignedOut(true);
      if (user && !hasSignedOut) {
        signOut(() => {
          localStorage.clear();
          setSelectedAccount(null);
          setState((prevState) => ({
            ...prevState,
            ready: true,
            performingAction: false,
            userData: null,
            roles: [],
          }));
          showSnackbar('You have been signed out', 'info');
          setHasSignedOut(true);
        });
      }
    };

    const handleDialogOpen = (dialogType) => {
      setLoginEmail(userLoginEmail || '');
      setTimeout(() => {
        signOutWhenInvitingUser();
        openDialog(dialogType);
      }, 1000);
    };

    if (loginType === 'signUp') {
      handleDialogOpen('signUpDialog');
    }
    if (loginType === 'signIn') {
      handleDialogOpen('signInDialog');
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * SINCE:  April 2024
     * MISSED REFs:  'openDialog', 'setSelectedAccount', 'showSnackbar', and 'signOut'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, hasSignedOut, loginType, userLoginEmail]);

  const closeSnackbar = (clearMessage: boolean = false): void => {
    setState((prevState) => ({
      ...prevState,
      snackbar: {
        autoHideDuration: 10,
        message: clearMessage ? '' : prevState.snackbar.message,
        open: false,
      },
    }));
  };

  const {
    ready,
    roles,
    emailVerificationDialog,
    snackbar,
    defaultLandingPage,
  } = state;
  const statsigUser = {
    userID: selectedAccount?.accountId || '',
    email: user?.email || '',
  };
  const userInfo = {
    name: selectedAccount?.accountName ?? '',
    email: user?.email ?? '',
  };

  const onSignOut = () => {
    setState((prevState) => ({
      ...prevState,
      performingAction: true,
    }));
    signOut(() => {
      localStorage.clear();
      resetAccountStore();
      setState((prevState) => ({
        ...prevState,
        ready: true,
        performingAction: false,
        userData: null,
        roles: [],
      }));
      showSnackbar('Signed out', 'info');
      localStorage.setItem('triggerReload', Date.now().toString());
    });
  };

  const emptyTawkCallback = () => {
    // Tawk required callback - intentionally empty
  };

  return (
    <CacheProvider value={emotionCache}>
      <ErrorBoundary user={userInfo}>
        <LoadingProvider>
          <GlobalSnackbarProvider>
            <StatsigProvider
              sdkKey={statsigKey}
              options={{
                environment: { tier: environment },
              }}
              waitForInitialization={true}
              user={statsigUser}
            >
              <StyledEngineProvider injectFirst>
                <ThemeProvider theme={theme}>
                  <UiStateProvider>
                    <CssBaseline />
                    <AuthGate>
                      {ready && defaultLandingPage && (
                        <>
                          <RouterProvider
                            router={Router({
                              user,
                              roles,
                              bar: (
                                <Bar
                                  user={user}
                                  onSignOutClick={onSignOut}
                                  onHelpClick={handleTawkMaximize}
                                />
                              ),
                              defaultLandingPage: defaultLandingPage,
                            })}
                          />
                          <DialogHost
                            theme={theme}
                            user={user}
                            dialogs={{
                              emailVerificationDialog: {
                                dialogProps: {
                                  open: emailVerificationDialog.open,
                                  email: loginEmail,
                                  onClose: (callback) => {
                                    closeDialogCallback(
                                      'emailVerificationDialog',
                                      callback
                                    );
                                  },
                                },
                              },
                            }}
                          />
                          <Snackbar
                            autoHideDuration={snackbar.autoHideDuration}
                            message={`Session will be expired in 1 minute without interaction `}
                            open={showCountdown}
                            anchorOrigin={{
                              vertical: 'top',
                              horizontal: 'center',
                            }}
                          ></Snackbar>
                          <Snackbar
                            autoHideDuration={snackbar.autoHideDuration}
                            message={'Session expired, please sign in again'}
                            open={isSessionExpired}
                            anchorOrigin={{
                              vertical: 'top',
                              horizontal: 'center',
                            }}
                          ></Snackbar>
                          {!isSessionExpired && (
                            <Snackbar
                              autoHideDuration={snackbar.autoHideDuration}
                              message={
                                typeof snackbar.message === 'string'
                                  ? snackbar.message
                                  : ''
                              }
                              open={snackbar.open}
                              onClose={() => closeSnackbar()}
                              anchorOrigin={{
                                vertical: 'bottom',
                                horizontal: 'center',
                              }}
                            >
                              {typeof snackbar.message !== 'string'
                                ? snackbar.message
                                : undefined}
                            </Snackbar>
                          )}
                          <TawkMessengerReact
                            propertyId="6626e6281ec1082f04e5ad21"
                            widgetId="1hs3v63th"
                            ref={tawkMessengerRef}
                            autoStart={false}
                            onLoad={handleTawkHide}
                            onBeforeLoad={emptyTawkCallback}
                            onStatusChange={emptyTawkCallback}
                            onChatHidden={emptyTawkCallback}
                            onChatMaximized={emptyTawkCallback}
                            onChatMinimized={emptyTawkCallback}
                            tawkOnChatStarted={emptyTawkCallback}
                            tawkOnChatEnded={emptyTawkCallback}
                            tawkOnChatMessageVisitor={emptyTawkCallback}
                            tawkOnChatMessageAgent={emptyTawkCallback}
                            tawkOnChatMessageSystem={emptyTawkCallback}
                            tawkOnPrechatSubmit={emptyTawkCallback}
                            tawkOnOfflineSubmit={emptyTawkCallback}
                            tawkOnAgentJoinChat={emptyTawkCallback}
                            tawkOnAgentLeaveChat={emptyTawkCallback}
                            tawkOnUnreadCountChanged={emptyTawkCallback}
                            tawkOnTagsUpdated={emptyTawkCallback}
                            customStyle={{
                              zIndex: 1200,
                              visibility: {
                                desktop: {
                                  yOffset: 48,
                                  position: 'br',
                                },
                                mobile: {
                                  position: 'br',
                                },
                              },
                            }}
                          />
                        </>
                      )}
                    </AuthGate>
                  </UiStateProvider>
                  <LoadingMask />
                  <GlobalSnackbar />
                  <TermsOfService onSignOut={onSignOut} />
                </ThemeProvider>
              </StyledEngineProvider>
            </StatsigProvider>
          </GlobalSnackbarProvider>
        </LoadingProvider>
      </ErrorBoundary>
    </CacheProvider>
  );
};

export default App;
