import { javascript } from '@codemirror/lang-javascript';
import {
  History,
  PlayArrow,
  Search,
  Lightbulb,
  EditNote,
} from '@mui/icons-material';
import {
  Box,
  Chip,
  FormControl,
  IconButton,
  InputAdornment,
  Popover,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
} from '@mui/material';
import CodeMirror from '@uiw/react-codemirror';
import { Allotment } from 'allotment';
import 'allotment/dist/style.css';
import { toolDesc } from 'common/tools';
import { useMemo, useState } from 'react';

import ToolDesc from './ToolDesc';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import { codeTheme } from './config';
import ResultTable from './ResultTable';
import { DescriptionButton } from './processorDescription';

export interface EditorPaneProps {
  showHistory: () => void;
  execCode: () => void;
  currentCode: string;
  setCurrentCode: (code: string) => void;
  processorTemplateList: { label: string; value: string }[];
  handleFabClick: () => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  currentResult: any;
  isReportProcessor?: boolean;
  onDescriptionClick: () => void;
  hasDescription: boolean;
}

export const EditorPane = ({
  showHistory,
  execCode,
  currentCode,
  setCurrentCode,
  processorTemplateList,
  handleFabClick,
  currentResult,
  isReportProcessor = false,
  onDescriptionClick,
  hasDescription,
}: EditorPaneProps) => {
  const [anchorEl, setAnchorEl] = useState<
    (EventTarget & HTMLButtonElement) | null
  >();
  const [resultTableSearch, setResultTableSearch] = useState('');
  const [showJson, setShowJson] = useState(false);

  const enhancedSelectorStyle = {
    minWidth: 125,
    width: 'auto',
    '& .MuiBox-root': {
      maxWidth: 'calc(100% - 24px)',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
    },
  };

  const options = processorTemplateList.map((item) => ({
    id: item.value,
    label: item.label,
  }));

  const selectedTemplate = useMemo(() => {
    return processorTemplateList.find((item) => item.value === currentCode);
  }, [processorTemplateList, currentCode]);

  const formattedResult = useMemo(() => {
    return typeof currentResult !== 'string'
      ? JSON.stringify(currentResult, null, 2)
      : currentResult;
  }, [currentResult]);

  return (
    <Box
      sx={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'scroll',
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(14, 165, 233, 0.05)',
        border: '1px solid #60a5fa',
        ml: 3,
      }}
    >
      <Box className="absolute top-2 right-4 z-10 flex gap-1">
        <Tooltip title="Tool library">
          <IconButton
            color="primary"
            onClick={(e) => setAnchorEl(e.currentTarget)}
          >
            <Lightbulb />
          </IconButton>
        </Tooltip>
        <Popover
          id="p"
          open={!!anchorEl}
          anchorEl={anchorEl}
          onClose={() => setAnchorEl(null)}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
        >
          <ToolDesc toolDesc={toolDesc}></ToolDesc>
        </Popover>
        <Tooltip title="Show history">
          <IconButton color="primary" onClick={showHistory}>
            <History />
          </IconButton>
        </Tooltip>
        <DescriptionButton
          onClick={onDescriptionClick}
          hasDescription={hasDescription}
        />
        <Tooltip title="Update log">
          <IconButton color="primary" onClick={handleFabClick}>
            <EditNote />
          </IconButton>
        </Tooltip>
        <IconButton color="primary" onClick={execCode}>
          <PlayArrow />
        </IconButton>

        {!isReportProcessor && (
          <FormControl>
            <EnhancedSelect
              placeholder="Template"
              sx={enhancedSelectorStyle}
              options={options}
              value={{
                id: currentCode || '',
                label: selectedTemplate?.label || '',
              }}
              onChange={(value) => setCurrentCode(value.id)}
            />
          </FormControl>
        )}
      </Box>

      <Allotment defaultSizes={[75, 25]} vertical>
        <Allotment.Pane>
          <Box
            sx={{
              width: '100%',
              height: '100%',
              overflow: 'auto',
              backgroundColor: 'rgba(14, 165, 233, 0.05)',
              border: '1px solid #60a5fa',
              fontSize: '0.875rem',
            }}
          >
            <CodeMirror
              theme={codeTheme}
              height="100%"
              value={currentCode}
              width="100%"
              extensions={[javascript({ jsx: true })]}
              onChange={setCurrentCode}
            />
          </Box>
        </Allotment.Pane>
        <Allotment.Pane>
          <Box
            sx={{
              width: '100%',
              overflow: 'auto',
              backgroundColor: 'rgba(14, 165, 233, 0.5)',
              border: '1px solid #60a5fa',
              mt: 3,
              fontSize: '0.875rem',
              height: 'calc(100% - 12.5px)',
            }}
          >
            {currentResult && typeof currentResult !== 'string' && (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  height: '100%',
                  width: '100%',
                  px: 1,
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    width: '100%',
                    mt: 1,
                    mb: 1,
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      flex: 1,
                      gap: 1,
                    }}
                  >
                    <ToggleButtonGroup
                      color="primary"
                      value={showJson ? 'json' : 'table'}
                      exclusive
                      onChange={(_, newValue) => {
                        setShowJson(newValue === 'json');
                      }}
                      aria-label="Preview options"
                    >
                      <ToggleButton value="json">JSON</ToggleButton>
                      <ToggleButton value="table">Table</ToggleButton>
                    </ToggleButtonGroup>
                    <Chip
                      label={`Rows: ${currentResult?.data?.length ?? 'n/a'}`}
                    />
                    <Box
                      sx={{
                        flex: 1,
                        display: 'flex',
                        justifyContent: 'flex-end',
                      }}
                    >
                      <TextField
                        size="small"
                        placeholder="Search..."
                        onChange={(e) => setResultTableSearch(e.target.value)}
                        sx={{
                          width: '200px',
                          '& .MuiOutlinedInput-root': {
                            borderRadius: '20px',
                            height: '32px',
                          },
                          '& .MuiInputLabel-root': {
                            lineHeight: '1em',
                          },
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Search />
                            </InputAdornment>
                          ),
                        }}
                      />
                    </Box>
                  </Box>
                </Box>
                <Box
                  sx={{
                    flex: 1,
                    height: '100%',
                    width: '100%',
                  }}
                >
                  {!showJson ? (
                    <ResultTable
                      headers={currentResult.fields}
                      rows={currentResult.data}
                      resultTableSearch={resultTableSearch}
                    />
                  ) : (
                    <Box
                      sx={{
                        flex: 1,
                        height: '100%',
                        width: '100%',
                        overflow: 'auto',
                      }}
                    >
                      <CodeMirror
                        value={formattedResult}
                        width="100%"
                        height="100%"
                        readOnly={true}
                        editable={false}
                        basicSetup={{
                          lineNumbers: false,
                          foldGutter: false,
                        }}
                        extensions={[javascript({ jsx: true })]}
                      />
                    </Box>
                  )}
                </Box>
              </Box>
            )}

            {typeof currentResult === 'string' && <Box>{currentResult}</Box>}
          </Box>
        </Allotment.Pane>
      </Allotment>
    </Box>
  );
};
