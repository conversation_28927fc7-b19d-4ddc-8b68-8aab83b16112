import {
  Box,
  Button,
  Dialog,
  DialogA<PERSON>,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Typography,
} from '@mui/material';
import { useContext, useEffect, useRef, useState } from 'react';
import { Close } from '@mui/icons-material';

import ProcessorPlayground from './ProcessorPlayground';
import type { IProcessorModalProps } from '.';
import { LoadingContext } from '@/contexts/LoadingContext';
import API from '@/services/API';

const ProcessorModal = ({
  open,
  handleCancel,
  rowData,
  isReportProcessor = false,
}: IProcessorModalProps) => {
  const [uploading, setUploading] = useState(false);
  const [showSavingMsg, setShowSavingMsg] = useState(false);
  const { data: documents, isLoading } = API.getBasicQuery('documents');

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const playgroundRef = useRef<any>(null);
  const { setLoadingConfig } = useContext(LoadingContext);

  useEffect(() => {
    setLoadingConfig({
      loading: isLoading,
      message: 'Loading...',
    });
  }, [isLoading, setLoadingConfig]);

  const modalTitle = isReportProcessor
    ? 'Report processor editor'
    : 'Document processor editor';

  const onSubmit = async () => {
    setUploading(true);
    const result = await (
      playgroundRef.current as unknown as { submit: () => Promise<boolean> }
    )?.submit();
    setUploading(false);
    if (result) {
      handleCancel(undefined);
    }
  };

  return (
    <Dialog
      open={open}
      fullScreen
      sx={{ background: 'transparent', p: 1 }}
      onClose={handleCancel}
      disableEscapeKeyDown
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ pl: 2 }}>
            {modalTitle}
            {showSavingMsg ? (
              <Typography
                component="span"
                variant="body2"
                sx={{ color: 'grey.500', pl: 0.5 }}
              >
                Saving to local...
              </Typography>
            ) : (
              ''
            )}
          </Box>
        </Box>
      </DialogTitle>
      <IconButton
        onClick={handleCancel}
        sx={{
          position: 'absolute',
          p: 2,
          right: 0,
          top: 0,
          cursor: 'pointer',
          '&:hover': {
            color: 'primary.main',
          },
        }}
      >
        <Close
          sx={{
            transition: 'all .15s ease-in-out',
            transformOrigin: 'center',
            '.MuiButtonBase-root:hover &': {
              transform: 'rotate(180deg)',
            },
          }}
        />
      </IconButton>
      <Divider />

      <DialogContent
        sx={{
          p: 0,
          backgroundColor: '#fff',
          borderRadius: '4px',
        }}
      >
        <ProcessorPlayground
          ref={playgroundRef}
          rowData={rowData}
          documentList={documents}
          setShowSavingMsg={setShowSavingMsg}
          isReportProcessor={isReportProcessor}
        />
      </DialogContent>

      <DialogActions sx={{ pt: 0, pb: 2, px: 2 }}>
        <Button onClick={handleCancel}>Cancel</Button>
        <Button
          onClick={onSubmit}
          loading={uploading}
          variant="contained"
          sx={{ width: '100px' }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProcessorModal;
