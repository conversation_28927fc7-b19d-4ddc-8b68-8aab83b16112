import { Box, Typography } from '@mui/material';
import { Allotment } from 'allotment';
import 'allotment/dist/style.css';
import { tool } from 'common/tools';
import localforage from 'localforage';
import {
  forwardRef,
  type Ref,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useInterval } from 'react-use';
import prettier from 'prettier/standalone';
import parserBabel from 'prettier/plugins/babel';
import parserEsTree from 'prettier/plugins/estree';

import { BasicDialog, CommentDrawer, FileDialogPreview } from '@/common';
import useCommonData from '@/components/UploadModal/processFlow/hoc/useCommonData';
import { LoadingContext } from '@/contexts/LoadingContext';
import useDownloadStorageFile from '@/contexts/useDownloadStorageFile';
import usePreviewParams from '@/contexts/usePreviewParams';
import useSnackbar from '@/contexts/useSnackbar';
import { auth } from '@/firebase';
import API from '@/services/API';
import Spreadsheet from '@/services/Spreadsheet';
import { DocumentPreviewKeys, NotesEntityTypes } from '@/types';
import HistoryDialog from '@/views/ProcessorPlayground/HistoryDialog';
import ReviewerSelector from '@/views/ProcessorPlayground/ReviewerSelector';
import {
  documentAICode,
  extractTableCode,
  htmlExtractCode,
  nanonetsCode,
  reportCode,
  spreadSheetCode,
} from '@/views/ProcessorPlayground/defaultCode';
import type {
  IDocumentModel,
  IExtractionData,
  IProcessorPlaygroundProps,
  IResultProps,
} from '.';
import { ProcessorSettings } from './ProcessorSettings';
import { ReportProcessorSettings } from './ReportProcessorSettings';
import { IncomingDataPane } from './IncomingDataPane';
import { EditorPane } from './EditorPane';
import type { Reviewer } from './types';
import { ProcessorDescriptionModal } from './processorDescription';

function ProcessorPlayground(
  {
    rowData,
    setShowSavingMsg,
    documentList,
    isReportProcessor,
  }: IProcessorPlaygroundProps,
  ref: Ref<unknown> | undefined
) {
  const [currentPreview, setCurrentPreview] = useState<
    IExtractionData | IExtractionData[] | Record<string, unknown> | undefined
  >();
  const [selectExtraction, setSelectExtraction] = useState<number | string>('');
  const [currentCode, setCurrentCode] = useState('');
  const [currentResult, setCurrentResult] = useState<IResultProps | string>('');
  const [showChangeCodeDialog, setShowChangeCodeDialog] = useState(false);
  const { setLoadingConfig } = useContext(LoadingContext);
  const [historyList, setHistoryList] = useState<
    Array<{ created_at: string; [key: string]: unknown }>
  >([]);
  const [showHistoryList, setShowHistoryList] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [reviewer, setReviewer] = useState<Reviewer>();

  const [openNotes, setOpenNotes] = useState(false);
  const [sheetList, setSheetList] = useState<string[]>([]);
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const [spreadsheetData, setSpreadsheetData] = useState<any>();
  const [showDescriptionModal, setShowDescriptionModal] = useState(false);

  const [search, setSearch] = useState('');

  const { showSnackbar } = useSnackbar();

  const reportMap = {
    'commissions-report': 'statement_data',
    'policies-report': 'report_data',
  };

  const fetchFieldsMap = {
    'commissions-report': 'statements',
    'policies-report': 'reports',
  };

  const processorTemplateList = [
    {
      label: 'Document AI',
      name: 'documentAI',
      value: documentAICode,
    },
    {
      label: 'Extract Table',
      name: 'extractTable',
      value: extractTableCode,
    },
    {
      label: 'Spreadsheet',
      name: 'spreadsheet',
      value: spreadSheetCode,
    },
    {
      label: 'Nanonets',
      name: 'nanonets',
      value: nanonetsCode,
    },
    {
      label: 'HTML Extract',
      name: 'htmlExtract',
      value: htmlExtractCode,
    },
  ];

  const type = isReportProcessor
    ? rowData?.type && rowData.type !== ''
      ? rowData.type
      : 'commissions-report'
    : (rowData?.type ?? '');

  const [formData, setFormData] = useState({
    name: '',
    type: type,
    method: undefined as string | undefined,
    company_ids: [] as string[],
    company_id: '',
    access: '',
    status: '',
    reviewer_str_id: undefined as string | undefined,
    notes: '' as string | null,
    document: '',
    sheetName: '',
    inner_name: '',
    owner: '',
    description: '' as string | null,
    stability_level: '' as string | null,
  });

  const [disableOption, setDisableOption] = useState({
    excelDocument: false,
    extractData: false,
  });

  const { showPreview, setShowPreview, previewId, setPreviewPath } =
    usePreviewParams();

  const extractPutter = API.getMutation('extractions', 'PUT');
  const processorPoster = API.getMutation('admin/processors', 'POST');
  const processorPatcher = API.getMutation('admin/processors', 'PATCH');
  const { data: owners = [] } = API.getBasicQuery('users/get_fintary_admins');

  const { data: curProcessorData, isLoading: isProcessorLoading } =
    API.getBasicQuery(`admin/processors/${rowData?.id}`, '', !!rowData?.id);

  const { data: curExtractionData, isLoading: isCurExtractionLoading } =
    isReportProcessor
      ? API.getBasicQuery(
          'export',
          [
            `orderBy=created_at`,
            `sort=desc`,
            `endpoint=${reportMap[(formData.type && formData.type.trim() !== '' ? formData.type : rowData?.type) || 'commissions-report']}`,
            `json_only=true`,
            `limit=50`,
            `page=0`,
          ].join('&')
        )
      : API.getBasicQuery(
          `extractions/${selectExtraction}`,
          '',
          !!selectExtraction
        );

  const [
    { isLoading: isCompaniesLoading, data: extractionsLists = [] },
    { isLoading: isExtractionLoading, data: companiesList = [] },
  ] = API.getBasicQueryAll(['extractions', 'companies'], '');

  const { downloadFile } = useDownloadStorageFile();

  const { fields } = useCommonData(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    fetchFieldsMap[formData.type] ?? (formData.type as any),
    null
  );

  const extractionsList = (extractionsLists as IExtractionData[]) || [];

  const spreadsheetDocuments = useMemo(() => {
    if (!documentList || !documentList.count) {
      return [];
    }
    return documentList.data
      .filter((doc) => doc?.file_type === 'spreadsheet')
      .map((doc) => {
        const { override_file_path, filename } = doc;
        if (!override_file_path) {
          doc.label = filename;
        } else {
          const _filename = override_file_path.split('/').pop();
          const [_h, ...s] = _filename?.split('-') || [];
          const file = s.join('-');
          doc.label = file;
        }
        return doc;
      });
  }, [documentList]);

  const handleFabClick = () => {
    setOpenNotes(true);
  };

  const handleDescriptionClick = () => {
    setShowDescriptionModal(true);
  };

  const handleDescriptionSave = (newDescription: string) => {
    setFormData((prev) => ({
      ...prev,
      description: newDescription,
    }));
  };

  useEffect(() => {
    if (reviewer?.uid) {
      setFormData((prev) => {
        return {
          ...prev,
          reviewer_str_id: reviewer.uid,
        };
      });
    }
  }, [reviewer]);

  useEffect(() => {
    if (rowData) {
      setSelectExtraction(rowData.extractionsid);
    } else {
      setSelectExtraction('');
    }
  }, [rowData]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    let code: string = reportCode;
    if (!rowData && (selectExtraction || formData.document)) {
      switch (formData.method) {
        case 'extractTable':
          code = extractTableCode;
          break;
        case 'documentAI':
          code = documentAICode;
          break;
        case 'nanonets':
          code = nanonetsCode;
          break;
        case 'htmlExtract':
          code = htmlExtractCode;
          break;
        case 'spreadsheet':
          code = spreadSheetCode;
          break;
        default:
          throw new Error(`Unsupported extraction method: ${formData.method}`);
      }
      if (currentCode && rowData) {
        setShowChangeCodeDialog(true);
      }
    }

    if (!currentCode || !rowData) {
      setCurrentCode((_c) => code);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.document, formData.method, rowData]);

  useEffect(() => {
    if (formData.company_id) {
      const targetCompany = companiesList?.data?.find(
        (c) => c.str_id === formData.company_id
      );
      let processorName = '';
      if (targetCompany) {
        processorName = `${targetCompany.company_name}`;
      }
      const _inner = targetCompany ? processorName : rowData?.inner_name || '';
      setFormData((prev) => {
        return {
          ...prev,
          name: rowData?.name || processorName,
          inner_name: _inner,
        };
      });
    }
  }, [
    companiesList?.data,
    formData.company_id,
    rowData?.inner_name,
    rowData?.name,
  ]);

  useInterval(() => {
    const localFn = async () => {
      const preCodeList = (await localforage.getItem('seedData')) as string;
      const seedData = {
        ...formData,
        selectExtraction,
        processor: currentCode,
        created_at: new Date().toISOString(),
      };
      const dbKey = `${selectExtraction}-${seedData.type}-${seedData.method}`;
      if (preCodeList && selectExtraction) {
        let preList = JSON.parse(preCodeList);
        if (Array.isArray(preList)) {
          localforage.removeItem('seedData');
          preList = {};
        }

        const target = preList[dbKey];
        if (target) {
          const { created_at: _c1, ...rest } = target[0];
          const { created_at: _c2, ...rest2 } = seedData;
          if (
            JSON.stringify(rest) === JSON.stringify(rest2) ||
            !seedData.processor
          ) {
            return;
          }
          preList[dbKey].unshift(seedData);
          if (preList[dbKey].length > 50) {
            preList[dbKey].pop();
          }
        } else {
          preList[dbKey] = [seedData];
        }
        setShowSavingMsg(true);
        localforage.setItem('seedData', JSON.stringify(preList));
        setTimeout(() => {
          setShowSavingMsg(false);
        }, 1000);
      } else {
        localforage.setItem(
          'seedData',
          JSON.stringify({
            [dbKey]: [seedData],
          })
        );
      }
    };
    localFn();
  }, 30000);

  const onClickShowPreview = () => {
    let fileStrId = '';
    if (selectExtraction) {
      const target = extractionsList.find(
        (item) => item.id === selectExtraction
      );
      if (target) {
        fileStrId = target.documents.str_id;
      }
    } else if (formData.document) {
      const target = spreadsheetDocuments.find(
        (item) => item.str_id === formData.document
      );
      if (target) {
        fileStrId = target.str_id;
      }
    }

    if (fileStrId) {
      setPreviewPath(fileStrId, DocumentPreviewKeys.ORIGINAL, true);
      setShowPreview(true);
    }
  };

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const setFormDataValue = useCallback((key: string, value: any) => {
    setFormData((prev) => {
      return {
        ...prev,
        [key]: value,
      };
    });
  }, []);

  const spreadsheetChange = useCallback(
    (value: IDocumentModel | string) => {
      const readFile = async (_path: string) => {
        setLoadingConfig({
          loading: true,
        });
        if (typeof value === 'string') return;
        const file = await downloadFile({
          endpoint_str_id: value.str_id,
          file_preview_type: 'original',
          endpoint: 'documents',
        });
        setLoadingConfig({
          loading: false,
        });
        const res = await Spreadsheet.loadSpreadsheet(file);
        setSpreadsheetData(res);
        const sheets = res?.getSheets();
        if (sheets?.length) {
          const _sheet = rowData?.suggest_for || sheets[0];
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          const data = res.getJson(_sheet) as any;
          setSheetList(sheets);
          setFormDataValue('sheetName', _sheet);
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          return data as any;
        }
        return null;
      };

      let docObj = value;
      if (typeof value === 'string') {
        docObj = ((spreadsheetDocuments || []) as IDocumentModel[]).find(
          (item) => item.str_id === value
        ) as IDocumentModel;
      }

      if (docObj) {
        readFile((docObj as IDocumentModel)?.file_path).then((fileData) => {
          setCurrentPreview(fileData);
        });
        setFormData((prev) => {
          return {
            ...prev,
            company_id: (docObj as IDocumentModel)?.companies?.str_id,
            type: (docObj as IDocumentModel)?.type,
            method: 'spreadsheet',
            document: (docObj as IDocumentModel).str_id,
          };
        });
        setDisableOption((prev) => {
          return {
            ...prev,
            extractData: true,
          };
        });
        setCurrentResult('');
      } else {
        console.log('not found');
      }
    },
    [
      downloadFile,
      rowData?.suggest_for,
      setFormDataValue,
      setLoadingConfig,
      spreadsheetDocuments,
    ]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const doAction = async () => {
      if (curProcessorData && curExtractionData) {
        const companyIds =
          curProcessorData.companies_processors?.map(
            (cp) => cp.company_str_id
          ) || [];
        const effectiveCompanyIds =
          companyIds.length > 0
            ? companyIds
            : [curProcessorData.company_id].filter(Boolean);

        setFormData((f) => ({
          company_id: effectiveCompanyIds[0] || '',
          company_ids: effectiveCompanyIds,
          type: f.type || curProcessorData.type,
          name: f.name || curProcessorData.name,
          access: curProcessorData.access,
          method: curProcessorData.method ?? undefined,
          status: curProcessorData.status,
          reviewer_str_id: curProcessorData.reviewer_str_id ?? undefined,
          notes: curProcessorData.notes,
          document: curProcessorData.document_str_id,
          sheetName: curProcessorData.suggest_for,
          inner_name: f.inner_name,
          owner: curProcessorData.owner,
          description: curProcessorData.description,
          stability_level: curProcessorData.stability_level,
        }));
        setCurrentCode(curProcessorData?.processor);
        if (curExtractionData) {
          if (isReportProcessor) {
            setCurrentPreview(curExtractionData || []);
          } else {
            setCurrentPreview(JSON.parse(curExtractionData?.output || ''));
          }
          setCurrentResult('');
        } else if (curProcessorData.document_str_id) {
          spreadsheetChange(curProcessorData.document_str_id);
        }
        setDisableOption({
          excelDocument: !!curExtractionData,
          extractData: !curExtractionData,
        });
      } else {
        if (!curProcessorData && curExtractionData) {
          if (isReportProcessor) {
            setCurrentPreview(curExtractionData || {});
          } else {
            let parsed:
              | IExtractionData
              | IExtractionData[]
              | Record<string, unknown> = {};
            if (
              curExtractionData?.output &&
              typeof curExtractionData.output === 'string'
            ) {
              try {
                parsed = JSON.parse(curExtractionData.output);
              } catch {
                parsed = {};
              }
            }
            setCurrentPreview(parsed);
          }
          setCurrentResult('');

          if (isReportProcessor) {
            setFormData((f) => ({
              ...f,
              company_ids: curExtractionData?.documents?.company_str_id
                ? [curExtractionData.documents?.company_str_id]
                : [],
              company_id: curExtractionData.documents?.company_str_id || '',
              method: undefined,
              name: '',
              access: 'account',
              notes: '',
              document: '',
            }));
          } else {
            setFormData((f) => ({
              ...f,
              company_ids: curExtractionData?.documents?.company_str_id
                ? [curExtractionData.documents?.company_str_id]
                : [],
              company_id: curExtractionData.documents?.company_str_id || '',
              type: curExtractionData.documents?.type || '',
              name: '',
              access: 'account',
              method: curExtractionData.method || '',
              notes: '',
              document: '',
            }));
          }
          setDisableOption((prev) => {
            return {
              ...prev,
              excelDocument: true,
            };
          });
        } else if (curProcessorData && !curExtractionData) {
          const companyIds =
            curProcessorData.companies_processors?.map(
              (cp) => cp.company_str_id
            ) || [];
          spreadsheetChange(curProcessorData.document_str_id);
          setCurrentCode(curProcessorData.processor);
          setFormData((_f) => ({
            company_ids: companyIds,
            company_id: companyIds[0] || '',
            type: curProcessorData.type,
            name: curProcessorData.name,
            access: curProcessorData.access,
            method: curProcessorData.method,
            status: curProcessorData.status,
            reviewer_str_id: curProcessorData.reviewer_str_id,
            notes: curProcessorData.notes,
            document: curProcessorData.document_str_id,
            sheetName: curProcessorData.suggest_for,
            inner_name: curProcessorData.inner_name,
            owner: curProcessorData.owner,
            description: curProcessorData.description,
            stability_level: curProcessorData.stability_level || '',
          }));
        } else if (
          !selectExtraction &&
          !formData.document &&
          !curProcessorData
        ) {
          setFormData((_f) => ({
            company_id: '',
            company_ids: [],
            type: '',
            name: '',
            access: 'account',
            method: undefined,
            status: '',
            reviewer_str_id: undefined,
            notes: '',
            document: '',
            sheetName: '',
            inner_name: '',
            owner: '',
            description: '',
            stability_level: '',
          }));
        }
      }
    };

    doAction();
    // TODO -----> @JACOB
  }, [
    curProcessorData,
    isProcessorLoading,
    curExtractionData,
    isCurExtractionLoading,
    rowData,
    selectExtraction,
    isReportProcessor,
    spreadsheetChange,
    formData.document,
  ]);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const onChangeSheetName = (e: any) => {
    const sheetName = e.target.value;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const data = spreadsheetData.getJson(sheetName) as any;
    setCurrentPreview(data);
    setFormDataValue('sheetName', e.target.value);
  };

  const execCode = useCallback(async () => {
    try {
      if (currentCode) {
        // biome-ignore lint/security/noGlobalEval: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        const resultFn = eval(`${currentCode}`);
        const formattedCode = await prettier.format(currentCode, {
          parser: 'babel',
          plugins: [parserBabel, parserEsTree],
        });
        const codeWithoutFinalColon = formattedCode.replace(/;\s*$/, '');
        setCurrentCode(codeWithoutFinalColon);
        const currentDocument = !formData.document
          ? documentList.data.find((item) => {
              return (
                item.extractions?.length &&
                item.extractions.some(
                  (extraction) => extraction.id === selectExtraction
                )
              );
            })
          : documentList.data.find((item) => {
              return item.str_id === formData.document;
            });
        const libs = {
          document: currentDocument,
          tools: tool,
        };
        const result = resultFn(currentPreview, libs);
        if (isReportProcessor) {
          const dataValues = result.map((item: object) => Object.values(item));
          setCurrentResult({
            fields: result?.[0] ? Object.keys(result[0]) : [],
            data: result?.[0] ? dataValues : [],
            version: 'report',
          });
        } else {
          setCurrentResult(result);
        }
      }
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } catch (error: any) {
      setCurrentResult(`eval error: ${error}`);
    }
  }, [
    currentCode,
    currentPreview,
    documentList?.data,
    formData.document,
    isReportProcessor,
    selectExtraction,
  ]);

  const checkValid = useCallback(() => {
    const isValid = currentCode && typeof currentResult !== 'string';

    if (isValid && currentResult?.version === 'Multiple-Spreadsheet') {
      return true;
    }

    if (!isValid) {
      showSnackbar(
        'Error: Please run the code first or check the form data',
        'error'
      );
      return false;
    }
    if (typeof currentResult === 'string' || !currentResult) {
      return true;
    }
    const resKeys = Object.keys(currentResult);
    const validKeys = ['fields', 'data', 'version', 'sheet'];
    const notValidKeys = resKeys.filter((key) => !validKeys.includes(key));
    if (notValidKeys.length) {
      showSnackbar(
        `Error: Please check the result key: [${notValidKeys.toString()}] is valid`,
        'error'
      );
      return false;
    }

    const resHeader = (currentResult as IResultProps).fields;
    const validHeaders = Object.keys(fields);
    const requiredHeaders = Object.keys(fields).filter(
      (key) => fields[key].required && fields[key].enabled
    );
    const notValidHeader = resHeader.filter(
      (item) => !validHeaders.includes(item)
    );
    if (!isReportProcessor && notValidHeader.length) {
      showSnackbar(
        `Error: Invalid headers: ${notValidHeader.join(', ')}`,
        'error'
      );
      return false;
    }

    const missingRequiredHeaders = requiredHeaders.filter(
      (item) => !resHeader.includes(item)
    );
    if (!isReportProcessor && missingRequiredHeaders.length) {
      showSnackbar(
        `Error: Missing required headers: ${missingRequiredHeaders.join(', ')}`,
        'error'
      );
    }
    return true;
  }, [currentCode, currentResult, fields, isReportProcessor, showSnackbar]);

  useEffect(() => {
    if (
      !isCompaniesLoading &&
      !isExtractionLoading &&
      currentPreview &&
      typeof currentResult !== 'string'
    ) {
      checkValid();
    }
  }, [
    checkValid,
    currentPreview,
    currentResult,
    isCompaniesLoading,
    isExtractionLoading,
  ]);

  const clearData = useCallback(() => {
    setCurrentCode('');
    setCurrentResult('');
    setCurrentPreview(undefined);
    setSelectExtraction('');
    setFormData({
      company_id: '',
      company_ids: [],
      type: '',
      name: '',
      access: '',
      method: undefined,
      status: '',
      reviewer_str_id: undefined,
      notes: '',
      document: '',
      sheetName: '',
      inner_name: '',
      owner: '',
      description: '',
      stability_level: '',
    });
    setDisableOption({
      excelDocument: false,
      extractData: false,
    });
  }, []);

  const onCloseCreateTip = async (isConfirm: boolean) => {
    const isExcelDocument = formData.method === 'spreadsheet';
    const processorParam = {
      ...formData,
      processor: currentCode,
      extractionsid: !isExcelDocument ? selectExtraction : '',
      document_str_id: formData.document,
      suggest_for: formData.sheetName,
    };

    Reflect.deleteProperty(processorParam, 'document');
    Reflect.deleteProperty(processorParam, 'company_id');

    if (!processorParam.extractionsid) {
      Reflect.deleteProperty(processorParam, 'extractionsid');
    }
    if (isConfirm) {
      const snapshotTag =
        rowData?.processor_status === 'processed'
          ? `${rowData?.str_id}::${new Date().toISOString()}`
          : `${rowData?.processor_status}::${new Date().toISOString()}`;
      const createRes = await processorPoster.mutateAsync({
        processor_status: snapshotTag,
        type: rowData?.type,
        status: rowData?.status,
        reviewer_str_id: rowData?.reviewer_str_id,
        processor: rowData?.processor,
        notes: rowData?.notes,
        name: rowData?.name,
        inner_name: rowData?.inner_name,
        file_type: rowData?.file_type !== '' ? rowData?.file_type : undefined,
        extractionsid: rowData?.extractionsid,
        extract_str_ids: rowData?.extract_str_ids,
        extract_ids: rowData?.extract_ids,
        document_str_id: rowData?.document_str_id,
        access: rowData?.access,
        method: rowData?.method || undefined,
        owner: rowData?.owner,
        suggest_for: formData.sheetName,
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any);

      if (createRes.error) {
        showSnackbar(createRes.error, 'error');
        return false;
      }

      const res = await processorPatcher.mutateAsync({
        ...processorParam,
        id: rowData?.id,
        updated_by: auth.currentUser?.uid,
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any);

      if (res.error) {
        showSnackbar(res.error, 'error');
        return false;
      }
    } else {
      clearData();
    }
    return true;
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const submit = useCallback(async () => {
    const isValid = checkValid();
    if (!isValid) {
      return false;
    }

    if (
      (!formData.company_ids || formData.company_ids.length === 0) &&
      !isReportProcessor
    ) {
      return false;
    }
    try {
      setLoadingConfig({
        loading: true,
      });

      const isExcelDocument = formData.method === 'spreadsheet';

      const processorParam = {
        ...formData,
        processor: currentCode,
        extractionsid: !isExcelDocument ? selectExtraction : '',
        document_str_id: formData.document,
        suggest_for: formData.sheetName,
      };
      Reflect.deleteProperty(processorParam, 'sheetName');
      Reflect.deleteProperty(processorParam, 'document');

      if (!processorParam.extractionsid) {
        Reflect.deleteProperty(processorParam, 'extractionsid');
      }

      if (rowData && rowData.processor_status === 'new') {
        if (
          !isReportProcessor &&
          formData.status === 'approved' &&
          auth.currentUser?.uid !== rowData?.reviewer_str_id
        ) {
          showSnackbar(
            `Error: Can't save processor status as "approved". Please ask the assigned reviewer to first approve it.`,
            'error'
          );
          return false;
        } else {
          const res = await processorPatcher.mutateAsync({
            ...processorParam,
            id: rowData.id,
            updated_by: auth.currentUser?.uid,
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          } as any);
          if (res.error) {
            showSnackbar(res.error, 'error');
            return false;
          }
        }
      } else {
        if (rowData && rowData.processor_status !== 'new') {
          const res = await onCloseCreateTip(true);
          setLoadingConfig({
            loading: false,
            delayToClose: 0,
          });
          return res;
        }
        let fileType = '';
        if (isExcelDocument) {
          fileType = 'spreadsheet';
        } else {
          fileType = curExtractionData?.documents?.file_type || undefined;
        }
        const { company_id: _company_id, ...cleanProcessorParam } =
          processorParam;
        const res = await processorPoster.mutateAsync({
          ...cleanProcessorParam,
          file_type: fileType,
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any);
        if (res.error) {
          showSnackbar(res.error, 'error');
          setLoadingConfig({
            loading: false,
            delayToClose: 1000,
          });
          return false;
        }
      }
      if (!isExcelDocument && !isReportProcessor) {
        const extractionParam = {
          output_format: 'Success',
          id: selectExtraction,
          method: formData.method,
        };
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        await extractPutter.mutateAsync(extractionParam as any);
      }
      setLoadingConfig({
        loading: false,
        delayToClose: 1000,
      });

      clearData();
      return true;
    } catch (error) {
      setLoadingConfig({
        loading: false,
        delayToClose: 1000,
      });
      showSnackbar(`Error: ${error}`, 'error');
      return false;
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Set/2023
     *
     * MISSED REFs: 'checkValid', 'clearData', 'curExtractionData?.documents?.file_type', 'extractPutter', 'isReportProcessor', 'onCloseCreateTip', 'processorPatcher', 'processorPoster', 'rowData', 'setLoadingConfig', and 'showSnackbar'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentCode, currentResult, selectExtraction, formData]);

  const showHistory = useCallback(() => {
    const localFn = async () => {
      const preCodeList = (await localforage.getItem('seedData')) as string;
      if (preCodeList) {
        const preList = JSON.parse(preCodeList);
        const dbKey = `${selectExtraction}-${formData.type}-${formData.method}`;
        const targetList = preList[dbKey];
        if (targetList?.length) {
          setHistoryList(
            targetList.sort((a, b) => a.created_at - b.created_at)
          );
          setShowHistoryList(true);
        } else {
          showSnackbar('Error: No history data for this extraction', 'error');
        }
      }
    };
    localFn();
  }, [selectExtraction, formData.type, formData.method, showSnackbar]);

  useImperativeHandle(ref, () => {
    return {
      submit,
    };
  });

  const extractionOptions = [
    ...(extractionsList || [])
      .sort((ia, ib) => +new Date(ib.created_at) - +new Date(ia.created_at))
      .map((item) => ({
        id: item.id,
        label: item,
      }))
      .filter((item) => {
        if (search) {
          if (
            item.label?.documents?.file_path
              ?.toLowerCase()
              .includes(search.toLowerCase()) ||
            item.label?.documents?.override_file_path
              ?.toLowerCase()
              .includes(search.toLowerCase())
          )
            return item;
        } else {
          return item;
        }
      }),
  ];

  return (
    <Box className="flex flex-col w-full h-full p-4 overflow-hidden">
      {isReportProcessor ? (
        <ReportProcessorSettings
          formData={formData}
          owners={owners}
          setFormDataValue={setFormDataValue}
          rowData={rowData}
          auth={auth}
          setOpenModal={setOpenModal}
        />
      ) : (
        <ProcessorSettings
          disableOption={disableOption}
          spreadsheetDocuments={spreadsheetDocuments}
          formData={formData}
          spreadsheetChange={spreadsheetChange}
          sheetList={sheetList}
          onChangeSheetName={onChangeSheetName}
          extractionsList={extractionsList}
          extractionOptions={extractionOptions}
          selectExtraction={selectExtraction}
          setSelectExtraction={setSelectExtraction}
          search={search}
          setSearch={setSearch}
          companiesList={companiesList}
          setFormData={setFormData}
          setFormDataValue={setFormDataValue}
          owners={owners}
          rowData={rowData}
          auth={auth}
          setOpenModal={setOpenModal}
        />
      )}
      <Box className="flex-1 flex">
        <Allotment defaultSizes={[25, 75]}>
          <Allotment.Pane>
            <IncomingDataPane
              disableOption={disableOption}
              curExtractionData={curExtractionData}
              onClickShowPreview={onClickShowPreview}
              currentPreview={currentPreview}
            />
          </Allotment.Pane>
          <Allotment.Pane>
            <EditorPane
              showHistory={showHistory}
              execCode={execCode}
              currentCode={currentCode}
              setCurrentCode={setCurrentCode}
              processorTemplateList={processorTemplateList}
              handleFabClick={handleFabClick}
              currentResult={currentResult}
              isReportProcessor={isReportProcessor}
              onDescriptionClick={handleDescriptionClick}
              hasDescription={!!formData.description}
            />
          </Allotment.Pane>
        </Allotment>
      </Box>
      <HistoryDialog
        setShowHistoryList={setShowHistoryList}
        showHistoryList={showHistoryList}
        historyList={historyList}
        setCurrentCode={setCurrentCode}
      />
      <ReviewerSelector
        setOpen={setOpenModal}
        open={openModal}
        setReviewer={setReviewer}
      />
      {openNotes && (
        <CommentDrawer
          open={openNotes}
          setOpen={setOpenNotes}
          type={NotesEntityTypes.PROCESSOR}
          rowData={rowData}
        />
      )}
      {showPreview && (
        <FileDialogPreview
          showPreview={showPreview}
          setShowPreview={setShowPreview}
          fileId={previewId}
        />
      )}
      {showChangeCodeDialog && (
        <BasicDialog
          title="Change processor code"
          bodyComponent={
            <Box className="flex flex-col gap-2">
              <Typography variant="body2">
                After the method is changed, we will match you with a more
                accurate parsing template. Are you sure you want to change the
                current code?
              </Typography>
              <Typography variant="body2" className="text-sky-700">
                You can also retrieve the previous code in the history.
              </Typography>
            </Box>
          }
          open={showChangeCodeDialog}
          onClose={(isConfirm) => {
            if (isConfirm) {
              const code =
                processorTemplateList.find(
                  (item) => item.name === formData.method
                )?.value || spreadSheetCode;
              setCurrentCode(code);
            }
            setShowChangeCodeDialog(false);
          }}
        />
      )}
      {showDescriptionModal && (
        <ProcessorDescriptionModal
          open={showDescriptionModal}
          onClose={() => setShowDescriptionModal(false)}
          processorStrId={rowData?.str_id}
          processorId={rowData?.id}
          initialDescription={formData.description || rowData?.description}
          onSave={handleDescriptionSave}
        />
      )}
    </Box>
  );
}

export default forwardRef(ProcessorPlayground);
