import { Box, FormControl, MenuItem, TextField } from '@mui/material';
import 'allotment/dist/style.css';
import Formatter from 'common/Formatter';
import {
  AccessTypes,
  ProcessorReviewStatuses,
  ProcessorReviewStatusesLabels,
  StabilityLevels,
  StabilityLevelsLabels,
} from 'common/globalTypes';
import { getFilenameFromPath } from 'common/helpers';
import { useEffect, useMemo } from 'react';

import { FilterSelect } from '@/common';
import { EnhancedSelect } from '@/components/molecules/EnhancedSelect';
import {
  documentAICode,
  extractTableCode,
  htmlExtractCode,
  nanonetsCode,
  spreadSheetCode,
} from '@/views/ProcessorPlayground/defaultCode';
import type { IDocumentModel, IExtractionData } from '.';

export interface ProcessorSettingsProps {
  disableOption: { excelDocument: boolean; extractData: boolean };
  spreadsheetDocuments: IDocumentModel[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  formData: any;
  spreadsheetChange: (doc: IDocumentModel | string) => void;
  sheetList: string[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  onChangeSheetName: (e: any) => void;
  extractionsList: IExtractionData[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  extractionOptions: any[];
  selectExtraction: string | number;
  setSelectExtraction: (id: string | number) => void;
  search: string;
  setSearch: (s: string) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  companiesList: { data: any[] };
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setFormData: React.Dispatch<React.SetStateAction<any>>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  setFormDataValue: (key: string, value: any) => void;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  owners: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  rowData: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  auth: any;
  setOpenModal: (open: boolean) => void;
}

export const ProcessorSettings = ({
  disableOption,
  spreadsheetDocuments,
  formData,
  spreadsheetChange,
  sheetList,
  onChangeSheetName,
  extractionsList,
  extractionOptions,
  selectExtraction,
  setSelectExtraction,
  search,
  setSearch,
  companiesList,
  setFormData,
  setFormDataValue,
  owners,
  rowData,
  auth,
  setOpenModal,
}: ProcessorSettingsProps) => {
  const processorTemplateList = [
    {
      label: 'Document AI',
      name: 'documentAI',
      value: documentAICode,
    },
    {
      label: 'Extract Table',
      name: 'extractTable',
      value: extractTableCode,
    },
    {
      label: 'Spreadsheet',
      name: 'spreadsheet',
      value: spreadSheetCode,
    },
    {
      label: 'Nanonets',
      name: 'nanonets',
      value: nanonetsCode,
    },
    {
      label: 'HTML Extract',
      name: 'htmlExtract',
      value: htmlExtractCode,
    },
  ];

  const stabilityLevelLabel = useMemo(() => {
    if (!formData.stability_level) return '';
    return StabilityLevelsLabels[formData.stability_level];
  }, [formData.stability_level]);

  const stabilityLevelOptions = useMemo(
    () => [
      ...Object.values(StabilityLevels).map((key) => ({
        id: key,
        label: StabilityLevelsLabels[key],
      })),
    ],
    []
  );

  useEffect(() => {
    console.log('Reviewer str_id:', formData.reviewer_str_id);
  }, [formData]);

  const enhancedSelectorStyle = {
    minWidth: 125,
    width: 'auto',
    '& .MuiBox-root': {
      maxWidth: 'calc(100% - 24px)',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
    },
  };

  return (
    <Box className="h-15 flex mb-2 gap-1">
      {!disableOption.excelDocument && (
        <>
          <FilterSelect
            id="id-document"
            sx={{
              '& .MuiInputBase-root': {
                height: '35px',
              },
              '& .MuiOutlinedInput-input': {
                padding: '0 14px',
                height: '35px',
              },
              '& .MuiInputLabel-root': {
                transform: 'translate(14px, -6px) scale(0.75)',
                backgroundColor: 'white',
                paddingLeft: '4px',
                paddingRight: '4px',
              },
              '& .MuiInputLabel-shrink': {
                transform: 'translate(14px, -6px) scale(0.75)',
              },
            }}
            options={spreadsheetDocuments || []}
            fullWidth
            valueKey="str_id"
            value={formData.document}
            onChange={(data) => {
              spreadsheetChange(data?.data as IDocumentModel);
            }}
            label="Excel/CSV document"
            InputLabelProps={{ shrink: true }}
            getOptionLabel={(value) => {
              let docObj = value;
              if (typeof value === 'string') {
                docObj = spreadsheetDocuments?.find(
                  (item) => item.str_id === value
                );
              }
              return getFilenameFromPath(docObj?.label);
            }}
            renderOptionItem={(item) => (
              <Box
                display="flex"
                alignItems="left"
                flexDirection="column"
                justifyContent="space-between"
              >
                <div className="flex-1 mr-2">
                  {getFilenameFromPath(item?.label)}
                </div>
                <div className="mx-1 text-sm text-gray-400">
                  {`${item.status} - ${Formatter.date(item.created_at, true, 'MM/DD/YYYY hh:mm:A')}`}
                </div>
              </Box>
            )}
          />

          {sheetList?.length ? (
            <FormControl fullWidth>
              <EnhancedSelect
                label="Sheet name"
                sx={enhancedSelectorStyle}
                options={
                  sheetList?.map((item) => ({
                    id: item,
                    label: item,
                  })) || []
                }
                value={{
                  id: formData.sheetName || '',
                  label: formData.sheetName || '',
                }}
                onChange={(value) =>
                  onChangeSheetName({
                    target: {
                      value: value.id,
                    },
                  })
                }
              />
            </FormControl>
          ) : (
            ''
          )}
        </>
      )}
      {!disableOption.extractData && extractionsList && (
        <FormControl fullWidth>
          <EnhancedSelect
            enableSearch
            label="Extraction"
            sx={enhancedSelectorStyle}
            listContainerSx={{
              minWidth: 800,
            }}
            value={{
              id: selectExtraction || '',
              label:
                getFilenameFromPath(
                  extractionOptions.find((opt) => opt.id === selectExtraction)
                    ?.label?.documents?.file_path
                ) || '',
            }}
            onChange={(value) => setSelectExtraction(value.id)}
            onSearch={setSearch}
            searchKeyword={search}
            options={extractionOptions}
            renderLabel={({ key }) => {
              const item = extractionOptions.find((opt) => opt.id === key);
              if (!item?.label) return null;

              const filename = getFilenameFromPath(
                item?.label?.documents?.override_file_path ||
                  item?.label?.documents?.file_path
              );

              return (
                <MenuItem key={item.id} value={item.id}>
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                    sx={{
                      width: '100%',
                      minWidth: '500px',
                      p: 1,
                    }}
                  >
                    <div className="flex-1 mr-2 truncate">{filename}</div>
                    <div className="mx-1 text-sm text-gray-400 whitespace-nowrap">
                      {`${item?.label?.method} - ${Formatter.date(
                        item.label?.created_at,
                        true,
                        'MM/DD/YYYY hh:mm:A'
                      )}`}
                    </div>
                  </Box>
                </MenuItem>
              );
            }}
          />
        </FormControl>
      )}

      <FormControl fullWidth>
        <EnhancedSelect
          multiple
          enableSearch
          label="Companies"
          sx={enhancedSelectorStyle}
          options={
            companiesList?.data?.filter(Boolean).map((item) => ({
              id: item.str_id,
              label:
                item.access === AccessTypes.GLOBAL
                  ? `${item.company_name} (Fintary)`
                  : item.company_name,
              type: 'item',
            })) || []
          }
          value={
            formData.company_ids?.map((id) => {
              const company = companiesList?.data?.find(
                (item) => item.str_id === id
              );
              return {
                id,
                label:
                  company?.access === AccessTypes.GLOBAL
                    ? `${company.company_name} (Fintary)`
                    : company?.company_name || '',
                type: 'item',
              };
            }) || []
          }
          onChange={(values) => {
            const ids = values.map((v) => v.id);
            setFormData((prev) => ({
              ...prev,
              company_ids: ids,
              company_id: ids[0] || '',
            }));
          }}
        />
      </FormControl>

      <FormControl fullWidth>
        <EnhancedSelect
          label="Document type"
          sx={enhancedSelectorStyle}
          options={[
            { id: 'statement', label: 'Statement' },
            { id: 'report', label: 'Report' },
          ]}
          value={{
            id: formData.type || '',
            label:
              formData.type === 'statement'
                ? 'Statement'
                : formData.type === 'report'
                  ? 'Report'
                  : '',
          }}
          onChange={(value) => setFormDataValue('type', value.id)}
        />
      </FormControl>

      <FormControl fullWidth>
        <EnhancedSelect
          label="Extraction method"
          sx={enhancedSelectorStyle}
          options={processorTemplateList.map((item) => ({
            id: item.name,
            label: item.label,
          }))}
          value={{
            id: formData.method || '',
            label:
              processorTemplateList.find(
                (item) => item.name === formData.method
              )?.label || '',
          }}
          onChange={(value) => setFormDataValue('method', value.id)}
        />
      </FormControl>

      <TextField
        label="Name"
        sx={{
          '& .MuiInputBase-root': {
            height: '35px',
          },
          '& .MuiOutlinedInput-input': {
            padding: '0 14px',
            height: '35px',
          },
        }}
        fullWidth
        variant="outlined"
        value={formData.name}
        onChange={(e) => setFormDataValue('name', e.target.value)}
        InputLabelProps={{ shrink: true }}
      />

      <FormControl fullWidth>
        <EnhancedSelect
          label="Stability level"
          sx={enhancedSelectorStyle}
          options={stabilityLevelOptions}
          value={{
            id: formData.stability_level || '',
            label: stabilityLevelLabel || '',
          }}
          onChange={(value) => setFormDataValue('stability_level', value.id)}
        />
      </FormControl>

      <FormControl fullWidth sx={{ minWidth: 100, mb: 2 }}>
        <EnhancedSelect
          label="Owner"
          sx={enhancedSelectorStyle}
          listContainerSx={{
            p: '10px',
            minWidth: 500,
          }}
          options={(owners || []).map((c) => ({
            id: c.uid,
            label: Formatter.contact(c),
            email: c.email,
          }))}
          value={{
            id: formData.owner || '',
            label: formData.owner
              ? Formatter.contact(owners?.find((c) => c.uid === formData.owner))
              : '',
            email: formData.owner
              ? owners?.find((c) => c.uid === formData.owner)?.email || ''
              : '',
          }}
          renderLabel={({ key }) => {
            const owner = owners.find((c) => c.uid === key);
            if (!owner) return null;

            return (
              <Box
                sx={{
                  display: 'flex',
                  width: 480,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  gap: 1,
                }}
              >
                <Box sx={{ flex: 1 }}>{Formatter.contact(owner)}</Box>
                <Box sx={{ color: '#666' }}>{owner.email}</Box>
              </Box>
            );
          }}
          onChange={(value) => setFormDataValue('owner', value.id)}
        />
      </FormControl>

      <FormControl fullWidth>
        <EnhancedSelect
          label="Access"
          sx={enhancedSelectorStyle}
          options={[
            { id: 'account', label: 'Account' },
            { id: 'global', label: 'Global' },
          ]}
          value={{
            id: formData.access || '',
            label:
              formData.access === 'account'
                ? 'Account'
                : formData.access === AccessTypes.GLOBAL
                  ? 'Global'
                  : '',
          }}
          onChange={(value) => setFormDataValue('access', value.id)}
        />
      </FormControl>

      <FormControl fullWidth>
        <EnhancedSelect
          label="Status"
          sx={enhancedSelectorStyle}
          options={
            auth.currentUser?.uid === rowData?.reviewer_str_id
              ? [
                  {
                    id: ProcessorReviewStatuses.Draft,
                    label:
                      ProcessorReviewStatusesLabels[
                        ProcessorReviewStatuses.Draft
                      ],
                  },
                  {
                    id: ProcessorReviewStatuses.InReview,
                    label:
                      ProcessorReviewStatusesLabels[
                        ProcessorReviewStatuses.InReview
                      ],
                  },
                  {
                    id: ProcessorReviewStatuses.Approved,
                    label:
                      ProcessorReviewStatusesLabels[
                        ProcessorReviewStatuses.Approved
                      ],
                  },
                  {
                    id: ProcessorReviewStatuses.NeedsUpdate,
                    label:
                      ProcessorReviewStatusesLabels[
                        ProcessorReviewStatuses.NeedsUpdate
                      ],
                  },
                ]
              : [
                  {
                    id: ProcessorReviewStatuses.Approved,
                    label:
                      ProcessorReviewStatusesLabels[
                        ProcessorReviewStatuses.Approved
                      ],
                    disabled: true,
                  },
                  {
                    id: ProcessorReviewStatuses.NeedsUpdate,
                    label:
                      ProcessorReviewStatusesLabels[
                        ProcessorReviewStatuses.NeedsUpdate
                      ],
                    disabled: true,
                  },
                  {
                    id: ProcessorReviewStatuses.Draft,
                    label:
                      ProcessorReviewStatusesLabels[
                        ProcessorReviewStatuses.Draft
                      ],
                  },
                  {
                    id: ProcessorReviewStatuses.InReview,
                    label:
                      ProcessorReviewStatusesLabels[
                        ProcessorReviewStatuses.InReview
                      ],
                  },
                ]
          }
          value={{
            id: formData.status || '',
            label: formData.status
              ? formData.status.charAt(0).toUpperCase() +
                formData.status.replace(/_/g, ' ').slice(1)
              : '',
          }}
          onChange={(value) => {
            setFormDataValue('status', value.id);
            if (
              auth.currentUser?.uid !== rowData?.reviewer_str_id &&
              value.id === 'in_review'
            ) {
              setOpenModal(true);
            }
          }}
        />
      </FormControl>
    </Box>
  );
};
