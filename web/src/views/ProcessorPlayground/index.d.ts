export interface IProcessorType {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  access: string;
  company_id: string;
  document_str_id: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  extract_ids: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  extract_str_ids: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  extractionsid: any;
  name: string;
  inner_name: string;
  method: ProcessorMethod;
  notes: string;
  processor: string;
  reviewed_at: string;
  reviewed_by: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  reviewer_id: any;
  reviewer_str_id: string;
  status: string;
  type: string;
  processor_status: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  file_type: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  extractions: any;
  companies: ICompanies;
  owner: string;
  suggest_for: string;
  profile_str_id: string;
  description?: string;
}

export interface IProcessorModalProps {
  open: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  handleCancel: (e: any) => void;
  rowData?: IProcessorType | null; // For edit
  isReportProcessor?: boolean;
}

export interface IProcessorPlaygroundProps {
  rowData?: IProcessorType | null; // For edit
  setShowSavingMsg: (show: boolean) => void;
  documentList: {
    count: number;
    data: IDocumentModel[];
  };
  isReportProcessor?: boolean;
}

export interface IExtractionData {
  // biome-ignore lint/suspicious/noShadowRestrictedNames: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  flat(Infinity: number): unknown;
  id: number;
  uid: string;
  state: string;
  created_at: string;
  updated_at: string;
  document_id: number;
  method: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  method_options: any;
  output: string;
  output_format: string;
  result: string;
  result_id: string;
  str_id: string;
  status: string;
  account_id: string;
  documents: IDocumentModel;
}

interface ICompanies {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  created_at: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  created_by: any;
  updated_at: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updated_by: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  address: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  company_id: any;
  company_name: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  email: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  group_id: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  notes: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  phone: any;
  type: string[];
  website: string;
}

interface IUsersDocumentsCreatedByTousers {
  id: number;
  uid: string;
  company: string;
  state: string;
  email: string;
  first_name: string;
  last_name: string;
  mode: string;
  phone: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  plaid_access_token: any;
  created_at: string;
  updated_at: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  created_by: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updated_by: any;
  str_id: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  reconciliation_view: any;
}

interface IUsersDocumentsUpdatedByTousers {
  id: number;
  uid: string;
  company: string;
  state: string;
  email: string;
  first_name: string;
  last_name: string;
  mode: string;
  phone: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  plaid_access_token: any;
  created_at: string;
  updated_at: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  created_by: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updated_by: any;
  str_id: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  reconciliation_view: any;
}

interface IDocumentModel {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by: string;
  company_str_id: string;
  file_type: string;
  filename: string;
  file_path: string;
  file_hash: string;
  mapping: string;
  method: string;
  override_file_hash: string;
  override_file_path: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  override_mapping: any;
  processor: string;
  status: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  tag: any;
  type: string;
  companies: ICompanies;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  extractions: any[];
  created_by_user: IUsersDocumentsCreatedByTousers;
  updated_by_user: IUsersDocumentsUpdatedByTousers;
  label?: string;
  value?: string;
}

export interface MessageModel {
  user_str_id: string;
  message: string;
  processor_str_id: string;
  str_id: string;
  created_at: string;
  id: number;
}

export interface IResultProps {
  fields: string[];
  data: string[][];
  version: string;
  sheet?: string;
}

export enum ProcessorMethod {
  extractTable = 'extractTable',
  documentAI = 'documentAI',
  spreadsheet = 'spreadsheet',
}
