{"compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "esnext"], "downlevelIteration": false, "allowJs": true, "allowSyntheticDefaultImports": true, "checkJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "node", "noEmit": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "baseUrl": "./src", "types": ["vite/client", "vite-plugin-svgr/client", "vitest/globals"], "paths": {"@/*": ["*"], "components": ["components/*"], "contexts": ["contexts/*"], "data": ["data/*"], "illustrations": ["illustrations/*"], "services": ["services/*"], "view": ["view/*"], "common/*": ["../common/*"], "class-transformer": ["./node_modules/class-transformer"], "class-validator": ["./node_modules/class-validator"]}}, "include": ["src", "custom.d.ts", "node_modules/common", "../common", "vite.config.ts"], "exclude": ["node_modules", "**/node_modules/*"]}