import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react-swc';
import { resolve } from 'node:path';
import tsconfigPaths from 'vite-tsconfig-paths';
import svgr from 'vite-plugin-svgr';
import addVersion from './plugins/addVersion';
import copyPdfWorker from './plugins/copyPdfWorker';
import externalNonceInjection from './plugins/externalNonce';
console.log('🔧 Vite config loaded');

export default defineConfig({
  publicDir: './public',
  plugins: [
    react(),
    svgr({
      svgrOptions: {
        ref: true,
        svgo: false,
        titleProp: true,
      },
      include: '**/*.svg',
    }),
    tsconfigPaths(),
    externalNonceInjection(),
    copyPdfWorker(),
    addVersion(),
  ],
  resolve: {
    ...(process.env.NODE_ENV === 'test'
      ? {}
      : {
          extensions: [
            '.browser.ts',
            '.mjs',
            '.js',
            '.mts',
            '.ts',
            '.jsx',
            '.tsx',
            '.json',
          ],
        }),
    alias: [{ find: '@/', replacement: resolve('.', 'src') }],
  },
  build: {
    modulePreload: true,
    sourcemap: process.env.ENVIRONMENT === 'production',
    outDir: 'build',
  },
  esbuild: {
    jsxFactory: 'React.createElement',
    jsxFragment: 'React.Fragment',
  },
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
    },
  },
  define: {
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
  },
  test: {
    globals: true,
    root: './src/',
    environment: 'jsdom',
    globalSetup: 'setupTestsGlobal.ts',
    setupFiles: './vitest.setup.tsx',
    coverage: {
      reportsDirectory: 'coverage',
      reporter: 'text-summary',
      provider: 'v8',
    },
    silent: process.env.QUIET_TESTS === 'true',
    outputFile: `test-results/web-${process.env.SHARD ?? 0}.xml`,
    reporters: ['junit', 'default'],
    testTimeout: 10000,
  },
});
